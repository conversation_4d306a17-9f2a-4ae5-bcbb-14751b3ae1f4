# UserPluginClient 使用说明

`UserPluginClient` 提供了两个主要方法来管理用户拥有的插件：

## 方法说明

### 1. DownloadMyOwnedPluginsAsync

从云端下载用户拥有的插件信息并保存到本地。

```csharp
Task DownloadMyOwnedPluginsAsync(string machineSerialNumber, CancellationToken cancellationToken)
```

**参数：**
- `machineSerialNumber`: 机器序列号，用于标识插件所有权
- `cancellationToken`: 取消令牌

**功能：**
- 调用云端 API `/api/v1/user-plugins/me?machineSerialNumber={machineSerialNumber}`
- 使用当前登录用户的访问令牌进行身份验证
- 将响应数据保存到 `AppFolder.PluginDb` 目录下的 `plugin-owned.json` 文件中

### 2. GetMyOwnedPluginsAsync

从本地文件读取用户拥有的插件信息，并验证数据的有效性。

```csharp
Task<string[]> GetMyOwnedPluginsAsync(string machineSerialNumber, CancellationToken cancellationToken)
```

**参数：**
- `machineSerialNumber`: 机器序列号，用于验证数据匹配性
- `cancellationToken`: 取消令牌

**返回值：**
- `string[]`: 用户拥有的有效插件代码数组

**验证逻辑：**
1. 检查本地文件是否存在
2. 验证当前登录用户ID是否与文件中的用户ID匹配
3. 验证当前机器序列号是否与文件中的序列号匹配
4. 过滤掉已过期的插件
5. 返回有效的插件代码数组

## 使用示例

```csharp
public class PluginService
{
    private readonly IUserPluginClient _userPluginClient;
    private readonly IPcSerialNumber _pcSerialNumber;

    public PluginService(IUserPluginClient userPluginClient, IPcSerialNumber pcSerialNumber)
    {
        _userPluginClient = userPluginClient;
        _pcSerialNumber = pcSerialNumber;
    }

    /// <summary>
    /// 刷新用户拥有的插件信息
    /// </summary>
    public async Task RefreshUserPluginsAsync(CancellationToken cancellationToken = default)
    {
        var serialNumber = _pcSerialNumber.GetSerialNumber();
        await _userPluginClient.DownloadMyOwnedPluginsAsync(serialNumber, cancellationToken);
    }

    /// <summary>
    /// 获取用户拥有的插件列表
    /// </summary>
    public async Task<string[]> GetOwnedPluginsAsync(CancellationToken cancellationToken = default)
    {
        var serialNumber = _pcSerialNumber.GetSerialNumber();
        return await _userPluginClient.GetMyOwnedPluginsAsync(serialNumber, cancellationToken);
    }

    /// <summary>
    /// 检查用户是否拥有指定插件
    /// </summary>
    public async Task<bool> HasPluginAsync(string pluginCode, CancellationToken cancellationToken = default)
    {
        var ownedPlugins = await GetOwnedPluginsAsync(cancellationToken);
        return ownedPlugins.Contains(pluginCode);
    }
}
```

## 数据结构

### UserOwnedPluginsResponse

```csharp
public class UserOwnedPluginsResponse
{
    public UserOwnedPluginsData Data { get; set; }
    public string Signature { get; set; }
    public string SignCertificatePublicKey { get; set; }
    public string Algorithm { get; set; }
    public string Nonce { get; set; }
    public DateTime SignatureTimestamp { get; set; }
    public string DataHash { get; set; }
}
```

### UserOwnedPluginsData

```csharp
public class UserOwnedPluginsData
{
    public string MachineSerialNumber { get; set; }
    public string UserId { get; set; }
    public string UserName { get; set; }
    public UserOwnedPlugin[] Plugins { get; set; }
    public DateTime QueryTimestamp { get; set; }
}
```

### UserOwnedPlugin

```csharp
public class UserOwnedPlugin
{
    public string PluginCode { get; set; }
    public DateTime ValidUntil { get; set; }
}
```

## 错误处理

- **网络错误**: 当调用云端API失败时，会抛出 `ApiException`
- **文件不存在**: `GetMyOwnedPluginsAsync` 会返回空数组
- **数据验证失败**: 当用户ID或机器序列号不匹配时，会返回空数组
- **JSON解析错误**: 当本地文件格式错误时，会返回空数组

## 注意事项

1. **登录状态**: 调用 `DownloadMyOwnedPluginsAsync` 前确保用户已登录
2. **网络连接**: 下载操作需要网络连接
3. **文件权限**: 确保应用有权限写入 `AppFolder.PluginDb` 目录
4. **数据安全**: 本地文件包含数字签名信息，用于验证数据完整性
5. **过期处理**: 系统会自动过滤已过期的插件

## 依赖注入配置

确保在DI容器中注册相关服务：

```csharp
services.AddScoped<IUserPluginClient, UserPluginClient>();
services.AddScoped<ILoginSession, LoginSession>();
services.AddScoped<IAppFolderService, AppFolderService>();
services.AddScoped<IPcSerialNumber, PcSerialNumber>();
```
