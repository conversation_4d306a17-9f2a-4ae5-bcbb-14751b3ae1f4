# CD_Bootloader有线更新方案



## 方案说明

由于STM32需要分包传输、写入，故更新时需要分为以下几部分：

**更新数据框架**

| 存储区域              | 功能                               | 大小     |
| --------------------- | ---------------------------------- | -------- |
| 传输状态存储区（RAM） | 暂存更新包大小、分包数、已更新包号 | 64字节   |
| 数据包缓冲区（RAM）   | 存储由主站发送的数据包             | 4096字节 |
| 固件存储区（FLASH）   | 存储收到的固件                     | 64KBytes |
**更新数据的流程**

![bootloader更新流程.drawio](bootloader通信框架.assets/bootloader更新流程.drawio.png)

通信协议需要包含站号、数据类型、数据长度、数据内容，包头包尾。

支持的功能目前为读取设备存储更新状态空间内的数据、读取设备当前状态、写入固件数据包，后续克视情况增加功能。



## 通信协议规格制定

通信协议使用标准串口进行通信，单包通信内容长度不超过2060字节，通信结尾对电文进行CRC16-Modbus进行校验，通信数据帧内容如下：

|      | 数据头    | 数据长度 | 从站号                | 功能码                                                       | 页码（固件需要） | 固件内容  | CRC16-Modbus  | 数据尾    |
| ---- | --------- | -------- | --------------------- | ------------------------------------------------------------ | ---------------- | --------- | ------------- | --------- |
| 内容 | 0xAA 0xBB | 0~2048   | 0~254<br />0xFF为广播 | 0：固件<br />1：更新信息<br />2：写FLASH<br />3：启动固件<br />4：写入硬件号<br />5：写入软件号 | 0~255            | payload   | 仅校验payload | 0xBB 0xAA |
| 长度 | 2bytes    | 2bytes   | 1bytes                | 1bytes                                                       | 1bytes           | 2048bytes | 2bytes        | 2bytes    |



**通信使用的上位机已按照上述协议完成编写**



## 通信逻辑

通信逻辑按照方案说明中描述实行

![bootloader更新流程.drawio](bootloader通信框架.assets/bootloader更新流程.drawio.png)

## 嵌入式端进行通信设计

### bootloader跳转函数

用于从bootloader跳转到APP区域【基础函数，完成FLASH运行空间跳转】



用于从APP区跳转到bootloader区域【适度添加逻辑】

在参数存储区中使用一个长度为一个word的空间，命名为app_jump_status

该变量为0，则说明已经成功进入过app
该变量为1，则说明未成功进入过app

在使用命令使设备从bootloader进入app前，对app_jump_status置1，并存储参数后跳转。

在每一个app中均需要在启动时检测app_jump_status是否为1，若为1，则将app_jump_status置为0，否则不予理会。

在设备启动时因正常通信超时跳转到APP前，会检查app_jump_status状态，若该状态为0，则跳转到app，若该状态为1，则代码进入到bootloader模式。

上述逻辑是用于解决后续进行更新时，若更新失败导致设备无法正确进入app，则设备重启后会继续跳入app或硬件错误的问题。



### 串口接收逻辑

为了通信接收逻辑稳固，使用串口空闲中断+DMA的方式实现串口接收逻辑；同时支持USB虚拟串口进行通信工作（预计需要10K的RAM缓存空间）。



### 串口信息处理逻辑

#### **设备接收到串口信息后，需要进行校验**

1、包头包尾校验，确认包头为0xAA 0xBB包尾为0xBB 0xAA；

2、数据长度校验，接收数据内长度为整个电文包的长度-11字节

3、站号校验，确认电文对应站号与本设备是否一致【可不用】

4、CRC校验，确认Payload的CRC校验码与电文一致





#### **功能区分**

确认电文有效后根据功能码进行区分

**针对0x00：固件帧**

申请缓冲区【4KB】，用于暂存固件；

将固件包payload存入缓冲区中。【固件分页放入缓冲区，crc为校验单页固件】

**示例：**

```
主站发送：【该电文不回复，状态通过功能码0x01读取】
AA BB 08 00 FB 00 00 XXXXXXXXXXXXXPAYLOADXXXXXXXXXXXXX A4 45 BB AA 
AA BB：数据头
0X800：数据包长度2048
FB：目标设备地址251（从站号）
00：功能码【固件帧】
00：固件页码
XXXXPAYLOADXXXX：固件内容【长度2048】
0XA445：固件的CRC16校验码
BB AA：数据尾
发送该电文后，bootloader会将固件缓存在一个大小为2K的缓冲区中，该操作不会写入flash。
```





**针对0x01：读取更新信息**

返回以下数据

当前缓冲区存储的固件页码

上次写入缓冲区的固件页码

上次写入缓冲区是否成功【0x00成功     0x01写入页码不匹配    0x02 FLASH忙】

写入成功页数【使用4字节表示，0x00 0x00 0x00 0x00 右侧为最小页0，左侧为最大页31，写入成功则对应页的位置1，未写入或未写入成功对应页码的位为0】

**示例：**            

```
主站发送：
AA BB 00 01 FB 01 00 00 4E 08 BB AA 
AA BB：数据头
0x0001：数据长度1
FB:目标设备地址251（从站号）
01:功能码【读取更新信息】
00:闲置【不参与CRC校验】
00:消息内容占位
4E 08：CRC16校验
BB AA：数据尾

*****************************************************************************************

从站返回：（目标地址为FF时不会返回）
AA BB 00 10 FB 01 00 02 01 00 01 00 00 00 03 12 34 56 78 87 65 43 21 D5 5E BB AA
AA BB：数据头
0x0008：数据长度8
FB:目标设备地址251（从站号）
01:功能码【读取更新信息】
00:闲置【不参与CRC校验】
02:当前缓冲页装载的固件页码
01:上一次写入的固件页码
00:上一次写入flash的写入情况（00：成功  01：页码不匹配失败  02：flash忙失败）
01:本次boot模式下已写入flash的历史最大页码
00000003:使用二进制表示为0000 0000 0000 0000 0000 0000 0000 0011，表示当前已写入的页面为0页和1页，其他页面未写入；
0x12345678:硬件号
0x87654321:软件号
D5 5E：前面16个字节的CRC16校验
BB AA：数据尾

设备启动时可通过该电文阻止设备进入APP；
该电文也可用于检测设备是否进入了APP，若能正常反馈数据则设备未进入APP，不会返回则设备已进入APP或设备异常。
```



**针对0x02：写入FLASH**

判定当前缓冲区固件所在页码与FLASH写入命令页码是否一致，一致则将该页数据写入FLASH，否则修改缓冲区写入状态为失败故障码0x01；

若写入失败，则反馈FLASH忙故障码0x02；

写入成功则写入页数位置1；

**示例：**

```
主站发送：【该电文不回复，状态通过功能码0x01读取】
AA BB 00 01 FB 02 02 00 4E 08 BB AA
AA BB：数据头
0x0001：数据长度1
FB:目标设备地址251（从站号）
02:功能码【写入flash功能】
02:需要写入的页码【需要与缓冲区固件匹配，否则不会写入】
00:消息内容占位
4E 08：CRC16校验
BB AA：数据尾

发送该电文后缓冲区数据会写入指定页码。
```



**针对0x03：跳转到APP**

判定接收到的最大页码以前的所有页码是否均为1或者所有页码均为0，两个条件均满足则进行跳转，否则不响应。

示例：

```
主站发送：【该电文不回复】
AA BB 00 01 FB 03 00 00 4E 08 BB AA 
AA BB：数据头
0x0001：数据长度1
FB:目标设备地址251（从站号）
03:功能码【跳转到APP】
00:闲置【不参与CRC校验】
00:消息内容占位
4E 08：CRC16校验
BB AA：数据尾

发送该电文后设备会自动检测APP区域是否有可运行固件头存在，不存在则会留在bootloader，存在则会跳转到APP区运行
```



**针对0x04：写入硬件号**

设备接收到硬件号，硬件号写入到FLASH中

示例：

```c
主站发送：
AA BB 00 04 FB 04 00 12 34 56 78 DF 8A BB AA
AA BB：数据头
0x0004：数据长度4
FB:目标设备地址251（从站号）
04:功能码【写入硬件号】
00:闲置【不参与CRC校验】
0x12345678：硬件号
DF 8A：CRC16校验
BB AA：数据尾
    
从站回复：
AA BB 00 04 FB 04 00 12 34 56 78 DF 8A BB AA
AA BB：数据头
0x0004：数据长度4
FB:目标设备地址251（从站号）
04:功能码【写入硬件号】
00:闲置【不参与CRC校验】
0x12345678：硬件号
DF 8A：CRC16校验
BB AA：数据尾
```



**针对0x05：写入软件号及固件页码**

设备接收到软件号，软件号在跳入APP前写入到FLASH中

示例：

```c
主站发送：
AA BB 00 04 FB 05 00 12 34 56 78 40 DF 8A BB AA
AA BB：数据头
0x0004：数据长度4
FB:目标设备地址251（从站号）
05:功能码【写入软件号】
00:闲置【不参与CRC校验及固件页】
0x12345678：软件号
0x40：固件总页数
DF 8A：CRC16校验
BB AA：数据尾
    
从站回复：
AA BB 00 04 FB 05 00 12 34 56 78 40 DF 8A BB AA【成功则回复原始电文，不成功则回复当前软件号】
AA BB：数据头
0x0004：数据长度4
FB:目标设备地址251（从站号）
05:功能码【写入软件号及固件页】
00:闲置【不参与CRC校验】
0x12345678：软件号
40：固件总页数
DF 8A：CRC16校验
BB AA：数据尾
```





**针对0x06：写入密钥**

设备接收到密钥，写入密钥到FLASH中

密钥前后需增加前8字节，后8字节固定内容

密钥前增加的8字节字符串“CFKJ_Hel”

密钥后增加的8字节字符串“lo_world”

示例中key...实际为【（CFKJ_Hel）（需要设定的密钥）（lo_world）】

传输该密钥时需要使用固定AES-CBC进行加密

固定AES-CBC密钥【9527YXLMWZRYHPJY】（字符串ASCII）
固定AES-CBC向量【YYS_MHXYDZPD9527】（字符串ASCII）

示例：

```C
主站发送：
AA BB 00 20 FB 06 00 key... DF 8A BB AA
AA BB：数据头
0x0004：数据长度4
FB:目标设备地址251（从站号）
06:功能码【写入密钥】
00:闲置【不参与CRC校验】
key...：密钥【16字节】
DF 8A：CRC16校验
BB AA：数据尾
    
主站发送：
AA BB 00 20 FB 06 00 key... DF 8A BB AA【回复源数据】
AA BB：数据头
0x0004：数据长度4
FB:目标设备地址251（从站号）
06:功能码【反馈密钥】
00:闲置【不参与CRC校验】
key...：密钥【16字节】
DF 8A：CRC16校验
BB AA：数据尾
```



**针对0x07：写入向量**

设备接收到向量，写入向量到FLASH中

示例：

```C
主站发送：
AA BB 00 20 FB 07 00 vi... DF 8A BB AA
AA BB：数据头
0x0004：数据长度4
FB:目标设备地址251（从站号）
07:功能码【写入向量】
00:闲置【不参与CRC校验】
vi...：向量【16字节】
DF 8A：CRC16校验
BB AA：数据尾
    
主站发送：
AA BB 00 20 FB 07 00 vi... DF 8A BB AA【回复源数据】
AA BB：数据头
0x0004：数据长度4
FB:目标设备地址251（从站号）
07:功能码【反馈向量】
00:闲置【不参与CRC校验】
vi...：向量【16字节】
DF 8A：CRC16校验
BB AA：数据尾
```

