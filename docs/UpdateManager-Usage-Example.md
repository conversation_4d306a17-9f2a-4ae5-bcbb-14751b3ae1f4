# UpdateManager 使用示例

## 概述

`IUpdateManager` 接口提供了完整的软件更新管理功能，包括下载更新、检查待安装版本和安装更新。

## 主要方法

### 1. DownloadUpdatesAsync - 下载更新

检查并下载最新版本的软件更新。

```csharp
// 下载更新
await updateManager.DownloadUpdatesAsync(cancellationToken);
```

### 2. GetUpdatePendingVersionAsync - 获取待安装版本

检查是否有等待安装的更新版本，如果有则返回版本号，否则返回 null。

```csharp
// 检查是否有待安装的更新
var pendingVersion = await updateManager.GetUpdatePendingVersionAsync(cancellationToken);

if (pendingVersion != null)
{
    // 提示用户有新版本可用
    MessageBox.Show($"发现新版本 {pendingVersion}，是否立即安装？", 
                    "软件更新", 
                    MessageBoxButton.YesNo);
}
else
{
    // 没有待安装的更新
    Console.WriteLine("当前已是最新版本");
}
```

### 3. InstallUpdatesAsync - 安装更新

安装已下载的更新。

```csharp
// 安装更新
await updateManager.InstallUpdatesAsync(cancellationToken);
```

## 完整使用示例

```csharp
public class UpdateService
{
    private readonly IUpdateManager _updateManager;
    private readonly ILogger _logger;

    public UpdateService(IUpdateManager updateManager, ILogger logger)
    {
        _updateManager = updateManager;
        _logger = logger;
    }

    /// <summary>
    /// 检查并处理软件更新
    /// </summary>
    public async Task CheckAndHandleUpdatesAsync()
    {
        try
        {
            // 1. 下载最新更新
            _logger.Information("正在检查软件更新...");
            await _updateManager.DownloadUpdatesAsync();

            // 2. 检查是否有待安装的版本
            var pendingVersion = await _updateManager.GetUpdatePendingVersionAsync();
            
            if (pendingVersion != null)
            {
                _logger.Information("发现新版本: {Version}", pendingVersion);
                
                // 3. 询问用户是否安装
                var result = MessageBox.Show(
                    $"发现新版本 {pendingVersion}，是否立即安装？\n\n" +
                    "安装过程中应用程序将会重启。",
                    "软件更新",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // 4. 安装更新
                    _logger.Information("开始安装更新版本: {Version}", pendingVersion);
                    await _updateManager.InstallUpdatesAsync();
                    
                    // 注意：InstallUpdatesAsync 会启动更新程序并退出当前应用
                    // 所以这里的代码可能不会执行到
                }
            }
            else
            {
                _logger.Information("当前已是最新版本");
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "处理软件更新时发生错误");
        }
    }

    /// <summary>
    /// 应用启动时检查更新
    /// </summary>
    public async Task CheckForUpdatesOnStartupAsync()
    {
        try
        {
            // 检查是否有待安装的更新（不下载新的）
            var pendingVersion = await _updateManager.GetUpdatePendingVersionAsync();
            
            if (pendingVersion != null)
            {
                var result = MessageBox.Show(
                    $"检测到已下载的更新版本 {pendingVersion}，是否立即安装？",
                    "软件更新",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Information);

                if (result == MessageBoxResult.Yes)
                {
                    await _updateManager.InstallUpdatesAsync();
                }
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "启动时检查更新失败");
        }
    }
}
```

## 依赖注入配置

在 `InfrastructureComponent.cs` 中已经注册了 `UpdateManager`：

```csharp
containerRegistry.Register<IUpdateManager, UpdateManager>();
```

## 注意事项

1. **权限要求**: 安装更新需要管理员权限，`InstallUpdatesAsync` 会以 "runas" 方式启动更新程序。

2. **应用退出**: 调用 `InstallUpdatesAsync` 后，当前应用程序应该准备退出，因为更新程序会替换当前的可执行文件。

3. **版本比较**: 只有当下载的版本比当前版本新时，`GetUpdatePendingVersionAsync` 才会返回版本号。

4. **错误处理**: 所有方法都包含完整的错误处理和日志记录。

5. **取消支持**: 所有异步方法都支持 `CancellationToken` 用于取消操作。

## 软件标识

UpdateManager 使用 "dgwin" 作为软件代码来从云端获取更新。这个标识符在 `IArtifactClient` 中用于识别特定的软件包。
