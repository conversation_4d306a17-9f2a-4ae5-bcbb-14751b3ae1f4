# 证书验证系统使用指南

## 概述

本文档介绍如何在 DeviceGuard 项目中使用证书验证系统。该系统基于依赖注入模式设计，提供了完整的 RSA 数字签名验证功能。

## 核心组件

### 1. ICertificateValidator
证书验证器接口，提供证书链验证功能。

### 2. CertificateValidator
证书验证器的具体实现，使用硬编码的 CFTech 根证书进行验证。

### 3. SignatureVerifier
签名验证器，专门用于验证服务端返回的 JSON 签名数据。

### 4. CertificateValidationExample
证书验证使用示例服务，提供高级验证方法。

## 依赖注入配置

系统已自动注册到依赖注入容器中：

```csharp
// 在 SecurityComponent 中的配置
public void RegisterTypes(IContainerRegistry container)
{
    // 注册证书验证器
    container.RegisterSingleton<ICertificateValidator, CertificateValidator>();
    
    // 注册签名验证器
    container.RegisterSingleton<SignatureVerifier>();
    
    // 注册证书验证示例服务
    container.RegisterSingleton<CertificateValidationExample>();
}
```

## 使用示例

### 基本用法

```csharp
public class MyService
{
    private readonly ICertificateValidator _certificateValidator;
    private readonly SignatureVerifier _signatureVerifier;
    private readonly CertificateValidationExample _validationExample;
    private readonly ILogger _logger;

    public MyService(
        ICertificateValidator certificateValidator,
        SignatureVerifier signatureVerifier,
        CertificateValidationExample validationExample,
        ILogger logger)
    {
        _certificateValidator = certificateValidator;
        _signatureVerifier = signatureVerifier;
        _validationExample = validationExample;
        _logger = logger;
    }

    public async Task ValidateServerResponseAsync()
    {
        // 1. 获取根证书信息
        var rootCertInfo = _certificateValidator.GetRootCertificateInfo();
        _logger.Information("根证书信息: {Subject}", rootCertInfo.Subject);

        // 2. 验证服务端签名数据
        var serverResponse = GetServerResponse(); // 获取服务端响应
        var result = await _signatureVerifier.VerifySignatureAsync(serverResponse);
        
        if (result.IsValid)
        {
            _logger.Information("✅ 签名验证成功");
            // 处理验证成功的逻辑
        }
        else
        {
            _logger.Error("❌ 签名验证失败: {Error}", result.ErrorMessage);
            // 处理验证失败的逻辑
        }
    }
}
```

### 高级用法

```csharp
public class AdvancedValidationService
{
    private readonly CertificateValidationExample _validationExample;
    private readonly ICertificateValidator _certificateValidator;
    private readonly SignatureVerifier _signatureVerifier;

    public AdvancedValidationService(
        CertificateValidationExample validationExample,
        ICertificateValidator certificateValidator,
        SignatureVerifier signatureVerifier)
    {
        _validationExample = validationExample;
        _certificateValidator = certificateValidator;
        _signatureVerifier = signatureVerifier;
    }

    public async Task<bool> ValidateCompleteWorkflowAsync(string serverResponseJson)
    {
        // 使用示例服务进行完整的验证流程
        return await _validationExample.ValidateServerSignatureAsync(
            _certificateValidator,
            _signatureVerifier,
            serverResponseJson);
    }

    public async Task<bool> ValidateSingleCertificateAsync(string certificatePem)
    {
        // 验证单个证书
        return await _validationExample.ValidateSingleCertificateAsync(
            _certificateValidator,
            certificatePem);
    }

    public async Task<CertificateValidationResult> GetDetailedValidationAsync(string certificatePem)
    {
        // 获取详细的验证结果
        return await _validationExample.GetDetailedValidationResultAsync(
            _certificateValidator,
            certificatePem);
    }
}
```

## 服务端 JSON 格式

系统支持验证以下格式的服务端签名数据：

```json
{
  "data": {
    "MachineSerialNumber": "11",
    "UserId": "f141ee3c-b917-4559-95eb-cde8baea6bae",
    "UserName": "13577137251",
    "Plugins": [],
    "QueryTimestamp": "2025-06-26T23:55:31.1128013+08:00"
  },
  "signature": "O5uSn2SJ11TT7hXu8c7gtkKaSqWs/w7q75EXWVdkQJkDZIvgLR2BNMuZVsA0OenCtzo3/7+Kd8Yq6q8FSkETrUJKeRnE/BDSiKBG6MzD5oDvVPDiurAPC1FQMUvWd/R7H2mrwyCvmEnGnlMijQJ3fC+r/2kQoty3V69k1zqrX7NnFGdfUQAE6p/NkbvfnCQFpUFHEAnpkJJizl9Ko1rgU99bzGJrqm0apRFCgSyAVBdMu8AdK58CjuNG//VdlBGtogVzaBDNunJA0GfhuxeHcyHOrooGWi17Yz9Gswwil8Ij6NQYKazObdRSIH+sQXlf2YjpXqqgtaG06A+B6IM1lQ==",
  "signCertificatePublicKey": "-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----",
  "algorithm": "SHA256withRSA",
  "nonce": "Ph3XPxAlD85YqVmnuAHA8+Oilr1/TOg8wwUXnDqYrPA=",
  "signatureTimestamp": "2025-06-26T15:55:31.1245568Z",
  "dataHash": "eh+pKAI6U0MNT1PyBCFObBGI3GX94/2vfrRkhLuhtlM="
}
```

## 错误处理

```csharp
public async Task<bool> SafeValidationAsync(string serverResponse)
{
    try
    {
        var result = await _signatureVerifier.VerifySignatureAsync(serverResponse);
        return result.IsValid;
    }
    catch (ArgumentNullException ex)
    {
        _logger.Error("参数为空: {Error}", ex.Message);
        return false;
    }
    catch (JsonException ex)
    {
        _logger.Error("JSON 解析失败: {Error}", ex.Message);
        return false;
    }
    catch (Exception ex)
    {
        _logger.Error(ex, "验证过程中发生未知错误");
        return false;
    }
}
```

## 最佳实践

### 1. 依赖注入
始终通过构造函数注入获取服务实例，不要直接实例化。

```csharp
// ✅ 正确的方式
public class MyService
{
    public MyService(ICertificateValidator validator) { ... }
}

// ❌ 错误的方式
public class MyService
{
    public void SomeMethod()
    {
        var validator = new CertificateValidator(); // 不要这样做
    }
}
```

### 2. 异步操作
所有验证操作都是异步的，确保正确使用 async/await。

```csharp
// ✅ 正确的异步调用
var result = await _signatureVerifier.VerifySignatureAsync(data);

// ❌ 错误的同步调用
var result = _signatureVerifier.VerifySignatureAsync(data).Result; // 可能导致死锁
```

### 3. 错误处理
始终检查验证结果并适当处理错误。

```csharp
var result = await _signatureVerifier.VerifySignatureAsync(data);
if (result.IsValid)
{
    // 处理成功情况
}
else
{
    // 记录错误并处理失败情况
    _logger.Error("验证失败: {Error}", result.ErrorMessage);
}
```

### 4. 日志记录
利用注入的日志记录器记录重要的验证事件。

```csharp
_logger.Information("开始验证签名数据");
var result = await _signatureVerifier.VerifySignatureAsync(data);
_logger.Information("验证完成，结果: {IsValid}", result.IsValid);
```

## 单元测试

参考 `tests/DeviceGuard.Tests/Security/CertificateValidationTests.cs` 了解如何为证书验证功能编写单元测试。

关键点：
- 使用 Mock 对象模拟依赖项
- 测试各种边界情况和错误条件
- 验证日志记录器的正确注入
- 测试异步操作的正确性

## 安全注意事项

1. **根证书保护**: 根证书数据已硬编码到程序中，防止外部篡改
2. **指纹验证**: 系统会双重验证根证书指纹，确保证书完整性
3. **证书链验证**: 完整的 X.509 证书链验证
4. **有效期检查**: 自动检查证书和根证书的有效期
5. **签名算法**: 使用 RSA-SHA256 数字签名算法

## 故障排除

### 常见问题

1. **"根证书指纹验证失败"**
   - 检查根证书数据是否被篡改
   - 确认 CFTechRootCertificate 类中的指纹值正确

2. **"证书链验证失败"**
   - 检查证书是否由 CFTech 根证书颁发
   - 确认证书未过期

3. **"签名验证失败"**
   - 检查 JSON 格式是否正确
   - 确认签名数据完整性
   - 验证签名算法是否为 SHA256withRSA

4. **依赖注入失败**
   - 确认 SecurityComponent 已正确注册
   - 检查构造函数参数类型是否正确
