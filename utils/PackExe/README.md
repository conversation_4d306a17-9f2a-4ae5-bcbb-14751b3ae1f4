# Summary

该项目旨在为发布完成的主程序 DeviceGuard.exe 以及所有依赖的库进行打包。

## Inno Setup

打包使用 InnoSetup.exe 软件完成, 脚本为 setup.iss 文件, 目前我们使用的软件版本为 6.4.3 
请在打包前安装 `https://jrsoftware.org/isdl.php`.

setup.iss 接收三个参数

- **InputDir** 输入目录, 整个软件的目录
- **OutputDir** 输出目录, 输出的安装包目录
- **Version** 软件版本

打包完毕, 将在输出目录生成 `device-guard-v{Version}.exe` 安装包

## 打包集成

我们将在项目 DeviceGuard 发布的时候, 增加发布后事件, 调用打包软件实现打包过程

```xml
<Target Name="AfterBuild">
    <Exec Command="PackExe.exe "\Program Files (x86)\Inno Setup 6\ISCC.exe" $(InputDir) $(OutputDir) $(Version)" />
</Target>
```

# How to Build PackExe.exe

完成编译调试后，您需要将 `PackExe.exe` 发布到 `utils` 目录下。执行以下步骤进行发布：

1. **构建 PackExe.exe**：
    - 打开项目并完成编译。
    - 在生成的输出文件夹中找到 `PackExe.exe`。
2. **发布 PackExe.exe**：
    - 执行 PackExe 项目的发布命令 `Publish PackExe to utils`。项目将自动编译, 打包和发布至目标位置
3. **签入编译的exe**：
    - 将编译后的 `PackExe.exe` 签入到源代码管理中。
