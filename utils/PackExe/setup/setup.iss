; Script generated by the Inno Setup Script Wizard.
; SEE THE DOCUMENTATION FOR DETAILS ON CREATING INNO SETUP SCRIPT FILES!

#define SetupFileName "device-guard-v"
#define MyAppName "DeviceGuard"
#define MyAppFullName "DeviceGuard"
#define MyAppExeName "DeviceGuard.exe"
#define MyAppPublisher "CfTech"
#define MyAppURL "https://dgc.chkfun.com/home"
#define Lang

#ifndef InputDir
  #define InputDir "C:\\Temp\\Input"  ; 默认路径
#endif

#ifndef OutputDir
  #define OutputDir "C:\\Temp\\Output" ; 默认路径
#endif

#ifndef Version
  #define Version "1.0.0"
#endif

; Generate setup filename
#ifndef FileName
  #define FileName   SetupFileName+Version
#endif

#pragma message "InputDir: " + InputDir
#pragma message "OutputDir: " + OutputDir
#pragma message "FileName: " + FileName
     
[Setup]
; NOTE: The value of AppId uniquely identifies this application. Do not use the same AppId value in installers for other applications.
; (To generate a new GUID, click Tools | Generate GUID inside the IDE.)
AppId={{17A7EA15-582E-470F-953F-C00AC08FBE99}
AppName={#MyAppFullName}
AppVersion={#Version}
AppVerName={#MyAppName} v{#Version}
AppPublisher={#MyAppPublisher}
AppPublisherURL={#MyAppURL}
AppSupportURL={#MyAppURL}
AppUpdatesURL={#MyAppURL}
ArchitecturesInstallIn64BitMode=x64compatible
DefaultDirName={autopf}\Cftech\DeviceGuard
DefaultGroupName=DeviceGuard
;LicenseFile=License.txt
;DisableProgramGroupPage=yes
; Uncomment the following line to run in non administrative install mode (install for current user only.)
;PrivilegesRequired=lowest
OutputDir={#OutputDir}
OutputBaseFilename={#FileName}
SetupIconFile=app.ico
Compression=lzma
SolidCompression=yes
UserInfoPage=no
WizardStyle=modern

[CustomMessages]
; en_US.ResetSettingMessage=Initialize (reset) all settings at startup
; zh_CN.ResetSettingMessage=在启动时初始化(重置)所有设置

en_US.GroupStartup=Startup
; zh_CN.GroupStartup=首次启动

[Tasks]
; Name: "ResetSetting"; Description: "{cm:ResetSettingMessage}"; GroupDescription: "{cm:GroupStartup}";  Flags: unchecked checkedonce

[code]
        
// 生成启动参数
function GetStartArguments(Default: String): String;
begin
  result := '-w -l ' + ActiveLanguage();
  if WizardIsTaskSelected('ResetSetting') then
  begin
    result := result + ' --reset';
  end;
end;

//function IsDotNetFrameworkInstalled():boolean; 
//begin 
//  Result := RegKeyExists(HKLM, 'SOFTWARE\Microsoft\.NETFramework\v4.0.30319\SKUs\.NETFramework,Version=v4.6'); 
//end; 

// execute after language selected
function InitializeSetup(): Boolean; 
var ResultCode:Integer; 
begin 
//    if not IsDotNetFrameworkInstalled() then 
//    begin 
//        ExtractTemporaryFile('NDP46-KB3045560-Web.exe'); 
//        Exec(ExpandConstant('{tmp}\NDP46-KB3045560-Web.exe'), '', '', SW_SHOWNORMAL, ewWaitUntilTerminated, ResultCode); 
//    end;
    Result:=true; 
end;

[Languages]
Name: "en_US"; MessagesFile: "compiler:Default.isl"
; Name: "zh_CN"; MessagesFile: "compiler:Languages\ChineseSimplified.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
Source: "{#InputDir}\*.*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs
; Source: "secret\{#SN}\*"; DestDir: "{app}"; Flags: ignoreversion
; Source: "setting\{#SN}\*"; DestDir: "{app}"; Flags: ignoreversion

; .net framework web installer
; Source: "setup\NDP46-KB3045560-Web.exe"; DestDir: "{tmp}"; Flags: ignoreversion

; NOTE: Don't use "Flags: ignoreversion" on any shared system files

[Dirs]

[Icons]
Name: "{group}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"
Name: "{group}\{cm:UninstallProgram,{#MyAppName}}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\{#MyAppName}"; Filename: "{app}\{#MyAppExeName}"; Tasks: desktopicon

[Run]
Filename: "{app}\{#MyAppExeName}"; Description: "{cm:LaunchProgram,{#StringChange(MyAppName, '&', '&&')}}"; Flags: nowait postinstall skipifsilent; Parameters: {code:GetStartArguments};
