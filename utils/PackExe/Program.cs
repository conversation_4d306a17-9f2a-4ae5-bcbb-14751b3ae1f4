using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Text;
using Newtonsoft.Json;

namespace PackExe;

public class Program
{
    static int Main(string[] args)
    {
        if (args.Length != 3)
        {
            Console.WriteLine("""
                              Argument missing.
                              Usage: PackExe.exe <PathToISCC.exe> <ExeBuildDir> <ExePublishDir>
                              Example: $(SolutionDir)utils\PackExe.exe $(OutDir) $(SolutionDir)bin\$(Configuration)\$(TargetFramework)\$(RuntimeIdentifier)
                              """);
            return -1;
        }

        try
        {
            var innoSetup = args[0];
            var srcDir    = args[1];
            var targetDir = args[2];

            // Invoke inno setup to generate the setup exe file
            var version = GetVersion(Path.Combine(srcDir, "DeviceGuard.dll"));

            Console.WriteLine($"Invoke Inno Setup to generate the setup exe file...");

            var issFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "setup", "setup.iss");

            var startInfo = new ProcessStartInfo
            {
                FileName               = innoSetup,
                Arguments              = $"{issFilePath} \"/DOutputDir={targetDir}\" \"/DInputDir={srcDir}\" \"/DVersion={version}\"",
                RedirectStandardOutput = true,
                RedirectStandardError  = true,
                UseShellExecute        = false, // 必须设为 false 才能重定向
                CreateNoWindow         = true
            };

            using var process = new Process();
            process.StartInfo = startInfo;

            // 订阅输出事件，实时读取输出
            process.OutputDataReceived += (sender, e) =>
            {
                if (e.Data != null)
                    Console.WriteLine("[OUT] " + e.Data);
            };

            process.ErrorDataReceived += (sender, e) =>
            {
                if (e.Data != null)
                    Console.WriteLine("[ERR] " + e.Data);
            };
            
            // 把要执行的命令打印出来
            Console.WriteLine($"{innoSetup} {issFilePath} /DOutputDir={targetDir} /DInputDir={srcDir} /DVersion={version}");

            process.Start();

            // 开始异步读取输出和错误流
            process.BeginOutputReadLine();
            process.BeginErrorReadLine();

            process.WaitForExit();
            Console.WriteLine($"ISCC.exe exited with code {process.ExitCode}");

            if (process.ExitCode != 0)
            {
                Console.Error.WriteLine("Inno Setup compilation failed.");
                return -2;
            }
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine(ex.Message);
            return -1;
        }

        return 0;
    }

    private static string GetVersion(string entryFileName)
    {
        if (!File.Exists(entryFileName))
            throw new Exception($"EntryPoint file '{entryFileName}' not found.");

        var assembly = Assembly.LoadFrom(entryFileName);
        var version = assembly
            .GetCustomAttribute<AssemblyInformationalVersionAttribute>()?
            .InformationalVersion ?? throw new Exception("AssemblyInformationalVersionAttribute not found.");
        version = version.Split('+')[0];

        return version;
    }

    // 递归复制文件
    static void CopyFilesRecursively(string sourceDir, string targetDir)
    {
        // 如果目标目录不存在，创建它
        if (!Directory.Exists(targetDir))
        {
            Directory.CreateDirectory(targetDir);
        }

        // 复制源目录中的所有文件到目标目录
        foreach (string file in Directory.GetFiles(sourceDir))
        {
            string destFile = Path.Combine(targetDir, Path.GetFileName(file));
            File.Copy(file, destFile, true); // true 表示如果文件已存在则覆盖
        }

        // 递归复制子目录
        foreach (string subDir in Directory.GetDirectories(sourceDir))
        {
            string destSubDir = Path.Combine(targetDir, Path.GetFileName(subDir));
            CopyFilesRecursively(subDir, destSubDir);
        }
    }
}