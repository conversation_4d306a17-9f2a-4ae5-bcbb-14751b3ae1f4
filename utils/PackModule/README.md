# Summary

该项目旨在为已编译完成的模块 (Module) 进行打包。其主要功能包括：

1. **复制到模块目录**：将编译好的文件复制到指定的模块目录 `<moduleId>/local`。
2. **删除不必要的库文件**：删除与模块无关的、冗余的库文件，确保打包目录整洁。
3. **生成 `manifest.json` 文件**：根据模块的信息生成 `manifest.json` 文件，用于描述模块的基本属性和配置。
4. **打包为 ZIP 文件**：将处理后的模块文件打包为压缩文件，存放在 `<moduleId>/publish/<moduleId>-<version>-win64.zip`。

通过此工具，开发人员可以直接调试已复制到模块目录的 DLL 文件。如果需要发布模块，只需将打包后的文件上传至 Web 云端系统即可。

# How to Use

在编译后事件 (Post-Build Event) 中，增加以下命令来调用 `PackModule.exe`：

```bash
$(SolutionDir)utils\PackModule.exe $(OutDir) $(SolutionDir)bin\$(Configuration)\net8.0-windows\Modules
```

此命令会自动触发打包过程。此后，您可以在编译后的事件中调用此工具，进行自动打包和发布操作。

# How to Build PackModule.exe

完成编译调试后，您需要将 `PackModule.exe` 发布到 `utils` 目录下。执行以下步骤进行发布：

1. **构建 PackModule.exe**：
    - 打开项目并完成编译。
    - 在生成的输出文件夹中找到 `PackModule.exe`。
2. **发布 PackModule.exe**：
    - 执行 PackModule 项目的发布命令 `Publish PackModule to utils`。项目将自动编译, 打包和发布至目标位置
3. **签入编译的exe**：
    - 将编译后的 `PackModule.exe` 签入到源代码管理中。
