using System.Diagnostics;
using System.IO.Compression;
using System.Reflection;
using System.Text;
using DeviceGuard.ModuleCore.Modules.Impl;
using Newtonsoft.Json;

namespace PackModule;

public class Program
{
    static int Main(string[] args)
    {
        if (args.Length != 2)
        {
            Console.WriteLine("""
                              Argument missing.
                              Usage: PackModule.exe <ModuleBuildDir> <ModuleOutputDir> ('/Modules' sub folder don't be filled)
                              Example: $(SolutionDir)utils\PackModule.exe $(OutDir) $(SolutionDir)bin\$(Configuration)\$(TargetFramework)\$(RuntimeIdentifier)
                              """);
            return -1;
        }

        try
        {
            var srcDir        = args[0];
            var targetBaseDir = args[1];
            var manifest      = GetManifest(srcDir);

            // All building results will copy to moduleName/dev
            var debuggingModules = "DebuggingModules";
            var target           = Path.Combine(targetBaseDir, debuggingModules, manifest.ModuleName, "dev");

            // Remove old files
            if (Directory.Exists(target))
            {
                Console.WriteLine($"Clear old files from '{target}'...");
                Directory.Delete(target, true);
            }

            // Copy files
            Console.WriteLine($"Copy files from '{srcDir}' to '{target}'...");
            CopyFilesRecursively(srcDir, target);

            // Remove the same file
            Console.WriteLine($"Remove specified files from '{target}'...");
            RemoveFilesRecursively(target, targetBaseDir);

            var version = GetVersion(Path.Combine(target, manifest.EntryPoint));

            // Generate manifest.json
            // Console.WriteLine($"Generate 'manifest.json' in the '{target}");
            // var contents = JsonConvert.SerializeObject(manifest, Formatting.Indented);
            // File.WriteAllText(Path.Combine(target, "manifest.json"), contents);

            // Zip all target directories into file '.publish/dgm-moduleName-version-release-win64.zip'
            Console.WriteLine($"Zip all target directories from dir '{target}");
            var zipFileName = $"dgm-{manifest.ModuleName}-{version}-win64.zip";
            var zipFolder   = Path.Combine(targetBaseDir, debuggingModules, ".publish");
            if (!Directory.Exists(zipFolder))
            {
                Directory.CreateDirectory(zipFolder);
            }

            var zipFilePath = Path.Combine(zipFolder, zipFileName);
            if (File.Exists(zipFilePath))
            {
                File.Delete(zipFilePath);
            }

            ZipFile.CreateFromDirectory(target, zipFilePath);
            Console.WriteLine($"Zip file: {zipFilePath}");

            Console.WriteLine($"Pack module '{manifest.ModuleName} ({version})' completed.");
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine(ex.Message);
            return -1;
        }

        return 0;
    }

    /// <summary>
    /// 比较 target 目录文件和 targetBaseDir 目录的文件, 把 targetBaseDir 已经包含的文件从 target 中移除
    /// </summary>
    /// <param name="target"></param>
    /// <param name="targetBaseDir"></param>
    /// <exception cref="NotImplementedException"></exception>
    private static void RemoveFilesRecursively(string target, string targetBaseDir)
    {
        var targetFiles = Directory.GetFiles(target, "*", SearchOption.AllDirectories);
        foreach (var file in targetFiles)
        {
            var fileName    = Path.GetFileName(file);
            var baseDirFile = Path.Combine(targetBaseDir, fileName);

            if (!File.Exists(baseDirFile))
                continue;

            // 如果是DLL, 比较文件的版本号
            if (fileName.EndsWith(".dll"))
            {
                var fileVersion        = FileVersionInfo.GetVersionInfo(file).FileVersion;
                var baseDirFileVersion = FileVersionInfo.GetVersionInfo(baseDirFile).FileVersion;
                if (fileVersion != baseDirFileVersion)
                {
                    continue;
                }
            }

            // 如果是PDB, 比较对应的DLL文件的版本
            if (fileName.EndsWith(".pdb"))
            {
                var dllFileName     = Path.ChangeExtension(fileName,    ".dll");
                var baseDllFileName = Path.ChangeExtension(baseDirFile, ".pdb");

                if (File.Exists(dllFileName) && File.Exists(baseDllFileName))
                {
                    var dllFileVersion     = FileVersionInfo.GetVersionInfo(dllFileName).FileVersion;
                    var baseDllFileVersion = FileVersionInfo.GetVersionInfo(baseDllFileName).FileVersion;
                    if (dllFileVersion != baseDllFileVersion)
                    {
                        continue;
                    }
                }
            }

            File.Delete(file);
        }
    }

    private static ModuleManifest GetManifest(string srcDir)
    {
        var manifestFileName = "manifest.json";
        if (!Directory.Exists(srcDir))
        {
            throw new Exception($"Source path not found: '{srcDir}");
        }

        if (!File.Exists(Path.Combine(srcDir, manifestFileName)))
        {
            var sb = new StringBuilder();
            sb.AppendLine("Argument missing.");
            sb.AppendLine($"You MUST generate {manifestFileName} in root path of the module and make it with 'Copy Always' in the  property of this file.");
            var template = new ModuleManifest
            {
                ModuleName = "YourModuleName",
                Brief      = "YourModule Brief",
                EntryPoint = "YourModule.dll"
            };
            sb.AppendLine(JsonConvert.SerializeObject(template, Formatting.Indented));
            throw new Exception(sb.ToString());
        }

        var manifest = JsonConvert.DeserializeObject<ModuleManifest>(File.ReadAllText(Path.Combine(srcDir, manifestFileName)));
        if (manifest == null)
        {
            throw new Exception("Invalid manifest file.");
        }

        if (string.IsNullOrEmpty(manifest.EntryPoint))
        {
            throw new Exception("EntryPoint is empty.");
        }

        if (string.IsNullOrEmpty(manifest.ModuleName))
        {
            throw new Exception("ModuleName is empty.");
        }

        var entryFileName = Path.Combine(srcDir, manifest.EntryPoint);
        if (!File.Exists(entryFileName))
        {
            throw new Exception($"EntryPoint file '{entryFileName}' not found.");
        }

        return manifest;
    }

    private static string GetVersion(string entryFileName)
    {
        var assembly = Assembly.LoadFrom(entryFileName);
        var version = assembly
            .GetCustomAttribute<AssemblyInformationalVersionAttribute>()?
            .InformationalVersion ?? throw new Exception("AssemblyInformationalVersionAttribute not found.");
        version = version.Split('+')[0];

        return version;
    }

    // 递归复制文件
    static void CopyFilesRecursively(string sourceDir, string targetDir)
    {
        // 如果目标目录不存在，创建它
        if (!Directory.Exists(targetDir))
        {
            Directory.CreateDirectory(targetDir);
        }

        // 复制源目录中的所有文件到目标目录
        foreach (string file in Directory.GetFiles(sourceDir))
        {
            string destFile = Path.Combine(targetDir, Path.GetFileName(file));
            File.Copy(file, destFile, true); // true 表示如果文件已存在则覆盖
        }

        // 递归复制子目录
        foreach (string subDir in Directory.GetDirectories(sourceDir))
        {
            string destSubDir = Path.Combine(targetDir, Path.GetFileName(subDir));
            CopyFilesRecursively(subDir, destSubDir);
        }
    }
}