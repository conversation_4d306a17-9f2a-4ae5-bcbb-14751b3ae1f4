using System.Text.RegularExpressions;
using Semver;

namespace ReleaseVersionConsole;

public class VersionInfo
{
    public SemVersion? AssemblyVersion { get; set; }

    public int BuildNumber { get; set; }

    public DateTime BuildTime { get; set; }

    public void WriteTo(string filePath)
    {
        var versionInfo = $"""
                           /*
                            * This file is AUTO-GENERATED by ReleaseVersionConsole
                            * Contact <EMAIL> if you have any questions or suggestions
                            * Generated at {DateTime.Now:yyyy-MM-dd HH:mm:ss}
                            *
                            * DO NOT EDIT THIS FILE MANUALLY
                            *
                            */
                                
                           using System.Reflection;

                           [assembly: AssemblyInformationalVersion("{AssemblyVersion}")]
                           [assembly: AssemblyMetadata("build", "{BuildNumber}")]
                           [assembly: AssemblyMetadata("time", "{BuildTime:yyyy-MM-dd HH:mm:ss}")]

                           """;

        File.WriteAllText(filePath, versionInfo);
    }

    public static VersionInfo? Parse(string versionInfoFilePath)
    {
        if (!File.Exists(versionInfoFilePath))
        {
            return null;
        }

        var versionText = File.ReadAllText(versionInfoFilePath);
        var verMatch    = Regex.Match(versionText, @"AssemblyInformationalVersion\(""(?<ver>\d+(\.\d+(\.\d+(\.\d+)?)?)?)""\)");
        var buildMatch  = Regex.Match(versionText, @"AssemblyMetadata\(""build"", ""(?<build_number>\d+)""\)");
        var timeMatch   = Regex.Match(versionText, @"AssemblyMetadata\(""time"", ""(?<build_time>.+)""\)");

        var versionInfo = new VersionInfo();
        var parsed      = false;
        if (verMatch.Success)
        {
            var ver = verMatch.Groups["ver"].Value;
            versionInfo.AssemblyVersion = SemVersion.Parse(ver);
            parsed                      = true;
        }

        if (buildMatch.Success)
        {
            versionInfo.BuildNumber = int.Parse(buildMatch.Groups["build_number"].Value);
            parsed                  = true;
        }

        if (timeMatch.Success)
        {
            versionInfo.BuildTime = DateTime.Parse(timeMatch.Groups["build_time"].Value);
            parsed                = true;
        }

        return parsed ? versionInfo : null;
    }
}