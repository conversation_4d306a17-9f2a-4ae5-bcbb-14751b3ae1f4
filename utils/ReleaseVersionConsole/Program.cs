// See https://aka.ms/new-console-template for more information
/* This project will recognize the following files like this:
[
  {
    "version": "1.1.0",
    "type": "beta",
    "date": "",
    "new_features": [
    ],
    "bug_fixed": [
    ]
  },
  {
    "version": "1.1.0",
    "type": "beta",
    "date": "",
    "new_features": [
    ],
    "bug_fixed": [
    ]
  }
]

Find the latest version of the file then calc and generate VersionInfo.cs in the same place.
The file VersionInfo.cs will be like this:
(We assume the latest version is 2.1.4)
```
// This file is auto-generated by ReleaseVersionConsole
// Do not edit this file manually
// Contact nepton if you have any questions

using System.Reflection;

[assembly: AssemblyVersion("2.1.4.1")]
[assembly: AssemblyFileVersion("2.1.4.27")]
```

The rule of version is:
```
major.minor.patch

major: when we make incompatible system, or the break change is too much, we will increase the major version.
minor: when we add new features, we will increase the minor version.
patch: when we fix bugs, we will increase the patch version.
build: when we build the project, we will increase the build number automatic.
```

> Notice:
> The build number is not exists in changelog.json, and not necessary to be update to changelog.json when build
> The build number just keep in VersionInfo.cs, and will be auto-generated when build.

*/

// read file and parse json by specified path in args:
// 1. read file

using ReleaseVersionConsole;

if (args.Any() == false)
{
    Console.Error.WriteLine("""
                            No arguments specified. Please specify the argument:
                            -v ./YourFile.exe     return the version of the specified file.
                            -g ./changelog.json generate version from changelog.json and write to VersionInfo.cs before build.

                            changelog.json file as the first argument.
                            The format of changelog.json file like:
                            [
                              {
                                "version": "1.1.0-beta",
                                "date": "2017-01-01",
                                "summary": "",
                                "features": [
                                  "release note1",
                                  "release note2"
                                ],
                                "bug_fixed": [
                                  "bug fixed 1",
                                  "bug fixed 2"
                                ]
                              }
                            ]
                            """);
    return 1;
}

if (args[0] == "-v")
{
    if (args.Length < 3)
    {
        Console.Error.WriteLine("No file path specified. Please specify the file path as the second argument.");
        return 1;
    }

    return VersionConsole.ShowVersion(args[1], args[2]);
}

if (args[0] == "-g")
{
    if (args.Length < 2)
    {
        Console.Error.WriteLine("No file path specified. Please specify the changelog.json file as the second argument.");
        return 1;
    }

    return VersionConsole.Generate(args[1]);
}

Console.Error.WriteLine("Unknown argument: " + args[0]);
return 1;