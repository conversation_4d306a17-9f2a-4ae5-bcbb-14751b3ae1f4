using System.Text.Json.Nodes;
using System.Text.RegularExpressions;
using Semver;

namespace ReleaseVersionConsole;

public class VersionConsole
{
    /// <summary>
    /// TODO 从 VersionInfo.cs 中读取信息，返回一个版本号, 通常这是给 CI/CD 用的
    /// </summary>
    /// <param name="filePath"></param>
    /// <param name="format">
    /// 支持以下关键字:
    /// 例如:
    /// [version:3].[type]_[date:yyyyMMddHHmmss] => 3.1.0.release_20210101101010
    /// [version:3].[type:s][build] => 3.1.0.r2
    ///  
    /// - version 版本号
    ///   3 表示取前3位版本号
    /// 
    /// - type 版本类型
    ///   s 表示版本类型使用缩写, 如 beta -> beta, alpha -> alpha, dev -> dev, release_candidate -> rc, release -> r
    ///   nr 表示不显示 release 版本类型
    ///
    /// - build 编译号
    ///
    /// - time 编译日期
    ///   yyyyMMddHHmmss 表示使用 yyyyMMddHHmmss 格式
    /// 
    /// </param>
    /// <returns></returns>
    /// <exception cref="ArgumentNullException"></exception>
    public static int ShowVersion(string filePath, string format)
    {
        if (format == null) throw new ArgumentNullException(nameof(format));

        var versionInfo = VersionInfo.Parse(filePath);
        if (versionInfo == null)
        {
            Console.Error.WriteLine($"Cannot find VersionInfo.cs file in directory: {filePath}");
            return 1;
        }

        var match = Regex.Match(format, @"\[(?<key>\w+)(:(?<format>.+?))?\]");
        while (match.Success)
        {
            var key = match.Groups["key"].Value;
            var fmt = match.Groups["format"].Value;
            var value = key switch
            {
                "version" => versionInfo.AssemblyVersion?.ToString(),
                "build"   => versionInfo.BuildNumber.ToString(),
                "time"    => versionInfo.BuildTime.ToString(fmt),
                _         => match.Value
            };
            format = format.Replace(match.Value, value);
            match  = match.NextMatch();
        }

        Console.WriteLine(format);

        return 0;
    }

    public static int Generate(string filePath)
    {
        if (filePath == null) throw new ArgumentNullException(nameof(filePath));

        filePath = Path.GetFullPath(filePath);
        var fileDir = Path.GetDirectoryName(filePath);

        if (File.Exists(filePath) == false)
        {
            Console.Error.WriteLine($"File not found: {filePath}");
            return 1;
        }

        if (Directory.Exists(fileDir) == false)
        {
            Console.Error.WriteLine($"Directory not found: {fileDir}");
            return 1;
        }

        var json = File.ReadAllText(filePath);
        if (string.IsNullOrEmpty(json))
        {
            Console.Error.WriteLine($"File is empty: {filePath}");
            return 1;
        }

        // 2. parse json
        var jVersionArray = JsonNode.Parse(json) as JsonArray;
        if (jVersionArray == null)
        {
            Console.Error.WriteLine($"Cannot file the version array in file: {filePath}");
            return 1;
        }

        // query latest version and it's type item
        var releaseNote = (from jObject in jVersionArray
            let verText = jObject["version"]?.GetValue<string>()
            where string.IsNullOrEmpty(verText) == false
            let ver = SemVersion.Parse(verText)
            orderby ver descending
            select new
            {
                Version = ver,
            }).FirstOrDefault();

        if (releaseNote == null)
        {
            Console.Error.WriteLine($"Cannot find any release version in file: {filePath}");
            return 1;
        }

        // 3. read current build number from VersionInfo.cs
        var versionInfoFilePath = Path.GetFullPath(Path.Combine(fileDir, "VersionInfo.cs"));

        var versionInfo = VersionInfo.Parse(versionInfoFilePath) ?? new();
        versionInfo.BuildTime = DateTime.Now;
        if (versionInfo.AssemblyVersion == releaseNote.Version)
        {
            versionInfo.BuildNumber += 1;
        }
        else
        {
            versionInfo.BuildNumber     = 1;
            versionInfo.AssemblyVersion = releaseNote.Version;
        }

        versionInfo.WriteTo(versionInfoFilePath);

        // 4. return the version number to stdout
        Console.WriteLine(releaseNote.Version);
        return 0;
    }
}