using DeviceGuard.Windows.Utility.Mvvm;
using Google.Protobuf.WellKnownTypes;
using MosIgbtTest.DeviceDB;
using Newtonsoft.Json.Linq;
using Prism.Commands;
using Prism.Events;
using Prism.Mvvm;
using Prism.Navigation.Regions;
using Serilog;
using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Reflection.Metadata;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;

namespace MosIgbtTest.Views;

public class TransistorMenuViewModel : BindableBase
{
    List<VmovsEFT> MOSEFT_TABItemsSource;
    private readonly IRegionManager regionManager;
    private readonly IEventAggregator eventAggregator;
    public TransistorMenu View;
    public TransistorMenuViewModel(IRegionManager regionManager,
        IEventAggregator eventAggregator
        )
    {

        MOSEFT_TABItemsSource = DeviceDBFactory.CreateVMOSEFTDB();
        this.regionManager = regionManager;
        this.eventAggregator = eventAggregator;
    }
    public List<VmovsEFT> MOSEFTDataSet => MOSEFT_TABItemsSource;

    BitmapImage _selectImage;
    public BitmapImage SelectImage
    {
        get
        {
            if (_selectImage != null)
                return _selectImage;

            BitmapImage bitmap = new BitmapImage();
            bitmap.BeginInit();
            bitmap.StreamSource = new System.IO.MemoryStream(Resource1.mosEFTImage);
            bitmap.EndInit();
            return bitmap;
        }
        private set
        {
            _selectImage = value;
            RaisePropertyChanged(nameof(SelectImage));
        }
    }
    string description;
    public string Description
    {
        get
        {
            if (string.IsNullOrEmpty(description))
                description = " ";
            return description;
        }
        set
        {
            description = value;
            RaisePropertyChanged(nameof(Description));
        }
    }
    string stype;
    public string Stype
    {
        get
        {
            if (string.IsNullOrEmpty(description))
                stype = "未选择器件";
            return stype;
        }
        set
        {
            stype = value;
            RaisePropertyChanged(nameof(Description));
        }
    }
    object _SelectedItem = null;
    public object SelectedItem
    {
        get { return _SelectedItem; }
        set
        {
            if (value is VmovsEFT moseft)
            {
                _SelectedItem = value;
                Description = moseft.DocDescription;
                Stype = moseft.Stype;
                SelectImage = moseft.DocImage;
                Debug.WriteLine($"ID＝{moseft.ID} 型号：{moseft.Stype}");
            }
        }
    }

    #region Command 
    private ICommand _IntoTestCommand;
    public ICommand IntoTestCommand => _IntoTestCommand ??= new DelegateCommand(ExecIntoTestCommand, CanExecIntoTestCommand).ObservesProperty(() => SelectedItem);

    void ExecIntoTestCommand()
    {
        if (_SelectedItem is VmovsEFT moseft)
        {
            var region = regionManager.Regions[RegionNames.PrimaryViewRegion];
            region.RequestNavigate(nameof(MostTest));
            eventAggregator.GetEvent<TestMosEFTEven>().Publish(moseft);
        }
    }
    bool CanExecIntoTestCommand()
    {
        return _SelectedItem != null;
    }




    /*
     ICommand selectionChangedCommand;
     public ICommand SelectionChangedCommand => selectionChangedCommand ??= new DelegateCommand<object>((paramater) =>
     {
         if (paramater is System.Windows.Controls.SelectionChangedEventArgs args)
         {
         }
         else
         {

         }

     },
     (paramater) =>
      {
          return true;
      }
     );
     */
    #endregion
}