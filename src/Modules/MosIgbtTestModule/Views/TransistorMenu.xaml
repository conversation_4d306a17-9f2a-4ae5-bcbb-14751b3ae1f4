<UserControl x:Class="MosIgbtTest.Views.TransistorMenu"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:prism="http://prismlibrary.com/"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:styles="clr-namespace:DeviceGuard.Styles;assembly=DeviceGuard.Styles"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:views="clr-namespace:MosIgbtTest.Views"
             prism:ViewModelLocator.AutoWireViewModel="True"
              xmlns:i="http://schemas.microsoft.com/xaml/behaviors"
             mc:Ignorable="d"
             d:DesignHeight="600"
             d:DesignWidth="1200"
             d:DataContext="{d:DesignInstance views:TransistorMenuViewModel}">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!--Design time style resources-->
                <styles:DesignTimeResourceDictionary
                    Source="pack://application:,,,/DeviceGuard.Styles;component/AppStyle.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"></ColumnDefinition>
            <ColumnDefinition Width="3"></ColumnDefinition>
            <ColumnDefinition Width="*"></ColumnDefinition>
        </Grid.ColumnDefinitions>
        <GridSplitter Grid.Column="1"
                      HorizontalAlignment="Center"
                      VerticalAlignment="Stretch"
                      Background="#FF7E7E7E"
                      ShowsPreview="True"
                      Width="5" />
        <StackPanel Grid.Column="0"
                    MinWidth="200">
            <!-- 设置晶体管测量的基础参数-->
            <materialDesign:Card x:Name="card" Padding="6"
                                 Margin="10"
                                 Background="#FF46A4ED">
                <StackPanel>
                    <TextBlock Margin="0 0 0 20"
                               Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                               Text="连接测试仪" />
                    <TextBlock Text="端口名" />
                    <TextBox Margin="0 10 0 0"
                              />
                    <!-- 启动和停止按钮 -->
                    <StackPanel Orientation="Horizontal"
                                HorizontalAlignment="Left"
                                Margin="0 10">
                        <Button Content="连接"
                                Style="{StaticResource MaterialDesignRaisedButton}" />
                        <Button Content="断开"
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Margin="10,0,0,0" />
                    </StackPanel>
                </StackPanel>
            </materialDesign:Card>
            <materialDesign:Card Padding="30"
                                 Margin="10"
                                 Background="#FFF1F1C8">
                <ListBox Grid.Row="1"
                         Margin="0,20,0,0"
                         VerticalAlignment="Stretch">
                    <ListBox.ItemsPanel>
                        <ItemsPanelTemplate>
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <!-- <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="*"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>-->
                                </Grid.RowDefinitions>
                            </Grid>
                        </ItemsPanelTemplate>
                    </ListBox.ItemsPanel>

                    <ListBoxItem Grid.Row="0"
                                 IsEnabled="False">
                        <Separator MinWidth="300"></Separator>
                    </ListBoxItem>
                    <ListBoxItem Grid.Row="1">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon VerticalAlignment="Stretch"
                                                     Kind="ExpansionCard"
                                                     Width="20"
                                                     Height="20"
                                                     Margin="0 0 10 0" />
                            <TextBlock
                                Text="大功率 MOSFET 三极管" />
                        </StackPanel>
                    </ListBoxItem>
                    <ListBoxItem Grid.Row="2">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon VerticalAlignment="Stretch"
                                                     Kind="Chip"
                                                     Width="20"
                                                     Height="20"
                                                     Margin="0 0 10 0" />
                            <TextBlock
                                Text="大功率整流二极管" />
                        </StackPanel>
                    </ListBoxItem>
                    <ListBoxItem Grid.Row="3">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon VerticalAlignment="Stretch"
                                                     Kind="Chip"
                                                     Width="20"
                                                     Height="20"
                                                     Margin="0 0 10 0" />
                            <TextBlock
                                Text="IGBT" />
                        </StackPanel>
                    </ListBoxItem>
                </ListBox>
            </materialDesign:Card>
            <materialDesign:Card Padding="10"
                                 Margin="10">
                <Button Content="开始=>测试"
                        Command="{Binding IntoTestCommand }"
                        
                        Style="{StaticResource MaterialDesignRaisedLightButton}"
                        Cursor="Hand"
                        Background="#FFE28080" />
            </materialDesign:Card>
            <materialDesign:Card Padding="10"
                                 Margin="10">
                <Button Content="退出晶体管测试"
                        Command="{Binding ConnectCommand }"
                        IsEnabled="{Binding IsSelectted }"
                        Style="{StaticResource MaterialDesignRaisedLightButton}"
                        Background="#FFF8F959" />
            </materialDesign:Card>
        </StackPanel>
        <Grid Grid.Column="2">
            <Grid.RowDefinitions>
                <RowDefinition Height="1*" />
                <RowDefinition Height="2*" />
            </Grid.RowDefinitions>
            <!--显示被选中器件的简单描述和图片-->
            <materialDesign:Card Grid.Row="0"
                                 Padding="10"
                                 Margin="10"
                                >
                <Grid >
                    <Grid.ColumnDefinitions >
                        <ColumnDefinition Width="{Binding ActualHeight, ElementName=card, Mode=OneWay}"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    <Grid.RowDefinitions >
                        <RowDefinition Height="40" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>
                    <Image Source="{Binding SelectImage}" Grid.Column="0" Grid.Row="0" Grid.RowSpan="2" Stretch="Fill"   />
                    <TextBlock  Text="{Binding Stype}" Grid.Column="1"  Grid.Row="0"  FontSize="28" />
                    <TextBlock Margin="6,6,6,0"  Text="{Binding Description}" Grid.Column="1"  Grid.Row="1" TextWrapping="WrapWithOverflow"/>
                </Grid>
            </materialDesign:Card>
            <!-- 添加 DataGrid 显示数据源, 显示器件列表 -->
            <TabControl Grid.Row="1">
                <TabItem Header="MOS管 " x:Name="MOSEFT_TAB" Background="#FFF6F7B0" >
                    <DataGrid  
                      Margin="10"
                      AutoGenerateColumns="True" 
                      x:Name="MosDataGrid"
                      ItemsSource="{Binding MOSEFTDataSet}" Background="#FFEFF0A5" 
                        SelectedItem="{Binding SelectedItem}" IsReadOnly="True" SelectionMode="Single" >
                        <!--<i:Interaction.Triggers>
                            <i:EventTrigger EventName="SelectionChanged">
                                <i:InvokeCommandAction Command="{Binding SelectionChangedCommand}"
                                           PassEventArgsToCommand="True"/>
                            </i:EventTrigger>
                        </i:Interaction.Triggers>-->
                    </DataGrid>
                </TabItem>
                <TabItem Header="大功率二极管" x:Name="Diode_Tab" Background="#FFC5F3B2">

                </TabItem>
                <TabItem Header="IGBT" x:Name="IGBT_Tab">

                </TabItem>
            </TabControl>

        </Grid>
    </Grid>
</UserControl>