using Doulex;
using System.Windows.Controls;
using System.ComponentModel;
using System;

namespace MosIgbtTest.Views;

/// <summary>
/// Interaction logic for TransistorMenu
/// </summary>
public partial class TransistorMenu : UserControl
{
    public TransistorMenu()
    {
        InitializeComponent();
        MosDataGrid.AutoGeneratingColumn += MosDataGrid_AutoGeneratingColumn;
        if (DataContext is TransistorMenuViewModel viewModel)
        {
            viewModel.View = this;
        }
    }

    private void MosDataGrid_AutoGeneratingColumn(object sender, DataGridAutoGeneratingColumnEventArgs e)
    {
        PropertyDescriptor p = (PropertyDescriptor)e.PropertyDescriptor;
        if (p.IsBrowsable == false)
        {
            e.Cancel = true;
            return;
        }
        e.Column.Header = p.Description;
    }

}