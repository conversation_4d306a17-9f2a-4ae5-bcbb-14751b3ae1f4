<UserControl x:Class="MosIgbtTest.Views.MostTest"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:styles="clr-namespace:DeviceGuard.Styles;assembly=DeviceGuard.Styles"
             xmlns:prism="http://prismlibrary.com/"
             xmlns:wpf1="clr-namespace:CefSharp.Wpf;assembly=CefSharp.Wpf"
             xmlns:conv="clr-namespace:DeviceGuard.Windows.Controls.Conv;assembly=DeviceGuard.Windows.Controls"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:views="clr-namespace:MosIgbtTest.Views"
             prism:ViewModelLocator.AutoWireViewModel="True"
             mc:Ignorable="d"
             d:DesignHeight="600"
             d:DesignWidth="1200"
             d:DataContext="{d:DesignInstance views:MostTestViewModel}">
    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!--Design time style resources-->
                <styles:DesignTimeResourceDictionary
                    Source="pack://application:,,,/DeviceGuard.Styles;component/AppStyle.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>
    <Grid Margin="10">
        <Grid.Resources>
            <conv:BooleanToTextConverter x:Key="ExpiredToForegroundConverter"
                                         True="LightGray"
                                         False="Black"/>
            <materialDesign:InvertBooleanConverter x:Key="InverseBooleanConverter"/>
        </Grid.Resources>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"></ColumnDefinition>
            <ColumnDefinition Width="3"></ColumnDefinition>
            <ColumnDefinition Width="*"></ColumnDefinition>
        </Grid.ColumnDefinitions>
        <!--<Grid.RowDefinitions></Grid.RowDefinitions>-->
        <GridSplitter Grid.Column="1"
                      HorizontalAlignment="Center"
                      VerticalAlignment="Stretch"
                      Background="#FF7E7E7E"
                      ShowsPreview="True"
                      Width="5"/>
        <StackPanel Grid.Column="0"
                    MinWidth="300">
            <!--<materialDesign:Card Padding="30" Margin="10"  Background="#FF46A4ED">
                    <TextBlock Margin="0 0 0 20"
                    Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                    Text="连接测试仪" />

                    <TextBlock Text="端口名" />
                    <TextBox Margin="0 10 0 0"  Text="{Binding PortName}" />


                </StackPanel>
            </materialDesign:Card>-->

            <materialDesign:Card Padding="10"
                                 Margin="10"
                                 Background="#FF94BDE2">
                <StackPanel>
                    <Button Grid.Column="0"
                            Content="选择晶体管"
                            Foreground="White"
                            Command="{Binding SelectCommand }"
                           
                            Margin="10,0,0,0"/>
                    <StackPanel Orientation="Horizontal"
                            Margin="4,0,6,0">
                        <TextBlock Text="型号:"
                                Style="{StaticResource MaterialDesignHeadline4TextBlock}"/>
                        <TextBlock Text="{Binding ActiveMOSEFT.Stype}" VerticalAlignment="Bottom" 
                                   Style="{StaticResource MaterialDesignHeadline6TextBlock}"/>
                    </StackPanel>
                    <!-- 启动和停止按钮 -->
                    <Grid   Margin="0 10 0 0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*"></ColumnDefinition>
                            <ColumnDefinition Width="1*"></ColumnDefinition>

                        </Grid.ColumnDefinitions>

                        <Button Grid.Column="0"
                                Content="启动测试"
                                Background="Green"
                                Foreground="White"
                                IsEnabled="{Binding IsConnected}"
                                Command="{Binding StartCommand }"
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Margin="10,0,0,0"/>
                        <Button Grid.Column="1"
                                Content="停止测试"
                                Background="Red"
                                Foreground="White"
                                IsEnabled="{Binding IsConnected}"
                                Command="{Binding StopCommand }"
                                Margin="10,0,0,0"
                                Style="{StaticResource MaterialDesignRaisedButton}">
                        </Button>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>
            <materialDesign:Card Padding="10"
                                 Margin="10"
                                 Background="#FFD6E294">
                <StackPanel>
                    <TextBlock Margin="0 0 0 3"
                               Text="耐压测试"
                               FontSize="25"/>
                    <Grid Margin="10">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"></ColumnDefinition>
                            <ColumnDefinition Width="40"></ColumnDefinition>
                            <ColumnDefinition Width="*"></ColumnDefinition>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="38"/>
                            <RowDefinition Height="38"/>
                            <RowDefinition Height="38"/>
                        </Grid.RowDefinitions>
                        <TextBlock  Grid.Column="0"
                                    Grid.Row="0"
                                    Text="测试电压:24 V"
                                    VerticalAlignment="Center"/>
                        <TextBlock  Grid.Column="1"
                                    Grid.Row="0"
                                    Text="{Binding ResultTest24V}"
                                    VerticalAlignment="Center"/>
                        <Button Margin="4"
                                Padding="1"
                                Content="24V 测试"
                                Grid.Column="3"
                                Grid.Row="0"
                                Command="{Binding Test24VCommand}"
                                Background="#FF6DD723"/>

                        <TextBlock  Grid.Column="0"
                                    Grid.Row="1"
                                    Text="测试电压: 200 V"
                                    VerticalAlignment="Center"/>
                        <TextBlock  Grid.Column="1"
                                    Grid.Row="1"
                                    Text="{Binding ResultTest200V}"
                                    VerticalAlignment="Center"/>
                        <Button Margin="4"
                                Padding="1"
                                Content="200V 测试"
                                Grid.Column="3"
                                Grid.Row="1"
                                Command="{Binding Test200VCommand}"
                                Background="#FFD79F23"/>

                        <TextBlock  Grid.Column="0"
                                    Grid.Row="2"
                                    Text="测试电压: 400 V"
                                    VerticalAlignment="Center"/>
                        <!--显示测试成果: 未开始, 通过, 失败-->
                        <TextBlock  Grid.Column="1"
                                    Grid.Row="2"
                                    Text="{Binding ResultTest400V}"
                                    VerticalAlignment="Center"/>
                        <Button Margin="4"
                                Padding="1"
                                Content="400V测试"
                                Grid.Column="3"
                                Grid.Row="2"
                                Command="{Binding Test400VCommand}"
                                Background="#FFD75123"/>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>
            <materialDesign:Card   Padding="10"
                                   Margin="10"
                                   Background="#FFD6E294">
                <StackPanel>
                    <TextBlock Margin="0 0 0 20"
                               Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                               Text="电流承载能力测试"/>
                    <Grid   Margin="10">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="39"/>
                            <RowDefinition Height="39"/>
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"></ColumnDefinition>
                            <ColumnDefinition Width="50"></ColumnDefinition>
                            <ColumnDefinition Width="*"></ColumnDefinition>
                        </Grid.ColumnDefinitions>
                        <Grid.Resources>
                            <Style TargetType="TextBlock">
                                <Setter Property="VerticalAlignment"
                                        Value="Center"></Setter>
                                <Setter Property="FontSize"
                                        Value="14"></Setter>
                                <Setter Property="Margin"
                                        Value="0 10 20 10"></Setter>
                            </Style>
                            <Style TargetType="TextBox"
                                   BasedOn="{StaticResource MaterialDesignTextBox }">
                                <Setter Property="TextAlignment"
                                        Value="Right"></Setter>
                                <Setter Property="VerticalAlignment"
                                        Value="Center"></Setter>
                                <Setter Property="IsReadOnly"
                                        Value="True"></Setter>
                            </Style>
                        </Grid.Resources>
                        <TextBlock Grid.Row="0"
                                   Grid.Column="0"
                                   Text="测试电流:"/>
                        <TextBox Grid.Row="0"
                                 Grid.Column="1"
                                 Text="{Binding RunStatus}"/>

                        <Button Grid.Row="0"
                                Grid.Column="2"
                                Content="测试"
                                Background="CadetBlue"
                                Command="{Binding counterTestCommand}"/>
                        <!--显示测试成果: 未开始, 通过, 失败-->
                        <TextBox Grid.Row="1"
                                 Grid.Column="0"
                                 Grid.ColumnSpan="3"
                                 Text="{Binding RunStatus}"/>
                    </Grid>
                </StackPanel>
            </materialDesign:Card>
        </StackPanel>
        <materialDesign:Card Grid.Column="2"
                             Margin="10">
            <!--显示器件数据 -->
            <!--<web:WebView2 Source="https://www.bing.com"
                                      HorizontalAlignment="Stretch"
                                      VerticalAlignment="Stretch"/>-->
            <wpf1:ChromiumWebBrowser Name="webView1"/>
        </materialDesign:Card>
    </Grid>
</UserControl>
