using DeviceGuard.Windows.Utility.Mvvm;
using MosIgbtTest.DeviceDB;
using Prism.Commands;
using Prism.Events;
using Prism.Mvvm;
using Prism.Navigation.Regions;
using System.Diagnostics;
using System.Windows.Input;

namespace MosIgbtTest.Views;

public class MostTestViewModel : BindableBase
{
    private VmovsEFT _Activemoseft;
    private readonly IEventAggregator eventAggregator;
    private readonly IRegionManager regionManager;
    public VmovsEFT ActiveMOSEFT
    {
        get { return _Activemoseft; }
        set { SetProperty(ref _Activemoseft, value); }
    }

    public MostTestViewModel(IRegionManager regionManager,  IEventAggregator eventAggregator)
    {
        ActiveMOSEFT = null;
        this.eventAggregator = eventAggregator;
        this.regionManager = regionManager;
        eventAggregator.GetEvent<TestMosEFTEven>().Subscribe(m =>
        {
            ActiveMOSEFT = m;
          
        });
    }

   private  ICommand _SelectCommand;
    public ICommand SelectCommand => _SelectCommand ??= new DelegateCommand(()=>
    {
        var region = regionManager.Regions[RegionNames.PrimaryViewRegion];
        region.RequestNavigate(nameof(TransistorMenu));
       
    });
}