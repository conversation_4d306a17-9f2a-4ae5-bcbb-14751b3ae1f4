<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <TargetFramework>net6.0-windows</TargetFramework>
        <UseWPF>true</UseWPF>
        <RootNamespace>MosIgbtTest</RootNamespace>
        <AssemblyName>MosIgbtTest</AssemblyName>
        <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
        <Platforms>AnyCPU;x64</Platforms>
        <Nullable>annotations</Nullable>
        <Version>1.2.0</Version>
        <LangVersion>latest</LangVersion>
    </PropertyGroup>
    <ItemGroup>
      <None Remove="mosEFTImage.png" />
      <None Update="Resources\audion.ico">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </None>
    </ItemGroup>
    <ItemGroup>
      <EmbeddedResource Include="mosEFTImage.png">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </EmbeddedResource>
    </ItemGroup>
    <ItemGroup>
        <PackageReference Include="CefSharp.Wpf.NETCore" Version="135.0.220" />
        <PackageReference Include="Google.Protobuf" Version="3.29.3" />
        <PackageReference Include="Grpc.Net.Client" Version="2.67.0" />
        <PackageReference Include="Grpc.Tools" Version="2.69.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="MaterialDesignThemes" Version="5.2.1" />
        <PackageReference Include="Prism.Core" Version="9.0.537" />
        <PackageReference Include="PropertyChanged.Fody" Version="4.1.0">
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="System.ComponentModel" Version="4.3.0" />
    </ItemGroup>
    <ItemGroup>
        <ProjectReference Include="..\..\Interfaces\DeviceGuard.Modules.Interface\DeviceGuard.Modules.Interface.csproj" />
        <ProjectReference Include="..\..\UI\DeviceGuard.Styles\DeviceGuard.Styles.csproj" />
        <ProjectReference Include="..\..\UI\DeviceGuard.Windows.Controls\DeviceGuard.Windows.Controls.csproj" />
        <ProjectReference Include="..\..\UI\DeviceGuard.Windows.Utility\DeviceGuard.Windows.Utility.csproj" />
    </ItemGroup>
    <ItemGroup>
      <Compile Update="Resource1.Designer.cs">
        <DesignTime>True</DesignTime>
        <AutoGen>True</AutoGen>
        <DependentUpon>Resource1.resx</DependentUpon>
      </Compile>
    </ItemGroup>
    <ItemGroup>
      <EmbeddedResource Update="Resource1.resx">
        <Generator>ResXFileCodeGenerator</Generator>
        <LastGenOutput>Resource1.Designer.cs</LastGenOutput>
      </EmbeddedResource>
    </ItemGroup>
    <ItemGroup>
        <None Update="manifest.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <PropertyGroup Condition="'$(PlatformTarget)' == 'x86'">
        <RuntimeIdentifier Condition="'$(RuntimeIdentifier)' == ''">win-x86</RuntimeIdentifier>
        <SelfContained Condition="'$(SelfContained)' == ''">false</SelfContained>
    </PropertyGroup>

    <PropertyGroup Condition="'$(PlatformTarget)' == 'x64'">
        <RuntimeIdentifier Condition="'$(RuntimeIdentifier)' == ''">win-x64</RuntimeIdentifier>
        <SelfContained Condition="'$(SelfContained)' == ''">false</SelfContained>
    </PropertyGroup>

    <Target Name="PostBuild" AfterTargets="PostBuildEvent">
      <Exec Command="$(SolutionDir)utils\PackModule.exe $(OutDir) $(SolutionDir)bin\$(Configuration)\$(TargetFramework)\$(RuntimeIdentifier)" />
    </Target>
</Project>