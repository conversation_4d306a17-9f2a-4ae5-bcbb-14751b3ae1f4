using System.Threading;
using System.Threading.Tasks;
using DeviceGuard.Modules.Interface;
using DeviceGuard.Modules.Interface.Impl;
using MosIgbtTest.Views;
using Prism.Navigation.Regions;

namespace MosIgbtTest;

public class MosIgbtTestModuleLoader : IModuleLoader
{
    private readonly IRegionManager _regionManager;

    public MosIgbtTestModuleLoader(IRegionManager regionManager)
    {
        _regionManager = regionManager;
    }

    public Task InitializeAsync(string? options, CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    public IModuleViewLoader[] GetViewLoaders(ModuleViewCategory viewCategory)
    {
        switch (viewCategory)
        {
            case ModuleViewCategory.Main:
                return
                [
                    // 首页
                    new DefaultModuleViewLoader(_regionManager, ModuleViewCategory.Main, "TransistorMenu", typeof(TransistorMenu).FullName!)
                ];
            case ModuleViewCategory.Setting:
            default:
                return
                [
                ];
        }
    }
}
