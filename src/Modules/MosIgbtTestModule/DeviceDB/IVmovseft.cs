using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Media.Imaging;

namespace MosIgbtTest.DeviceDB;

/// <summary>
/// VMOSEFT 器件数据定义
/// </summary>
public interface IVmovseft : IVmovseftDevice, IVmovseftTest
{
}
public interface IVmovseftDevice
{
    /// <summary>
    /// 器件ID , 
    /// ID=0 有特殊含义
    /// </summary>
    ulong ID { get; set; }
    /// <summary>
    /// 器件型号,
    /// 例如:IRFP260NPbF
    /// </summary>
    string Stype { get; set; }
    /// <summary>
    /// 品牌或制造厂家
    /// 例如:英飞凌
    /// </summary>
    string trademark { get; set; }
    /// <summary>
    /// 最大IDss漏电值单位: 微安 (UA)
    /// 条件:
    /// 1.常温下
    /// 2.额定Vds
    /// </summary>
    int IDss { get; set; }
    /// <summary>
    /// 额定Vds ,单位 V 
    /// </summary>
    int NominalVds { get; set; }
    /// <summary>
    /// 最大/额定电流 IDs
    /// </summary>
    int IDSMax { get; set; }
    /// <summary>
    /// 器件信息资料位置
    /// </summary>
    string DocURL { get;   }
    /// <summary>
    /// 简短描述, 大概几十字的描述
    /// Short description
    /// </summary>
       
    string DocDescription { get;  }
    /// <summary>
    /// 器件外观缩略图
    /// 尺寸小于: 30Kb 
    /// Jpg 格式
    /// </summary>
    public BitmapImage DocImage { get;  }
}
/// <summary>
/// 测试参数, 该参数用于测试仪器 ,不显示在用户界面
/// </summary>
public interface IVmovseftTest
{
    /// <summary>
    /// VDs 测试电压 V
    /// </summary>
    int TestVds { get; set; }
    /// <summary>
    /// 在 TestVds下的最大允许电流 单位 uA(微安)
    /// </summary>
    int CriterialIDs { get; set; }
    /// <summary>
    /// 导通降压测试电流　（A)
    /// 只有：　５Ａ，　１０Ａ，　２０Ａ　，５０Ａ
    /// </summary>
    int TestIDsOn { get; set; }
    /// <summary>
    /// 在TestIDsOn 下的最大CriterialVDs
    /// 单位 V
    /// </summary>
    int CriterialVDs { get; set; }
    /// <summary>
    /// 测试ON 时间 单位 us(微秒),一般 1000us
    /// </summary>
    int TestOnTime { get; set; }


}