using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Media.Imaging;

namespace MosIgbtTest.DeviceDB;

public class VmovsEFT : IVmovseft
{ /// <summary>
    /// 器件ID , 
    /// ID=0 有特殊含义
    /// </summary>
    //   [Display(Name ="编号",Description="ggg",ShortName ="oo")]
    [Browsable(true)]
    [ReadOnly  (true )]
    [Description ("编号" )]
    public ulong ID { get; set; }
    /// <summary>
    /// 器件型号,
    /// 例如:IRFP260NPbF
    /// </summary>
    [Description( "型号")]
    [Browsable(true)]
    public string Stype { get; set; }
    /// <summary>
    /// 品牌或制造厂家
    /// 例如:英飞凌
    /// </summary>
    [Description("厂家")]
    [Browsable(true)]
    public string trademark { get; set; }
    /// <summary>
    /// 最大IDss漏电值单位: 微安 (UA)
    /// 条件:
    /// 1.常温下
    /// 2.额定Vds
    /// </summary>
    [Description("额定漏电流(uA)")]
    [Browsable(false )]
    public int IDss { get; set; }
    /// <summary>
    /// 额定Vds ,单位 V 
    /// </summary>
    [Description("额定耐压(V)")]
    [Browsable(true)]
    public int NominalVds { get; set; }
    /// <summary>
    /// 最大/额定电流 IDs 
    /// </summary>
    [Browsable(false )]
    public int IDSMax { get; set; }

    // 以下为测试参数
    /// <summary>
    /// VDs 测试电压 V
    /// </summary> 
    [Browsable(false )]
    public int TestVds { get; set; }
    /// <summary>
    /// 在 TestVds下的最大允许电流 单位 uA(微安)
    /// </summary>
    [Browsable(false)]
    public int CriterialIDs { get; set; }
    /// <summary>
    /// 导通降压测试电流　（A)
    /// 只有：　５Ａ，　１０Ａ，　２０Ａ　，５０Ａ
    /// </summary>
    public int TestIDsOn { get; set; }
    /// <summary>
    /// 在TestIDsOn 下的最大CriterialVDs
    /// 单位 V
    /// </summary>
    public int CriterialVDs { get; set; }
    /// <summary>
    /// 测试ON 时间 单位 us(微秒)
    /// </summary>
    public int TestOnTime { get; set; }
    /// <summary>
    /// 参考资料
    /// </summary>
    [Browsable(false)]
    public string DocURL { get; set; }
    /// <summary>
    /// 简要描述
    /// </summary>
    [Browsable(false)]
    public string DocDescription { get; set; }
    /// <summary>
    /// 简图
    /// </summary>
    [Browsable(false)]
    public  BitmapImage DocImage { get; set; }
}