using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MosIgbtTest.DeviceDB;

public class DeviceDBFactory
{
    /// <summary>
    /// 数据源 , 如果DBSource ==null ji
    /// </summary>
    static object? DBSource;
    public DeviceDBFactory()
    {
        if (DBSource != null) throw new InvalidOperationException("不应在此处调用 DeviceDBFactory()");
    }
    /// <summary>
    /// 定义数据产生的来源
    /// </summary>
    /// <param name="source"></param>
    public DeviceDBFactory(object source) : base()
    {
        DBSource = source;
    }
    /// <summary>
    /// VMOSEFT 数据生成代码
    /// </summary>
    /// <returns></returns>
    public static List<VmovsEFT> CreateVMOSEFTDB()
    {

        List<VmovsEFT> dataset = new List<VmovsEFT>();
        if (DBSource != null)
        {
            //正式数据返回代码

        }
        else
        {   //一下是调试代码,正式代码应根基 DBSource生成
            dataset.Add(new VmovsEFT
            {
                ID           = 0,
                Stype        = "通用大功率MOS管",
                DocDescription= "可自定义测试参数大功率MOS管",
                trademark    = "自定义",
                IDss         = 50,
                NominalVds   = 200,
                IDSMax       = 30,
                TestVds      = 150,
                CriterialIDs = 5,
                TestIDsOn    = 5,
                CriterialVDs = 1,
                TestOnTime   = 100
            });
            dataset.Add(new VmovsEFT
            {
                ID           = 1,
                Stype        = "IRFP260NPbF",
                trademark    = "英飞凌",
                DocDescription = "英飞凌公司 N 沟道大功率MOS管",
                IDss         = 10,
                NominalVds   = 200,
                IDSMax       = 30,
                TestVds      = 150,
                CriterialIDs = 5,
                TestIDsOn    = 5,
                CriterialVDs = 1,
                TestOnTime   = 100
            });
        }
        return dataset;
    }

}