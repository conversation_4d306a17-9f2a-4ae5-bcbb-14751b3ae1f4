using DeviceGuard.Modules.Interface;
using MosIgbtTest.Views;
using Prism.Ioc;
using Prism.Modularity;

namespace MosIgbtTest;

public class MosIgbtTestModule : IModule
{
    public void OnInitialized(IContainerProvider containerProvider)
    {
    }

    public void RegisterTypes(IContainerRegistry containerRegistry)
    {
        // Bootstrap
        containerRegistry.Register<IModuleLoader, MosIgbtTestModuleLoader>();

        containerRegistry.RegisterForNavigation<TransistorMenu>(typeof(TransistorMenu).FullName);
        containerRegistry.RegisterForNavigation<MostTest>(nameof(MostTest));
        containerRegistry.RegisterForNavigation<DiodeTest>(nameof(DiodeTest));
    }
}
