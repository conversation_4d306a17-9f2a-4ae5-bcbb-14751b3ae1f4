using ChargerController.DependencyInjection;
using DeviceGuard.Interface.Prism;
using DeviceGuard.Modules.Interface;
using GbPower.IO;
using GbPower.Pages.Index;

namespace GbPower;

public class GbPowerModule : IModule
{
    public void OnInitialized(IContainerProvider containerProvider)
    {
    }

    public void RegisterTypes(IContainerRegistry containerRegistry)
    {
        // Bootstrap
        containerRegistry.Register<IModuleLoader, GbPowerModuleLoader>();

        // UI
        containerRegistry.RegisterForNavigation<GbPowerIndexView>(typeof(GbPowerIndexView).FullName);

        // IO
        containerRegistry.Register<GbPowerClient>();
        containerRegistry.RegisterComponent<ChargerControllerComponent>();
    }
}