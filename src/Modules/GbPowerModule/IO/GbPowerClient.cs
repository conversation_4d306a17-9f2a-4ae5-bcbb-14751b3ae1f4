using System.Buffers.Binary;
using System.Threading;
using ChargerController;
using ChargerController.IO;
using ChargerController.Utils;
using DeviceGuard.Interface.Cloud;
using DeviceGuard.Windows.Utility.Dialogs;

namespace GbPower.IO;

/// <summary>
/// 国标的充电模块
/// </summary>
public class GbPowerClient
{
    /// <summary>
    /// 连接检测仪的客户端
    /// </summary>
    private readonly IChargerControllerClient _controller;

    /// <summary>
    /// 连接云端许可证的客户端
    /// </summary>
    private readonly ILicenseClient _licenseClient;

    public GbPowerClient(IChargerControllerClient controller, ILicenseClient licenseClient)
    {
        _controller    = controller;
        _licenseClient = licenseClient;
    }

    /// <summary>
    /// 传输对象
    /// </summary>
    public ITransport? Transport { get; private set; }

    public ChargerPropertyValue<double?>   OutputVoltage  { get; } = new();
    public ChargerPropertyValue<double?>   OutputCurrent  { get; } = new();
    public ChargerPropertyValue<double?>   OutputPower    { get; } = new();
    public ChargerPropertyValue<double>   InputVoltageAB { get; } = new();
    public ChargerPropertyValue<double>   InputVoltageBC { get; } = new();
    public ChargerPropertyValue<double>   InputVoltageCA { get; } = new();
    public ChargerPropertyValue<byte[]>   Status         { get; } = new();
    public ChargerPropertyValue<string[]> StatusTexts    { get; } = new();
    public ChargerPropertyValue<bool>     Enabled        { get; } = new();

    /// <summary>
    /// 端口状态
    /// </summary>
    public bool IsConnected => _controller.IsOpen;

    public string PortName { get; private set; } = "";

    /// <summary>
    /// 开关机状态、电压电流设定值
    /// </summary>
    public bool PowerStatus = false;
    public double Voltage = 0;
    public double Current = 0;
    public UInt32 CmdTrough = 0;

    /// <summary>
    /// 初始化底层, 包括打开端口, 订阅数据等
    /// </summary>
    /// <param name="transport"></param>
    /// <param name="scanInterval"></param>
    /// <param name="cancellationToken"></param>
    /// <exception cref="ArgumentNullException"></exception>
    public async Task ConnectAsync(ITransport transport, uint scanInterval, CancellationToken cancellationToken)
    {
        if (transport == null) throw new ArgumentNullException(nameof(transport));

        // 创建串口客户端
        _controller.Use(transport);
        PortName  = transport.Name;
        Transport = transport;

        try
        {
            if (!await _controller.IsActivatedAsync(cancellationToken))
            {
                // 获取序列号
                var serialNumber = await _controller.GetSerialNumberAsync(cancellationToken);

                // 去云端激活
                var productKey = await _licenseClient.ActivateLicenseAsync(_controller.Sku, serialNumber, cancellationToken);

                // 把激活码写入设备
                await _controller.ActivateAsync(productKey, cancellationToken);
                throw new ReconnectException("激活完成, 请稍等测试仪重启动");
            }

            // 清理缓存
            await _controller.CleanAsync(cancellationToken);

            // 设置关闭模块的遗嘱
            await _controller.SetupWillDataAsync(0,
                CanType.Extended,
                0x18019FA0,
                [0x02, 0xFF, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00],
                125,
                10,
                cancellationToken);

            // 读取模块0x80输入电压、电流、状态、故障信息
            var subscribingId = await _controller.SubscriptDataAsync(CanType.Extended,
                1000,
                0x1820A080,
                [],
                (data) =>
                {
                    if (data.Length < 8)
                    {
                        // todo 这里报警
                        return;
                    }

                    OutputVoltage.Value = BinaryPrimitives.ReadUInt16LittleEndian(data.AsSpan(2, 2)) / 10.0;
                    OutputCurrent.Value = BinaryPrimitives.ReadUInt16LittleEndian(data.AsSpan(4, 2)) / 100.0;
                    OutputPower.Value = OutputVoltage.Value * OutputCurrent.Value;
                    if ((data[0] & 0x20) == 0x20)
                    {
                        Status.Value = [1];// 告警
                    }
                    else if ((data[0] & 0x10) == 0x10)
                    {
                        Status.Value = [2];// 故障
                    }
                    else 
                    { 
                        Status.Value = [0]; // 正常
                    }

                    if ((data[0] & 0x40) == 0x40)
                    {
                        Enabled.Value = false;// 设备待机
                    }
                    else if((data[0] & 0x80) == 0x80)
                    {
                        Enabled.Value = true;// 设备运行
                    }

                    // StatusTexts.Value = ["测试告警码"];
                    //https://kmsmg.coding.net/p/zhiliuzhuangceshiyi/files/all/45729347/preview/45729371
                    StatusTexts.Value = new List<string>()
                                        .AddWhen((data[1] & 0x01) > 0, "其他故障")
                                        .AddWhen((data[1] & 0x02) > 0, "泄放故障")
                                        .AddWhen((data[1] & 0x04) > 0, "风扇故障")
                                        .AddWhen((data[1] & 0x08) > 0, "直流输出短路故障")
                                        .AddWhen((data[1] & 0x10) > 0, "过温故障")
                                        .AddWhen((data[1] & 0x20) > 0, "直流输出欠压故障")
                                        .AddWhen((data[1] & 0x40) > 0, "直流输出过压故障")
                                        .AddWhen((data[1] & 0x80) > 0, "交流输入故障")
                                        .ToArray();
                },
                cancellationToken);
            if (subscribingId == null)
            {
                throw new Exception("订阅模块1输出电压电流、状态和故障失败");
            }

            // 读取模块0x80告警信息
            subscribingId = await _controller.SubscriptDataAsync(CanType.Extended,
                1000,
                0x1821A080,
                [],
                (data) =>
                {
                    if (data.Length < 8)
                    {
                        // todo 这里报警
                        return;
                    }

                    // StatusTexts.Value = ["测试告警码"];
                    //https://kmsmg.coding.net/p/zhiliuzhuangceshiyi/files/all/45729347/preview/45729371
                    StatusTexts.Value = new List<string>()
                                        .AddWhen((data[0] & 0x80) > 0, "充电模块地址异常故障")

                                        .AddWhen((data[1] & 0x01) > 0, "关机失败")
                                        .AddWhen((data[1] & 0x02) > 0, "启动失败")
                                        .AddWhen((data[1] & 0x04) > 0, "通信故障")
                                        .AddWhen((data[1] & 0x08) > 0, "输入缺相告警")
                                        .AddWhen((data[1] & 0x10) > 0, "输入欠压告警")
                                        .AddWhen((data[1] & 0x20) > 0, "输入过压告警")
                                        .AddWhen((data[1] & 0x40) > 0, "输出过流告警")
                                        .AddWhen((data[1] & 0x80) > 0, "过温告警")
                                        .ToArray();
                },
                cancellationToken);
            if (subscribingId == null)
            {
                throw new Exception("订阅模块1输出电压电流、状态和故障失败");
            }

            // 定时发送开关机、电压电流配置命令
            var payload = new byte[8];
            payload[0] = 0x00;
            payload[1] = 0xFF;
            payload[2] = 0;
            payload[3] = 0;
            payload[4] = 0;
            payload[5] = 0;
            payload[6] = 0;
            payload[7] = 0;

            if (Voltage > 5000)
            {
                payload[0] |= 0x10;// 低电压模式
            }
            else
            {
                payload[0] &= 0xEF;// 高电压模式
            }

            if (PowerStatus)
            {
                payload[0] &= 0xF0;
                payload[0] |= 0x01;// 开机
            }
            else {
                payload[0] &= 0xF0;
                payload[0] |= 0x02;// 关机
            }
            ushort voltageSetup = (ushort)(Voltage);
            ushort currentSetup = (ushort)(Current);
            BinaryPrimitives.WriteUInt16LittleEndian(payload.AsSpan(2), voltageSetup);
            BinaryPrimitives.WriteUInt16LittleEndian(payload.AsSpan(4), currentSetup);
            BinaryPrimitives.WriteUInt16LittleEndian(payload.AsSpan(6), voltageSetup);
            CmdTrough = (uint)await _controller.SendAsync(CanType.Extended, 0x18019FA0, payload, 125, 0, cancellationToken);// 命令包
            
            await _controller.SendAsync(CanType.Extended, 0x18409FA0, [], 500, 0, cancellationToken);// 心跳包
        }
        catch (Exception)
        {
            _controller.Close();
            throw;
        }
    }

    public async Task DisconnectAsync(CancellationToken cancellationToken)
    {
        try
        {
            await _controller.CleanAsync(cancellationToken);
        }
        catch (Exception)
        {
            // todo 发出警告, 但是忽略异常
        }
        finally
        {
            _controller.Close();
        }
    }

    public async Task SetupAsync(double voltage, double current, CancellationToken cancellationToken)
    {
        if (_controller.IsOpen == false)
        {
            throw new Exception("检测仪没打开");
        }

        Voltage = (uint)(voltage * 10);
        Current = (uint)(current * 100);
        await _controller.StopSending(CmdTrough, cancellationToken);
        var payload = new byte[8];
        payload[0] = 0x00;
        payload[1] = 0xFF;
        payload[2] = 0;
        payload[3] = 0;
        payload[4] = 0;
        payload[5] = 0;
        payload[6] = 0;
        payload[7] = 0;

        if (Voltage > 5000)
        {
            payload[0] |= 0x10;// 低电压模式
        }
        else
        {
            payload[0] &= 0xEF;// 高电压模式
        }

        if (PowerStatus)
        {
            payload[0] &= 0xF0;
            payload[0] |= 0x01;// 开机
        }
        else
        {
            payload[0] &= 0xF0;
            payload[0] |= 0x02;// 关机
        }
        ushort voltageSetup = (ushort)(Voltage);
        ushort currentSetup = (ushort)(Current);
        BinaryPrimitives.WriteUInt16LittleEndian(payload.AsSpan(2), voltageSetup);
        BinaryPrimitives.WriteUInt16LittleEndian(payload.AsSpan(4), currentSetup);
        BinaryPrimitives.WriteUInt16LittleEndian(payload.AsSpan(6), voltageSetup);
        CmdTrough = (uint)await _controller.SendAsync(CanType.Extended, 0x18019FA0, payload, 125, 0, cancellationToken);// 命令包
        //return Task.CompletedTask;
    }

    public async Task OpenAsync(CancellationToken cancellationToken)
    {
        if (_controller.IsOpen == false)
        {
            throw new Exception("检测仪没打开");
        }

        // 启动 TODO 目前没有处理回应数据
        PowerStatus = true;

        await _controller.StopSending(CmdTrough, cancellationToken);
        var payload = new byte[8];
        payload[0] = 0x00;
        payload[1] = 0xFF;
        payload[2] = 0;
        payload[3] = 0;
        payload[4] = 0;
        payload[5] = 0;
        payload[6] = 0;
        payload[7] = 0;

        if (Voltage > 5000)
        {
            payload[0] |= 0x10;// 低电压模式
        }
        else
        {
            payload[0] &= 0xEF;// 高电压模式
        }

        if (PowerStatus)
        {
            payload[0] &= 0xF0;
            payload[0] |= 0x01;// 开机
        }
        else
        {
            payload[0] &= 0xF0;
            payload[0] |= 0x02;// 关机
        }
        ushort voltageSetup = (ushort)(Voltage);
        ushort currentSetup = (ushort)(Current);
        BinaryPrimitives.WriteUInt16LittleEndian(payload.AsSpan(2), voltageSetup);
        BinaryPrimitives.WriteUInt16LittleEndian(payload.AsSpan(4), currentSetup);
        BinaryPrimitives.WriteUInt16LittleEndian(payload.AsSpan(6), voltageSetup);
        CmdTrough = (uint)await _controller.SendAsync(CanType.Extended, 0x18019FA0, payload, 125, 0, cancellationToken);// 命令包

        //return Task.CompletedTask;
    }

    public async Task CloseAsync(CancellationToken cancellationToken)
    {
        if (_controller.IsOpen == false)
        {
            throw new Exception("检测仪没打开");
        }

        PowerStatus = false;
        await _controller.StopSending(CmdTrough, cancellationToken);
        var payload = new byte[8];
        payload[0] = 0x00;
        payload[1] = 0xFF;
        payload[2] = 0;
        payload[3] = 0;
        payload[4] = 0;
        payload[5] = 0;
        payload[6] = 0;
        payload[7] = 0;

        if (Voltage > 5000)
        {
            payload[0] |= 0x10;// 低电压模式
        }
        else
        {
            payload[0] &= 0xEF;// 高电压模式
        }

        if (PowerStatus)
        {
            payload[0] &= 0xF0;
            payload[0] |= 0x01;// 开机
        }
        else
        {
            payload[0] &= 0xF0;
            payload[0] |= 0x02;// 关机
        }
        ushort voltageSetup = (ushort)(Voltage);
        ushort currentSetup = (ushort)(Current);
        BinaryPrimitives.WriteUInt16LittleEndian(payload.AsSpan(2), voltageSetup);
        BinaryPrimitives.WriteUInt16LittleEndian(payload.AsSpan(4), currentSetup);
        BinaryPrimitives.WriteUInt16LittleEndian(payload.AsSpan(6), voltageSetup);
        CmdTrough = (uint)await _controller.SendAsync(CanType.Extended, 0x18019FA0, payload, 125, 0, cancellationToken);// 命令包
        //return Task.CompletedTask;
    }
}
