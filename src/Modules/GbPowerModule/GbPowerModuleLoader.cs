using DeviceGuard.Modules.Interface;
using DeviceGuard.Modules.Interface.Impl;
using GbPower.Pages.Index;

namespace GbPower;

public class GbPowerModuleLoader : IModuleLoader
{
    private readonly IRegionManager _regionManager;

    public GbPowerModuleLoader(IRegionManager regionManager)
    {
        _regionManager = regionManager;
    }

    public Task InitializeAsync(string? options, CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    public IModuleViewLoader[] GetViewLoaders(ModuleViewCategory viewCategory)
    {
        switch (viewCategory)
        {
            case ModuleViewCategory.Main:
                return
                [
                    // 首页
                    new DefaultModuleViewLoader(_regionManager, ModuleViewCategory.Main, "GbPower", typeof(GbPowerIndexView).FullName!)
                ];
            case ModuleViewCategory.Setting:
            default:
                return
                [
                ];
        }
    }
}
