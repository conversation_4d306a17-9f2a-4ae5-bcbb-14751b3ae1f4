using System.Buffers.Binary;
using ChargerController;
using ChargerController.IO;
using ChargerController.Utils;
using DeviceGuard.Interface.Cloud;

namespace UUGreenPower.IO;

/// <summary>
/// 优优绿能的充电模块
/// </summary>
public class UUGreenPowerClient
{
    private readonly IChargerControllerClient _client;
    private readonly ILicenseClient           _licenseClient;

    public UUGreenPowerClient(IChargerControllerClient client, ILicenseClient licenseClient)
    {
        _client        = client;
        _licenseClient = licenseClient;
    }
    
    /// <summary>
    /// 传输对象
    /// </summary>
    public ITransport? Transport { get; private set; }


    public ChargerPropertyValue<double?>   OutputVoltage  { get; } = new();
    public ChargerPropertyValue<double?>   OutputCurrent  { get; } = new();
    public ChargerPropertyValue<double?>   OutputPower    { get; } = new();
    public ChargerPropertyValue<double>   InputVoltageAB { get; } = new();
    public ChargerPropertyValue<double>   InputVoltageBC { get; } = new();
    public ChargerPropertyValue<double>   InputVoltageCA { get; } = new();
    public ChargerPropertyValue<byte[]>   Status         { get; } = new();
    public ChargerPropertyValue<string[]> StatusTexts    { get; } = new();
    public ChargerPropertyValue<bool>     Enabled        { get; } = new();

    /// <summary>
    /// 端口状态
    /// </summary>
    public bool IsConnected => _client.IsOpen;

    public string PortName { get; private set; } = "";

    /// <summary>
    /// 初始化底层, 包括打开端口, 订阅数据等
    /// </summary>
    /// <param name="transport"></param>
    /// <param name="scanInterval"></param>
    /// <param name="cancellationToken"></param>
    /// <exception cref="ArgumentNullException"></exception>
    public async Task ConnectAsync(ITransport transport, uint scanInterval, CancellationToken cancellationToken)
    {
        if (transport == null) throw new ArgumentNullException(nameof(transport));

        // 创建串口客户端
        _client.Use(transport);
        PortName  = transport.Name;
        Transport = transport;

        try
        {
            if (!await _client.IsActivatedAsync(cancellationToken))
            {
                // 获取序列号
                var serialNumber = await _client.GetSerialNumberAsync(cancellationToken);

                // 去云端激活
                var productKey = await _licenseClient.ActivateLicenseAsync(_client.Sku, serialNumber, cancellationToken);

                // 把激活码写入设备
                await _client.ActivateAsync(productKey, cancellationToken);
                throw new ReconnectException("激活完成, 请稍等测试仪重启动");
            }

            // 清理缓存
            await _client.CleanAsync(cancellationToken);

            // 设置关闭模块的遗嘱
            await _client.SetupWillDataAsync(0,
                CanType.Extended,
                0x02204200,
                [0x10, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01],
                1000,
                10,
                cancellationToken);

            // 读取模块1输出电压 
            var subscribingId = await _client.SubscriptDataAsync(CanType.Extended,
                0x02204200,
                [0x12, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00],
                scanInterval,
                0x02204200,
                [0x13, 0x00],
                (data) =>
                {
                    if (data.Length < 8)
                    {
                        // todo 这里报警
                        return;
                    }

                    OutputVoltage.Value = BinaryPrimitives.ReadUInt32LittleEndian(data.AsSpan(4, 4)) / 1000.0;
                    OutputPower.Value   = OutputVoltage.Value * OutputCurrent.Value;
                },
                cancellationToken);
            if (subscribingId == null)
            {
                throw new Exception("订阅模块1输出电压失败");
            }

            // 读取模块1输出电流
            subscribingId = await _client.SubscriptDataAsync(CanType.Extended,
                0x02204200,
                [0x12, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00],
                scanInterval,
                0x02204200,
                [0x13, 0x01],
                (data) =>
                {
                    if (data.Length < 8)
                    {
                        // todo 这里报警
                        return;
                    }

                    OutputCurrent.Value = BinaryPrimitives.ReadUInt32LittleEndian(data.AsSpan(4, 4)) / 1000.0;
                    OutputPower.Value   = OutputVoltage.Value * OutputCurrent.Value;
                },
                cancellationToken);
            if (subscribingId == null)
            {
                throw new Exception("订阅模块1输出电压失败");
            }

            // 读取模块1的输入电压Vab
            subscribingId = await _client.SubscriptDataAsync(CanType.Extended,
                0x02204200,
                [0x12, 0x14, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00],
                scanInterval,
                0x02204200,
                [0x13, 0x14],
                (data) =>
                {
                    if (data.Length < 8)
                    {
                        // todo 这里报警
                        return;
                    }

                    InputVoltageAB.Value = BinaryPrimitives.ReadUInt32LittleEndian(data.AsSpan(4, 4)) / 1000.0;
                },
                cancellationToken);
            if (subscribingId == null)
            {
                throw new Exception("订阅模块1输入电压Vab失败");
            }

            // 读取模块1的输入电压Vbc
            subscribingId = await _client.SubscriptDataAsync(CanType.Extended,
                0x02204200,
                [0x12, 0x15, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00],
                scanInterval,
                0x02204200,
                [0x13, 0x15],
                (data) =>
                {
                    if (data.Length < 8)
                    {
                        // todo 这里报警
                        return;
                    }

                    InputVoltageBC.Value = BinaryPrimitives.ReadUInt32LittleEndian(data.AsSpan(4, 4)) / 1000.0;
                },
                cancellationToken);
            if (subscribingId == null)
            {
                throw new Exception("订阅模块1输入电压Vbc失败");
            }

            // 读取模块1的输入电压Vca
            subscribingId = await _client.SubscriptDataAsync(CanType.Extended,
                0x02204200,
                [0x12, 0x16, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00],
                scanInterval,
                0x02204200,
                [0x13, 0x16],
                (data) =>
                {
                    if (data.Length < 8)
                    {
                        // todo 这里报警
                        return;
                    }

                    InputVoltageCA.Value = BinaryPrimitives.ReadUInt32LittleEndian(data.AsSpan(4, 4)) / 1000.0;
                },
                cancellationToken);
            if (subscribingId == null)
            {
                throw new Exception("订阅模块1输入电压Vca失败");
            }

            // 读取模块1状态、故障
            subscribingId = await _client.SubscriptDataAsync(CanType.Extended,
                0x02204200,
                [0x12, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00],
                scanInterval,
                0x02204200,
                [0x13, 0x08],
                (data) =>
                {
                    if (data.Length < 8)
                    {
                        // todo 这里报警
                        return;
                    }

                    Status.Value      = data.Skip(4).ToArray();
                    Enabled.Value     = (data[4] & 0x02) == 0x00;
                    // StatusTexts.Value = ["测试告警码"];
                    // https://kmsmg.coding.net/p/zhiliuzhuangceshiyi/files/all/46741530/preview/46741532
                    StatusTexts.Value = new List<string>()
                                       .AddWhen((data[7] & 0x01) > 0, "交流过压")
                                       .AddWhen((data[7] & 0x02) > 0, "交流欠压")
                                       .AddWhen((data[7] & 0x04) > 0, "交流过压脱离(交流过压关机)")
                                       .AddWhen((data[7] & 0x08) > 0, "PFC 母线过压")
                                       .AddWhen((data[7] & 0x10) > 0, "PFC 母线欠压")
                                       .AddWhen((data[7] & 0x20) > 0, "PFC 母线不平衡")
                                       .AddWhen((data[7] & 0x40) > 0, "直流输出过压")
                                       .AddWhen((data[7] & 0x80) > 0, "直流过压关机")

                                       .AddWhen((data[6] & 0x01) > 0, "直流输出欠压")
                                       .AddWhen((data[6] & 0x02) > 0, "风扇不运行")
                                       //.AddWhen((data[6] & 0x04) > 0, "预留")
                                       .AddWhen((data[6] & 0x08) > 0, "风扇驱动电路损坏")
                                       .AddWhen((data[6] & 0x10) > 0, "环境过温")
                                       .AddWhen((data[6] & 0x20) > 0, "环境温度过低")
                                       .AddWhen((data[6] & 0x40) > 0, "PFC 过温保护")
                                       .AddWhen((data[6] & 0x80) > 0, "输出继电器故障")

                                       .AddWhen((data[5] & 0x01) > 0, "DC 过温保护")
                                       //.AddWhen((data[5] & 0x02) > 0, "预留")
                                       .AddWhen((data[5] & 0x04) > 0, "PFC 与 DCDC 通信故障")
                                       //.AddWhen((data[5] & 0x08) > 0, "预留")
                                       .AddWhen((data[5] & 0x10) > 0, "PFC 故障")
                                       .AddWhen((data[5] & 0x20) > 0, "DCDC 故障")
                                       //.AddWhen((data[5] & 0x40) > 0, "预留")
                                       //.AddWhen((data[5] & 0x80) > 0, "预留")

                                       //.AddWhen((data[4] & 0x01) > 0, "预留")
                                       .AddWhen((data[4] & 0x02) > 0, "DCDC 不运行")

                                       //.AddWhen((data[4] >> 2 & 0x03) == 0, "电压环")
                                       //.AddWhen((data[4] >> 2 & 0x03) == 2, "电流环")

                                       .AddWhen((data[4] & 0x10) > 0, "DC 输出电压不平衡")
                                       .AddWhen((data[4] & 0x20) > 0, "发现相同序列号的模块")
                                       //.AddWhen((data[4] & 0x40) > 0, "预留")
                                       .AddWhen((data[4] & 0x80) > 0, "泄放电路异常")
                                       .ToArray();
                },
                cancellationToken);
            if (subscribingId == null)
            {
                throw new Exception("订阅模块1状态和故障失败");
            }
        }
        catch (Exception)
        {
            _client.Close();
            throw;
        }
    }

    public async Task DisconnectAsync(CancellationToken cancellationToken)
    {
        try
        {
            await _client.CleanAsync(cancellationToken);
        }
        catch (Exception)
        {
            // todo 发出警告, 但是忽略异常
        }
        finally
        {
            _client.Close();
        }
    }

    public async Task SetupAsync(double voltage, double current, CancellationToken cancellationToken)
    {
        if (_client.IsOpen == false)
        {
            throw new Exception("检测仪没打开");
        }

        ushort voltageSetup = (ushort)(voltage * 1000);
        ushort currentSetup = (ushort)(current * 1000);

        var payload = new byte[8];
        payload[0] = 0x10;
        payload[1] = 0x02;
        // 将 voltageSetup 存到从 4 字节开始的 4 字节空间
        BinaryPrimitives.WriteUInt32LittleEndian(payload.AsSpan(4), (uint)voltageSetup);

        // 设置电压 TODO 目前没有处理回应数据
        await _client.SendAsync(CanType.Extended, 0x02204200, payload, cancellationToken);

        payload[0] = 0x10;
        payload[1] = 0x03;
        // 将 currentSetup 存到从 4 字节开始的 4 字节空间
        BinaryPrimitives.WriteUInt32LittleEndian(payload.AsSpan(4), (uint)currentSetup);

        // 设置电流 TODO 目前没有处理回应数据
        await _client.SendAsync(CanType.Extended, 0x02204200, payload, cancellationToken);
    }

    public async Task OpenAsync(CancellationToken cancellationToken)
    {
        if (_client.IsOpen == false)
        {
            throw new Exception("检测仪没打开");
        }

        // 启动 TODO 目前没有处理回应数据
        var result = await _client.SendAsync(CanType.Extended, 0x02204200, [0x10, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00], cancellationToken);
        if (!result)
        {
            throw new Exception("启动失败");
        }
    }

    public async Task CloseAsync(CancellationToken cancellationToken)
    {
        if (_client.IsOpen == false)
        {
            throw new Exception("检测仪没打开");
        }

        var result = await _client.SendAsync(CanType.Extended, 0x02204200, [0x10, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01], cancellationToken);
        if (!result)
        {
            throw new Exception("停止失败");
        }
    }
}
