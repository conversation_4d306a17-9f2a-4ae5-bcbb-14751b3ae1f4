using ChargerController.DependencyInjection;
using DeviceGuard.Interface.Prism;
using DeviceGuard.Modules.Interface;
using UUGreenPower.IO;
using UUGreenPower.Pages.Index;

namespace UUGreenPower;

public class UUGreenPowerModule : IModule
{
    public void OnInitialized(IContainerProvider containerProvider)
    {
    }

    public void RegisterTypes(IContainerRegistry containerRegistry)
    {
        // Bootstrap
        containerRegistry.Register<IModuleLoader, UUGreenPowerModuleLoader>();

        // UI
        containerRegistry.RegisterForNavigation<UUGreenPowerIndexView>(typeof(UUGreenPowerIndexView).FullName);

        // IO
        containerRegistry.Register<UUGreenPowerClient>();
        containerRegistry.RegisterComponent<ChargerControllerComponent>();
    }
}