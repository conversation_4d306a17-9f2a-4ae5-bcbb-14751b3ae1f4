using ChargerController;
using Newtonsoft.Json;
using Oulida.Configuration;

namespace UUGreenPower.Configs;

public class UUGreenPowerConfig : ConfigBase
{
    /// <summary>
    /// 构造函数，
    /// </summary>
    /// <param name="config">强命名该分组</param>
    public UUGreenPowerConfig(Config config) : base(config)
    {
    }

    #region Overrides of ConfigBase

    /// <summary>
    /// 定义作用域(包)
    /// 在多个配置文件合并的时候，用来区分不同的作用域
    /// </summary>
    public override string PathName => "Modules.UUGreenPower";

    #endregion

    /// <summary>
    /// 最后打开的模块名
    /// </summary>
    public CFDevice? LastSelectionPort
    {
        get
        {
            try
            {
                var json = GetPropertyValue("{}");
                return JsonConvert.DeserializeObject<CFDevice>(json);
            }
            catch
            {
                // No code here
            }

            return null;
        }
        set
        {
            try
            {
                var json = value == null ? "{}" : JsonConvert.SerializeObject(value);
                SetPropertyValue(json);
            }
            catch
            {
                // No code here
            }
        }
    }

    /// <summary>
    /// 最后扫描的端口
    /// 仅用来进入时快速显示
    /// </summary>
    public CFDevice[] LastPorts
    {
        get
        {
            try
            {
                var json = GetPropertyValue("[]");
                return JsonConvert.DeserializeObject<CFDevice[]>(json) ?? [];
            }
            catch
            {
                // No code here
            }

            return [];
        }
        set
        {
            try
            {
                var json = JsonConvert.SerializeObject(value);
                SetPropertyValue(json);
            }
            catch
            {
                // No code here
            }
        }
    }
}
