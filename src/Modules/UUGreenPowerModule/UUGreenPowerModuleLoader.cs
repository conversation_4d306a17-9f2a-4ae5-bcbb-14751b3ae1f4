using DeviceGuard.Modules.Interface;
using DeviceGuard.Modules.Interface.Impl;
using UUGreenPower.Pages.Index;

namespace UUGreenPower;

public class UUGreenPowerModuleLoader : IModuleLoader
{
    private readonly IRegionManager _regionManager;

    public UUGreenPowerModuleLoader(IRegionManager regionManager)
    {
        _regionManager = regionManager;
    }

    public Task InitializeAsync(string? options, CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    public IModuleViewLoader[] GetViewLoaders(ModuleViewCategory viewCategory)
    {
        switch (viewCategory)
        {
            case ModuleViewCategory.Main:
                return
                [
                    // 首页
                    new DefaultModuleViewLoader(_regionManager, ModuleViewCategory.Main, "UUGreenPower", typeof(UUGreenPowerIndexView).FullName!)
                ];
            case ModuleViewCategory.Setting:
            default:
                return
                [
                ];
        }
    }
}
