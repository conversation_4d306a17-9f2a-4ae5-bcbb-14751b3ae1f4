<UserControl x:Class="UUGreenPower.Pages.Index.UUGreenPowerIndexView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:conv="clr-namespace:DeviceGuard.Windows.Controls.Conv;assembly=DeviceGuard.Windows.Controls"
             xmlns:index="clr-namespace:UUGreenPower.Pages.Index"
             xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
             mc:Ignorable="d"
             d:DesignHeight="450"
             d:DesignWidth="800"
             d:DataContext="{d:DesignInstance index:UUGreenPowerIndexViewModel }">
    <UserControl.Resources>
        <!--Default style-->
        <ResourceDictionary Source="pack://application:,,,/DeviceGuard.Styles;component/AppStyle.xaml" />
    </UserControl.Resources>
    <Grid>
        <Grid.Resources>
            <conv:BooleanToTextConverter x:Key="ExpiredToForegroundConverter" True="LightGray" False="Black" />
            <materialDesign:InvertBooleanConverter x:Key="InverseBooleanConverter" />
        </Grid.Resources>
        <ScrollViewer IsDeferredScrollingEnabled="True">
            <Grid Margin="20">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                    <ColumnDefinition Width="*"></ColumnDefinition>
                </Grid.ColumnDefinitions>
                <StackPanel Grid.Column="0" MinWidth="500">
                    <materialDesign:Card Padding="30" Margin="10">
                        <StackPanel>
                            <TextBlock Margin="0 0 0 20"
                                       Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                       Text="连接测试仪" />

                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="1*"></ColumnDefinition>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Row="0" Grid.Column="0" Text="选择端口" />
                                <ComboBox Grid.Row="1" Grid.Column="0" Margin="0 10 0 0"
                                          ItemsSource="{Binding AllPorts}"
                                          SelectedItem="{Binding SelectedPort}"
                                          IsEditable="False">
                                    <ComboBox.ItemTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding Name}" />
                                        </DataTemplate>
                                    </ComboBox.ItemTemplate>
                                </ComboBox>
                                <Button Grid.Row="1" Grid.Column="1"
                                        VerticalAlignment="Bottom"
                                        Margin="10 0 0 0"
                                        Padding="0"
                                        Width="30"
                                        Height="30"
                                        Command="{Binding DiscoverCommand }"
                                        Style="{StaticResource MaterialDesignIconForegroundButton}">
                                    <materialDesign:PackIcon
                                        Kind="Refresh" />
                                </Button>
                            </Grid>
                            <!-- 启动和停止按钮 -->
                            <StackPanel Orientation="Horizontal"
                                        HorizontalAlignment="Left" Margin="0 10">
                                <Button Content="连接"
                                        Command="{Binding ConnectCommand }"
                                        Style="{StaticResource MaterialDesignRaisedButton}" />
                                <Button Content="断开"
                                        Command="{Binding DisconnectCommand }"
                                        Style="{StaticResource MaterialDesignRaisedButton}" Margin="10,0,0,0" />
                                <TextBlock VerticalAlignment="Center" Foreground="DimGray" Margin="20 0 0 0"
                                           Text="{Binding PortStatus}">
                                </TextBlock>
                            </StackPanel>
                        </StackPanel>
                    </materialDesign:Card>

                    <materialDesign:Card Padding="30" Margin="10">
                        <StackPanel>
                            <TextBlock Margin="0 0 0 20"
                                       Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                       Text="配置电源输出" />

                            <Grid Margin="10">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"></ColumnDefinition>
                                    <ColumnDefinition Width="40"></ColumnDefinition>
                                    <ColumnDefinition Width="*"></ColumnDefinition>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="电压设定 (V)" />
                                    <materialDesign:NumericUpDown Margin="0 10 0 0"
                                                                  Value="{Binding OutputVoltageSetup}" />
                                </StackPanel>
                                <StackPanel Grid.Column="2">
                                    <TextBlock Text="电流设定 (A)" />
                                    <materialDesign:NumericUpDown Margin="0 10 0 0"
                                                                  Value="{Binding OutputCurrentSetup}" />
                                </StackPanel>
                            </Grid>

                            <!-- 启动和停止按钮 -->
                            <Grid
                                Margin="0 10 0 0">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    <ColumnDefinition Width="*"></ColumnDefinition>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                </Grid.ColumnDefinitions>
                                <Button Grid.Column="0"
                                        Content="保存至电源"
                                        IsEnabled="{Binding IsConnected}"
                                        Command="{Binding SetupCommand }"
                                        Style="{StaticResource MaterialDesignRaisedButton}" />
                                <Button Grid.Column="2"
                                        Content="启动电源" Background="Green" Foreground="White"
                                        IsEnabled="{Binding IsConnected}"
                                        Command="{Binding StartCommand }"
                                        Style="{StaticResource MaterialDesignRaisedButton}" Margin="10,0,0,0" />
                                <Button Grid.Column="3"
                                        Content="关闭电源" Background="Red" Foreground="White"
                                        IsEnabled="{Binding IsConnected}"
                                        Command="{Binding StopCommand }"
                                        Style="{StaticResource MaterialDesignRaisedButton}" Margin="10,0,0,0" />
                            </Grid>
                        </StackPanel>
                    </materialDesign:Card>

                    <materialDesign:Card Grid.Row="1" Grid.Column="0" Padding="30" Margin="10">
                        <StackPanel>
                            <TextBlock Margin="0 0 0 20"
                                       Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                       Text="电源状态" />

                            <!-- 右侧电压/电流显示 -->
                            <Grid Grid.Column="2" Grid.Row="2" Margin="10">
                                <Grid.RowDefinitions>
                                    <RowDefinition />
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    <ColumnDefinition Width="*"></ColumnDefinition>
                                </Grid.ColumnDefinitions>
                                <Grid.Resources>
                                    <Style TargetType="TextBlock">
                                        <Setter Property="VerticalAlignment" Value="Center"></Setter>
                                        <Setter Property="FontSize" Value="14"></Setter>
                                        <Setter Property="Margin" Value="0 10 20 10"></Setter>
                                    </Style>
                                    <Style TargetType="TextBox" BasedOn="{StaticResource MaterialDesignTextBox }">
                                        <Setter Property="TextAlignment" Value="Right"></Setter>
                                        <Setter Property="VerticalAlignment" Value="Center"></Setter>
                                        <Setter Property="IsReadOnly" Value="True"></Setter>
                                    </Style>
                                </Grid.Resources>
                                <TextBlock Grid.Row="0" Grid.Column="0" Text="运行状态" />
                                <TextBox Grid.Row="0" Grid.Column="1" Text="{Binding RunStatus}"
                                         Foreground="{Binding RunStatusExpired, Converter={StaticResource ExpiredToForegroundConverter}}" />
                                <TextBlock Grid.Row="1" Grid.Column="0" Text="当前电压 (V)" />
                                <TextBox Grid.Row="1" Grid.Column="1" Text="{Binding OutputVoltage}"
                                         Foreground="{Binding OutputVoltageExpired, Converter={StaticResource ExpiredToForegroundConverter}}" />
                                <TextBlock Grid.Row="2" Grid.Column="0" Text="当前电流 (A)" />
                                <TextBox Grid.Row="2" Grid.Column="1" Text="{Binding OutputCurrent}"
                                         Foreground="{Binding OutputCurrentExpired, Converter={StaticResource ExpiredToForegroundConverter}}" />

                                <!-- 右侧输入电压 -->
                                <TextBlock Grid.Row="3" Grid.Column="0" Text="输入电压AB (V)" />
                                <TextBox Grid.Row="3" Grid.Column="1" Text="{Binding InputVoltageAB}"
                                         Foreground="{Binding InputVoltageABExpired, Converter={StaticResource ExpiredToForegroundConverter}}" />
                                <TextBlock Grid.Row="4" Grid.Column="0" Text="输入电压BC (V)" />
                                <TextBox Grid.Row="4" Grid.Column="1" Text="{Binding InputVoltageBC}"
                                         Foreground="{Binding InputVoltageBCExpired, Converter={StaticResource ExpiredToForegroundConverter}}" />
                                <TextBlock Grid.Row="5" Grid.Column="0" Text="输入电压CA (V)" />
                                <TextBox Grid.Row="5" Grid.Column="1"
                                         Text="{Binding InputVoltageCA}"
                                         Foreground="{Binding InputVoltageCAExpired, Converter={StaticResource ExpiredToForegroundConverter}}" />
                                <TextBlock VerticalAlignment="Top" Grid.Row="6" Grid.Column="0" Text="告警代码" />
                                <Grid Grid.Row="6" Grid.Column="1">
                                    <Grid.Resources>
                                        <conv:BooleanToTextConverter x:Key="NoErrorForegroundConverter"
                                                                     True="LightGray" False="Gray">
                                        </conv:BooleanToTextConverter>
                                    </Grid.Resources>
                                    <!-- NO DATA 占位符 -->
                                    <TextBlock Text="无告警代码"
                                               Foreground="{Binding StatusTextsExpired,Converter={StaticResource NoErrorForegroundConverter}}"
                                               FontStyle="Italic"
                                               HorizontalAlignment="Center"
                                               VerticalAlignment="Center"
                                               FontSize="14">
                                        <TextBlock.Style>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="Visibility" Value="Collapsed" />
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding StatusTexts.Length}" Value="0">
                                                        <Setter Property="Visibility" Value="Visible" />
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </TextBlock.Style>
                                    </TextBlock>
                                    <ItemsControl
                                        BorderBrush="Gray"
                                        BorderThickness="1"
                                        Padding="5"
                                        Margin="0 8 0 0"
                                        MinHeight="120"
                                        ItemsSource="{Binding StatusTexts}">
                                        <ItemsControl.Resources>
                                            <conv:BooleanToTextConverter x:Key="ErrorStatusForegroundConverter"
                                                                         True="LightGray" False="Red">
                                            </conv:BooleanToTextConverter>
                                        </ItemsControl.Resources>
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <TextBlock Text="{Binding}"
                                                           Foreground="{Binding DataContext.StatusTextsExpired, RelativeSource={RelativeSource FindAncestor, AncestorType=UserControl}, Converter={StaticResource ErrorStatusForegroundConverter}}"
                                                           FontSize="13"
                                                           Margin="0 0 0 2" />
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </Grid>
                            </Grid>
                        </StackPanel>
                    </materialDesign:Card>
                </StackPanel>
                <Grid Grid.Column="1">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"></RowDefinition>
                        <RowDefinition Height="*"></RowDefinition>
                    </Grid.RowDefinitions>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"></ColumnDefinition>
                            <ColumnDefinition Width="*"></ColumnDefinition>
                        </Grid.ColumnDefinitions>
                        <materialDesign:Card Padding="30 20" Margin="10">
                            <StackPanel>
                                <TextBlock Margin="0 0 0 12">连接状态</TextBlock>
                                <StackPanel Orientation="Horizontal">
                                    <StackPanel.Resources>
                                        <Style TargetType="Border">
                                            <Setter Property="Width" Value="36"></Setter>
                                            <Setter Property="Height" Value="36"></Setter>
                                            <Setter Property="BorderThickness" Value="0"></Setter>
                                            <Setter Property="CornerRadius" Value="18"></Setter>
                                            <Setter Property="VerticalAlignment" Value="Center"></Setter>
                                            <Setter Property="HorizontalAlignment" Value="Center"></Setter>
                                        </Style>
                                        <Style TargetType="materialDesign:PackIcon" x:Key="Node">
                                            <Setter Property="Width" Value="20"></Setter>
                                            <Setter Property="Height" Value="20"></Setter>
                                            <Setter Property="Foreground" Value="White"></Setter>
                                            <Setter Property="VerticalAlignment" Value="Center"></Setter>
                                            <Setter Property="HorizontalAlignment" Value="Center"></Setter>
                                        </Style>
                                        <Style TargetType="materialDesign:PackIcon" x:Key="Dot">
                                            <Setter Property="VerticalAlignment" Value="Center"></Setter>
                                            <Setter Property="Width" Value="20"></Setter>
                                            <Setter Property="Height" Value="20"></Setter>
                                        </Style>
                                        <Style TargetType="materialDesign:PackIcon" x:Key="Conn">
                                            <Setter Property="VerticalAlignment" Value="Center"></Setter>
                                            <Setter Property="Width" Value="20"></Setter>
                                            <Setter Property="Height" Value="20"></Setter>
                                        </Style>
                                        <conv:BooleanToTextConverter x:Key="ConnectedConverter" True="Green"
                                                                     False="LightGray">
                                        </conv:BooleanToTextConverter>
                                    </StackPanel.Resources>
                                    <Border
                                        ToolTip="DEVICE GUARD"
                                        Background="{Binding ConnectedToTester, Converter={StaticResource ConnectedConverter}}">
                                        <materialDesign:PackIcon Kind="ApplicationOutline"
                                                                 Style="{StaticResource Node}" />
                                    </Border>
                                    <materialDesign:PackIcon Kind="DotsHorizontal"
                                                             Foreground="{Binding ConnectedToTester, Converter={StaticResource ConnectedConverter}}"
                                                             Style="{StaticResource Dot}" />
                                    <materialDesign:PackIcon Kind="UsbPort"
                                                             Visibility="{Binding ConnectedByUsb, Converter={StaticResource DisplayTrueElseCollapsedConverter}}"
                                                             Foreground="{Binding ConnectedToTester, Converter={StaticResource ConnectedConverter}}"
                                                             Style="{StaticResource Conn}" />
                                    <materialDesign:PackIcon Kind="Bluetooth"
                                                             Visibility="{Binding ConnectedByBluetooth, Converter={StaticResource DisplayTrueElseCollapsedConverter}}"
                                                             Foreground="{Binding ConnectedToTester, Converter={StaticResource ConnectedConverter}}"
                                                             Style="{StaticResource Conn}" />
                                    <materialDesign:PackIcon Kind="Close"
                                                             Visibility="{Binding ConnectedToTester, Converter={StaticResource DisplayFalseElseCollapsedConverter}}"
                                                             Foreground="{Binding ConnectedToTester, Converter={StaticResource ConnectedConverter}}"
                                                             Style="{StaticResource Conn}" />
                                    <materialDesign:PackIcon Kind="DotsHorizontal"
                                                             Foreground="{Binding ConnectedToTester, Converter={StaticResource ConnectedConverter}}"
                                                             Style="{StaticResource Dot}" />
                                    <Border
                                        Background="{Binding ConnectedToTester, Converter={StaticResource ConnectedConverter}}"
                                        ToolTip="检测仪">
                                        <materialDesign:PackIcon Kind="Stethoscope"
                                                                 Style="{StaticResource Node}" />
                                    </Border>
                                    <materialDesign:PackIcon Kind="DotsHorizontal"
                                                             Foreground="{Binding ConnectedToTarget, Converter={StaticResource ConnectedConverter}}"
                                                             Style="{StaticResource Dot}" />
                                    <materialDesign:PackIcon Kind="Check"
                                                             Visibility="{Binding ConnectedToTarget, Converter={StaticResource DisplayTrueElseCollapsedConverter}}"
                                                             Foreground="{Binding ConnectedToTarget, Converter={StaticResource ConnectedConverter}}"
                                                             Style="{StaticResource Conn}" />
                                    <materialDesign:PackIcon Kind="Close"
                                                             Visibility="{Binding ConnectedToTarget, Converter={StaticResource DisplayFalseElseCollapsedConverter}}"
                                                             Foreground="{Binding ConnectedToTarget, Converter={StaticResource ConnectedConverter}}"
                                                             Style="{StaticResource Conn}" />
                                    <materialDesign:PackIcon Kind="DotsHorizontal"
                                                             Foreground="{Binding ConnectedToTarget, Converter={StaticResource ConnectedConverter}}"
                                                             Style="{StaticResource Dot}" />
                                    <Border
                                        Background="{Binding ConnectedToTarget, Converter={StaticResource ConnectedConverter}}"
                                        ToolTip="被测设备">
                                        <materialDesign:PackIcon Kind="ServerOutline"
                                                                 Style="{StaticResource Node}" />
                                    </Border>
                                </StackPanel>
                            </StackPanel>
                        </materialDesign:Card>
                        <materialDesign:Card Grid.Column="1" Padding="30 20" Margin="10">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                    <RowDefinition Height="Auto"></RowDefinition>
                                </Grid.RowDefinitions>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    <ColumnDefinition Width="*"></ColumnDefinition>
                                </Grid.ColumnDefinitions>
                                <TextBlock Grid.Row="0" Grid.Column="0"
                                           Margin="0 0 0 12">
                                    电压
                                </TextBlock>
                                <TextBlock Grid.Row="1" Grid.Column="0" VerticalAlignment="Center"
                                           Foreground="{Binding OutputCurrentExpired, Converter={StaticResource ExpiredToForegroundConverter}}"
                                           Margin="0 0 50 0">
                                    <Run Text="{Binding OutputVoltage, TargetNullValue=--, StringFormat=F1}"
                                         FontSize="24">
                                    </Run>
                                    <Run Text="V"></Run>
                                </TextBlock>
                                <TextBlock Grid.Row="0" Grid.Column="1">电流</TextBlock>
                                <TextBlock Grid.Row="1" Grid.Column="1" VerticalAlignment="Center"
                                           Foreground="{Binding OutputCurrentExpired, Converter={StaticResource ExpiredToForegroundConverter}}"
                                           Margin="0 0 50 0">
                                    <Run Text="{Binding OutputCurrent, TargetNullValue=--, StringFormat=F1}"
                                         FontSize="24">
                                    </Run>
                                    <Run Text="A"></Run>
                                </TextBlock>
                                <StackPanel Grid.Row="1" Grid.Column="2" Orientation="Horizontal">
                                    <StackPanel.Resources>
                                        <conv:SwitchConverter x:Key="RunningStatusColor">
                                            <conv:SwitchCase When="NotConnected" Then="LightGray"></conv:SwitchCase>
                                            <conv:SwitchCase When="Stopped" Then="Orange"></conv:SwitchCase>
                                            <conv:SwitchCase When="Running" Then="Green"></conv:SwitchCase>
                                            <conv:SwitchCase When="Warning" Then="Red"></conv:SwitchCase>
                                        </conv:SwitchConverter>
                                        <conv:SwitchConverter x:Key="RunningStatusText">
                                            <conv:SwitchCase When="NotConnected" Then="未连接"></conv:SwitchCase>
                                            <conv:SwitchCase When="Stopped" Then="已停机"></conv:SwitchCase>
                                            <conv:SwitchCase When="Running" Then="运行中"></conv:SwitchCase>
                                            <conv:SwitchCase When="Warning" Then="检查告警码"></conv:SwitchCase>
                                        </conv:SwitchConverter>
                                    </StackPanel.Resources>
                                    <materialDesign:PackIcon Kind="CheckboxBlankCircle" Width="28" Height="28"
                                                             VerticalAlignment="Center"
                                                             Foreground="{Binding RunningStatus, Converter={StaticResource RunningStatusColor}}" />
                                    <TextBlock Margin="10 0 0 0" VerticalAlignment="Center"
                                               Text="{Binding RunningStatus, Converter={StaticResource RunningStatusText}}">
                                    </TextBlock>
                                </StackPanel>
                            </Grid>
                        </materialDesign:Card>
                    </Grid>
                    <materialDesign:Card Grid.Row="1" Padding="30 20" Margin="10">
                        <Viewbox MaxWidth="500">
                            <TextBlock>优优绿能电源</TextBlock>
                        </Viewbox>
                    </materialDesign:Card>
                </Grid>
            </Grid>
        </ScrollViewer>
    </Grid>
</UserControl>