using ChargerController;
using ChargerController.Impl;
using Moq;
using Serilog;

namespace ChargerControllerTests;

public class EscapePacketTransportTests
{
    [Fact]
    public async Task ReceiveNormalPacket_ShouldReturnPacket()
    {
        // Arrange
        var serialPort = new Mock<ITransport>();
        var byteQueue  = new Queue<byte>([0x27, 0x02, 0x00, 0x0C, 0x0A, 0x0A, 0x1A, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xB7, 0x27, 0x03]);
        serialPort.Setup(x => x.ReadByte()).Returns(() => byteQueue.Dequeue());
        serialPort.Setup(x => x.IsOpen).Returns(true);
        serialPort.Setup(x => x.DataAvailable).Returns(() => byteQueue.Count > 0);
        var logger    = new Mock<ILogger>();
        var transport = new EscapePacketTransport(logger.Object);
        transport.UseTransport(serialPort.Object);
        transport.ReceiveTimeout = TimeSpan.FromSeconds(1);

        // Act
        var packet = await transport.ReceivePacketAsync(CancellationToken.None);

        // Assert
        Assert.Equal([0x00, 0x0C, 0x0A, 0x0A, 0x1A, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1B, 0xB7], packet);
    }
}
