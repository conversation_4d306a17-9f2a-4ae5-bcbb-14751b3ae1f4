using ChargerController.DependencyInjection;
using DeviceGuard.Interface.Prism;
using DeviceGuard.Modules.Interface;
using InfyPower.IO;
using InfyPower.Pages.Index;

namespace InfyPower;

public class InfyPowerModule : IModule
{
    public void OnInitialized(IContainerProvider containerProvider)
    {
    }

    public void RegisterTypes(IContainerRegistry containerRegistry)
    {
        // Bootstrap
        containerRegistry.Register<IModuleLoader, InfyPowerModuleLoader>();

        // UI
        containerRegistry.RegisterForNavigation<InfyPowerIndexView>(typeof(InfyPowerIndexView).FullName);

        // IO
        containerRegistry.Register<InfyPowerClient>();
        containerRegistry.RegisterComponent<ChargerControllerComponent>();
    }
}