using DeviceGuard.Modules.Interface;
using DeviceGuard.Modules.Interface.Impl;
using InfyPower.Pages.Index;

namespace InfyPower;

public class InfyPowerModuleLoader : IModuleLoader
{
    private readonly IRegionManager _regionManager;

    public InfyPowerModuleLoader(IRegionManager regionManager)
    {
        _regionManager = regionManager;
    }

    public Task InitializeAsync(string? options, CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    public IModuleViewLoader[] GetViewLoaders(ModuleViewCategory viewCategory)
    {
        switch (viewCategory)
        {
            case ModuleViewCategory.Main:
                return
                [
                    // 首页
                    new DefaultModuleViewLoader(_regionManager, ModuleViewCategory.Main, "InfyPower", typeof(InfyPowerIndexView).FullName!)
                ];
            case ModuleViewCategory.Setting:
            default:
                return
                [
                ];
        }
    }
}
