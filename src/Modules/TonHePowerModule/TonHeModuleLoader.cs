using DeviceGuard.Modules.Interface;
using DeviceGuard.Modules.Interface.Impl;
using TonHePower.Pages.Index;

namespace TonHePower;

public class TonHeModuleLoader : IModuleLoader
{
    private readonly IRegionManager _regionManager;

    public TonHeModuleLoader(IRegionManager regionManager)
    {
        _regionManager = regionManager;
    }

    public Task InitializeAsync(string? options, CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    public IModuleViewLoader[] GetViewLoaders(ModuleViewCategory viewCategory)
    {
        switch (viewCategory)
        {
            case ModuleViewCategory.Main:
                return
                [
                    // 首页
                    new DefaultModuleViewLoader(_regionManager, ModuleViewCategory.Main, "TonHePower", typeof(TonHePowerIndexView).FullName!)
                ];
            case ModuleViewCategory.Setting:
            default:
                return
                [
                ];
        }
    }
}
