<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net6.0-windows</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <UseWPF>true</UseWPF>
        <Nullable>enable</Nullable>
        <RootNamespace>TonHePower</RootNamespace>
        <AssemblyName>TonHePower</AssemblyName>
        <CopyLocalLockFileAssemblies>true</CopyLocalLockFileAssemblies>
        <Platforms>AnyCPU;x64</Platforms>
        <LangVersion>latest</LangVersion>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="MaterialDesignThemes" Version="5.2.1"/>
        <PackageReference Include="Prism.Core" Version="9.0.537"/>
        <PackageReference Include="PropertyChanged.Fody" Version="4.1.0">
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="System.IO.Ports" Version="6.0.0" />
    </ItemGroup>

    <ItemGroup>
        <Page Update="Pages\Index\TonHePowerIndexView.xaml">
            <Generator>MSBuild:Compile</Generator>
            <XamlRuntime>Wpf</XamlRuntime>
            <SubType>Designer</SubType>
        </Page>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Interfaces\DeviceGuard.Modules.Interface\DeviceGuard.Modules.Interface.csproj"/>
        <ProjectReference Include="..\..\UI\DeviceGuard.Windows.Controls\DeviceGuard.Windows.Controls.csproj"/>
        <ProjectReference Include="..\..\UI\DeviceGuard.Windows.Utility\DeviceGuard.Windows.Utility.csproj"/>
        <ProjectReference Include="..\ChargerController\ChargerController.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <None Update="manifest.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="Resources\tonhe.ico">
          <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <PropertyGroup Condition="'$(PlatformTarget)' == 'x86'">
        <RuntimeIdentifier Condition="'$(RuntimeIdentifier)' == ''">win-x86</RuntimeIdentifier>
        <SelfContained Condition="'$(SelfContained)' == ''">false</SelfContained>
    </PropertyGroup>

    <PropertyGroup Condition="'$(PlatformTarget)' == 'x64'">
        <RuntimeIdentifier Condition="'$(RuntimeIdentifier)' == ''">win-x64</RuntimeIdentifier>
        <SelfContained Condition="'$(SelfContained)' == ''">false</SelfContained>
    </PropertyGroup>

    <Target Name="PostBuild" AfterTargets="PostBuildEvent">
      <Exec Command="$(SolutionDir)utils\PackModule.exe $(OutDir) $(SolutionDir)bin\$(Configuration)\$(TargetFramework)\$(RuntimeIdentifier)" />
    </Target>

</Project>
