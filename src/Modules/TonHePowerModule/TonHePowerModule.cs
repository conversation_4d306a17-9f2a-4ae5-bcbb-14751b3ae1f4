using ChargerController.DependencyInjection;
using DeviceGuard.Interface.Prism;
using TonHePower.IO;
using TonHePower.Pages.Index;

namespace TonHePower;

public class TonHePowerModule : IModule
{
    public void OnInitialized(IContainerProvider containerProvider)
    {
    }

    public void RegisterTypes(IContainerRegistry containerRegistry)
    {
        containerRegistry.RegisterForNavigation<TonHePowerIndexView>(typeof(TonHePowerIndexView).FullName);
        containerRegistry.Register<TonHePowerClient>();

        containerRegistry.RegisterComponent<ChargerControllerComponent>();
    }
}