using System.Buffers.Binary;
using ChargerController;
using ChargerController.IO;
using ChargerController.Utils;
using DeviceGuard.Interface.Cloud;

namespace TonHePower.IO;

/// <summary>
/// 通合的充电模块
/// </summary>
public class TonHePowerClient
{
    private readonly IChargerControllerClient _controller;

    /// <summary>
    /// 连接云端许可证的客户端
    /// </summary>
    private readonly ILicenseClient _licenseClient;

    public TonHePowerClient(IChargerControllerClient controller, ILicenseClient licenseClient)
    {
        _controller    = controller;
        _licenseClient = licenseClient;
    }

    /// <summary>
    /// 传输对象
    /// </summary>
    public ITransport? Transport { get; private set; }

    public ChargerPropertyValue<double?>   OutputVoltage  { get; } = new();
    public ChargerPropertyValue<double?>   OutputCurrent  { get; } = new();
    public ChargerPropertyValue<double?>   OutputPower    { get; } = new();
    public ChargerPropertyValue<double>   InputVoltageAB { get; } = new();
    public ChargerPropertyValue<double>   InputVoltageBC { get; } = new();
    public ChargerPropertyValue<double>   InputVoltageCA { get; } = new();
    public ChargerPropertyValue<byte[]>   Status         { get; } = new();
    public ChargerPropertyValue<string[]> StatusTexts    { get; } = new();
    public ChargerPropertyValue<bool>     Enabled        { get; } = new();

    /// <summary>
    /// 端口状态
    /// </summary>
    public bool IsConnected => _controller.IsOpen;

    public string PortName { get; private set; } = "";

    /// <summary>
    /// 初始化底层, 包括打开端口, 订阅数据等
    /// </summary>
    /// <param name="transport"></param>
    /// <param name="scanInterval"></param>
    /// <param name="cancellationToken"></param>
    /// <exception cref="ArgumentNullException"></exception>
    public async Task ConnectAsync(ITransport transport, uint scanInterval, CancellationToken cancellationToken)
    {
        if (transport == null) throw new ArgumentNullException(nameof(transport));

        // 创建串口客户端
        _controller.Use(transport);
        PortName  = transport.Name;
        Transport = transport;

        try
        {
            if (!await _controller.IsActivatedAsync(cancellationToken))
            {
                // 获取序列号
                var serialNumber = await _controller.GetSerialNumberAsync(cancellationToken);

                // 去云端激活
                var productKey = await _licenseClient.ActivateLicenseAsync(_controller.Sku, serialNumber, cancellationToken);

                // 把激活码写入设备
                await _controller.ActivateAsync(productKey, cancellationToken);
                throw new ReconnectException("激活完成, 请稍等测试仪重启动");
            }

            // 清理缓存
            await _controller.CleanAsync(cancellationToken);

            // 设置关闭模块的遗嘱
            await _controller.SetupWillDataAsync(0,
                CanType.Extended,
                0x0803FFA0,
                [0x01, 0x00, 0x00, 0x55, 0x00, 0x00, 0x00, 0x00],
                1000,
                10,
                cancellationToken);

            // 读取模块1输出状态、电压、电流、故障 
            var subscribingId = await _controller.SubscriptDataAsync(CanType.Extended,
                1000,
                0x1801A001,
                [],
                (data) =>
                {
                    if (data.Length < 8)
                    {
                        // todo 这里报警
                        return;
                    }

                    OutputVoltage.Value = BinaryPrimitives.ReadUInt16LittleEndian(data.AsSpan(1, 2)) / 10.0;
                    OutputCurrent.Value = BinaryPrimitives.ReadUInt16LittleEndian(data.AsSpan(3, 2)) / 100.0;
                    OutputPower.Value   = OutputVoltage.Value * OutputCurrent.Value;
                    Status.Value        = data.Skip(5).ToArray();
                    Enabled.Value       = (data[0] & 0x01) == 0x00;
                    // StatusTexts.Value = ["测试告警码"];
                    //https://kmsmg.coding.net/p/zhiliuzhuangceshiyi/files/all/45729306/preview/45729321
                    StatusTexts.Value = new List<string>()
                                        .AddWhen((data[5] & 0x01) > 0, "模块输入欠压")
                                        .AddWhen((data[5] & 0x02) > 0, "模块输入缺相")
                                        .AddWhen((data[5] & 0x04) > 0, "模块输入过压")
                                        .AddWhen((data[5] & 0x08) > 0, "模块输出过压")
                                        .AddWhen((data[5] & 0x10) > 0, "模块输出过流")
                                        .AddWhen((data[5] & 0x20) > 0, "模块温度过高")
                                        .AddWhen((data[5] & 0x40) > 0, "模块风扇故障")
                                        .AddWhen((data[5] & 0x80) > 0, "模块硬件故障")

                                        .AddWhen((data[6] & 0x01) > 0, "母线异常")
                                        .AddWhen((data[6] & 0x02) > 0, "SCI 通信异常")
                                        .AddWhen((data[6] & 0x04) > 0, "泄放故障")
                                        .AddWhen((data[6] & 0x08) > 0, "PFC 异常关机")
                                        .AddWhen((data[6] & 0x10) > 0, "输出欠压告警")
                                        .AddWhen((data[6] & 0x20) > 0, "输出过压告警")
                                        .AddWhen((data[6] & 0x40) > 0, "高温限功率")
                                        .AddWhen((data[6] & 0x80) > 0, "短路故障")

                                        .AddWhen((data[7] & 0x01) > 0, "输入过流故障")
                                        .AddWhen((data[7] & 0x02) > 0, "市电频率故障")
                                        .AddWhen((data[7] & 0x04) > 0, "市电不平衡故障")
                                        .AddWhen((data[7] & 0x08) > 0, "DCTz 故障")
                                        .AddWhen((data[7] & 0x10) > 0, "地址冲突")
                                        .AddWhen((data[7] & 0x20) > 0, "母线偏压")
                                        .AddWhen((data[7] & 0x40) > 0, "相位异常故障")
                                        .AddWhen((data[7] & 0x80) > 0, "母线过压故障")
                                        .ToArray();
                },
                cancellationToken);
            if (subscribingId == null)
            {
                throw new Exception("订阅模块1输出电压电流、状态和故障失败");
            }

            // 读取模块1的输入电压
            subscribingId = await _controller.SubscriptDataAsync(CanType.Extended,
                1000,
                0x180BA001,
                [],
                (data) =>
                {
                    if (data.Length < 8)
                    {
                        // todo 这里报警
                        return;
                    }

                    InputVoltageAB.Value = BinaryPrimitives.ReadUInt16LittleEndian(data.AsSpan(0, 2)) / 10.0;
                    InputVoltageBC.Value = BinaryPrimitives.ReadUInt16LittleEndian(data.AsSpan(2, 2)) / 10.0;
                    InputVoltageCA.Value = BinaryPrimitives.ReadUInt16LittleEndian(data.AsSpan(4, 2)) / 10.0;
                },
                cancellationToken);
            if (subscribingId == null)
            {
                throw new Exception("订阅模块1输出电压电流、状态和故障失败");
            }

            // 读取扩展状态、扩展故障
            subscribingId = await _controller.SubscriptDataAsync(CanType.Extended,
                1000,
                0x1891A001,
                [],
                (data) =>
                {
                    if (data.Length < 8)
                    {
                        // todo 这里报警
                        return;
                    }

                    // 不在界面显示范畴，暂不处理
                },
                cancellationToken);
            if (subscribingId == null)
            {
                throw new Exception("订阅模块1扩展状态和故障失败");
            }

            // 配置输入模式为交流输入
            await _controller.SendAsync(CanType.Extended, 0x18AAFFA0, [0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,], cancellationToken);

            // 配置对时命令为2500ms循环不间断发送
            await _controller.SendAsync(CanType.Extended, 0x1805FFA0, [0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,], 2500, cancellationToken);
        }
        catch (Exception)
        {
            _controller.Close();
            throw;
        }
    }

    public async Task DisconnectAsync(CancellationToken cancellationToken)
    {
        try
        {
            await _controller.CleanAsync(cancellationToken);
        }
        catch (Exception)
        {
            // todo 发出警告, 但是忽略异常
        }
        finally
        {
            _controller.Close();
        }
    }

    public async Task SetupAsync(double voltage, double current, CancellationToken cancellationToken)
    {
        if (_controller.IsOpen == false)
        {
            throw new Exception("检测仪没打开");
        }

        ushort voltageSetup = (ushort)(voltage * 10);
        ushort currentSetup = (ushort)(current * 100);

        var payload = new byte[8];
        payload[0] = 0x01;
        BinaryPrimitives.WriteUInt16LittleEndian(payload.AsSpan(4), voltageSetup);
        BinaryPrimitives.WriteUInt16LittleEndian(payload.AsSpan(6), currentSetup);

        // 设置电压和电流 TODO 目前没有处理回应数据
        await _controller.SendAsync(CanType.Extended, 0x1004FFA0, payload, cancellationToken);
    }

    public async Task OpenAsync(CancellationToken cancellationToken)
    {
        if (_controller.IsOpen == false)
        {
            throw new Exception("检测仪没打开");
        }

        // 启动 TODO 目前没有处理回应数据
        var result = await _controller.SendAsync(CanType.Extended, 0x0803FFA0, [0x01, 0x00, 0x00, 0xAA, 0x00, 0x00, 0x00, 0x00], cancellationToken);
        if (!result)
        {
            throw new Exception("启动失败");
        }
    }

    public async Task CloseAsync(CancellationToken cancellationToken)
    {
        if (_controller.IsOpen == false)
        {
            throw new Exception("检测仪没打开");
        }

        var result = await _controller.SendAsync(CanType.Extended, 0x0803FFA0, [0x01, 0x00, 0x00, 0x55, 0x00, 0x00, 0x00, 0x00], cancellationToken);
        if (!result)
        {
            throw new Exception("停止失败");
        }
    }
}
