using ChargerController.DependencyInjection;
using DeviceGuard.Interface.Prism;
using DeviceGuard.Modules.Interface;
using IncreasePower.IO;
using IncreasePower.Pages.Index;

namespace IncreasePower;

public class IncreasePowerModule : IModule
{
    public void OnInitialized(IContainerProvider containerProvider)
    {
    }

    public void RegisterTypes(IContainerRegistry containerRegistry)
    {
        // Bootstrap
        containerRegistry.Register<IModuleLoader, IncreasePowerModuleLoader>();

        // UI
        containerRegistry.RegisterForNavigation<IncreasePowerIndexView>(typeof(IncreasePowerIndexView).FullName);

        // IO
        containerRegistry.Register<IncreasePowerClient>();
        containerRegistry.RegisterComponent<ChargerControllerComponent>();
    }
}