using System.Windows;
using System.Windows.Threading;
using ChargerController;
using ChargerController.Impl;
using DeviceGuard.Windows.Utility;
using DeviceGuard.Windows.Utility.Dialogs;
using DeviceGuard.Windows.Utility.Mvvm;
using Doulex;
using IncreasePower.Configs;
using IncreasePower.IO;

namespace IncreasePower.Pages.Index;

public class IncreasePowerIndexViewModel : RegionViewModelBase
{
    private readonly IMessageBox         _messageBox;
    private          DispatcherTimer     _timer;
    private readonly IncreasePowerClient _increasePower;
    private readonly ITransportFacade    _transportFacade;
    private readonly IncreasePowerConfig _config;
    private readonly uint                _scanInterval = 500;
    private          bool                _timerPaused  = false;

    public IncreasePowerIndexViewModel(
        IMessageBox         messageBox,
        IncreasePowerClient increasePower,
        IncreasePowerConfig config,
        ITransportFacade    transportFacade)
    {
        _messageBox      = messageBox;
        _increasePower   = increasePower;
        _config          = config;
        _transportFacade = transportFacade;

        AllPorts     = _config.LastPorts;
        SelectedPort = AllPorts.FirstOrDefault(x => x == _config.LastSelectionPort);

        _timer = new DispatcherTimer(TimeSpan.FromMilliseconds(200),
            DispatcherPriority.Normal,
            TimerCallback,
            Dispatcher.CurrentDispatcher);
    }

    private async void TimerCallback(object? sender, EventArgs e)
    {
        try
        {
            if (_timerPaused)
                return;

            PortStatus = DiscoverCommand.IsExecuting ? "扫描设备中" : ConnectCommand.IsExecuting ? "连接中" : "";

            var deadline = DateTime.Now.AddMilliseconds(-_scanInterval * 3);
            
            ConnectedToTester    = _increasePower.IsConnected && !ConnectCommand.IsExecuting;
            ConnectedByBluetooth = ConnectedToTester && _increasePower.Transport is BluetoothTransport;
            ConnectedByUsb       = ConnectedToTester && _increasePower.Transport is SerialPortTransport;
            ConnectedToTarget    = ConnectedToTester && _increasePower.Enabled.LastUpdated >= deadline;

            RunStatus        = _increasePower.Enabled.Value ? "工作中" : "已停机";
            RunStatusExpired = _increasePower.Enabled.LastUpdated < deadline;
            // ErrorStatus = BitConverter.ToString(_increasePower.ErrorStatus.Value ?? []);
            StatusTexts           = _increasePower.StatusTexts.Value ?? [];
            StatusTextsExpired    = _increasePower.StatusTexts.LastUpdated < deadline;
            OutputVoltage         = _increasePower.OutputVoltage.Value?.Round(2);
            OutputVoltageExpired  = _increasePower.OutputVoltage.LastUpdated < deadline;
            OutputCurrent         = _increasePower.OutputCurrent.Value?.Round(2);
            OutputCurrentExpired  = _increasePower.OutputCurrent.LastUpdated < deadline;
            InputVoltageAB        = _increasePower.InputVoltageAB.Value;
            InputVoltageABExpired = _increasePower.InputVoltageAB.LastUpdated < deadline;
            InputVoltageBC        = _increasePower.InputVoltageBC.Value;
            InputVoltageBCExpired = _increasePower.InputVoltageBC.LastUpdated < deadline;
            InputVoltageCA        = _increasePower.InputVoltageCA.Value;
            InputVoltageCAExpired = _increasePower.InputVoltageCA.LastUpdated < deadline;
        }
        catch (Exception ex)
        {
            _timerPaused = true;
            if (await _messageBox.ShowQuestionAsync($"运行时发生错误: {ex.Message} \n结束程序?") == true)
            {
                Application.Current.Shutdown();
            }

            _timerPaused = false;
        }
    }

    static string BytesToBinaryString(byte[] bytes)
    {
        // 使用 StringBuilder 来构建最终的二进制字符串
        System.Text.StringBuilder binaryStringBuilder = new System.Text.StringBuilder();

        for (int i = 0; i < bytes.Length; i++)
        {
            // 将每个字节转换为 8 位二进制字符串
            string binary = Convert.ToString(bytes[i], 2).PadLeft(8, '0');

            // 将二进制字符串添加到 StringBuilder 中
            binaryStringBuilder.Append(binary);

            // 如果不是最后一个字节，添加连字符
            if (i < bytes.Length - 1)
            {
                binaryStringBuilder.Append('-');
            }
        }

        return binaryStringBuilder.ToString();
    }

    public override async Task OnUnloadedAsync(CancellationToken cancellation)
    {
        using var cursor = DisposableWaitCursor.WaitCursor();

        // 如果在连接中, 则等待执行完毕
        while (ConnectCommand.IsExecuting)
        {
            await Task.Delay(100, cancellation);
        }
        await _increasePower.DisconnectAsync(cancellation);
    }

    public override async Task OnNavigatedFromAsync(NavigationContext navigationContext, CancellationToken cancellation)
    {
        using var cursor = DisposableWaitCursor.WaitCursor();
        
        // 如果在连接中, 则等待执行完毕
        while (ConnectCommand.IsExecuting)
        {
            await Task.Delay(100, cancellation);
        }
        await _increasePower.DisconnectAsync(cancellation);
    }


    public override bool IsNavigationTarget(NavigationContext navigationContext)
    {
        return false;
    }

    public AsyncDelegateCommand ConnectCommand => GetPropertyCached(() => new AsyncDelegateCommand(async cancellationToken =>
    {
        using var cursor = DisposableWaitCursor.WaitCursor();
        try
        {
            if (string.IsNullOrEmpty(PortName))
            {
                await _messageBox.ShowWarningAsync("请输入端口名, 例如 COM6");
                return;
            }

            if (_increasePower.IsConnected)
            {
                await _messageBox.ShowWarningAsync("端口已打开");
                return;
            }

            if (SelectedPort == null)
            {
                await _messageBox.ShowWarningAsync("请选择端口");
                return;
            }

            var transport = await _transportFacade.CreateTransport(SelectedPort, cancellationToken);
            await _increasePower.ConnectAsync(transport, _scanInterval, cancellationToken);

            IsConnected = true;

            _config.LastSelectionPort = SelectedPort;
            _config.LastPorts         = AllPorts;
        }
        catch (ReconnectException e)
        {
            await _messageBox.ShowInfoAsync(e.Message);
        }
    }).ObservesCanExecute(() => CanConnect));

    public bool CanConnect => !IsConnected && SelectedPort != null;

    public AsyncDelegateCommand DisconnectCommand => GetPropertyCached(() =>
        new AsyncDelegateCommand(async cancellationToken =>
        {
            await _increasePower.DisconnectAsync(cancellationToken);
            IsConnected = false;
        }).ObservesCanExecute(() => CanDisconnect));

    public bool CanDisconnect => IsConnected;

    public AsyncDelegateCommand SetupCommand => GetPropertyCached(() =>
        new AsyncDelegateCommand(async cancellationToken =>
        {
            await _increasePower.SetupAsync(OutputVoltageSetup, OutputCurrentSetup, cancellationToken);
        }));

    public AsyncDelegateCommand StartCommand =>
        GetPropertyCached(() => new AsyncDelegateCommand(async cancellationToken =>
        {
            await _increasePower.OpenAsync(cancellationToken);
        }));

    public AsyncDelegateCommand StopCommand =>
        GetPropertyCached(() => new AsyncDelegateCommand(async cancellationToken =>
        {
            await _increasePower.CloseAsync(cancellationToken);
        }));

    public AsyncDelegateCommand DiscoverCommand =>
        GetPropertyCached(() => new AsyncDelegateCommand(async cancellationToken =>
        {
            using var cursor = DisposableWaitCursor.WaitCursor();
            try
            {
                AllPorts = await _transportFacade.DiscoverAsync([TransportType.SerialPort, TransportType.Bluetooth], cancellationToken);
                var selected = SelectedPort;
                if (selected != null && !AllPorts.Contains(selected))
                {
                    selected = null;
                }

                if (selected == null && AllPorts.Any())
                {
                    selected = AllPorts.First();
                }

                SelectedPort = selected;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }).ObservesCanExecute(() => CanDiscover));

    public bool CanDiscover => !IsConnected;

    public CFDevice[] AllPorts { get; set; } = [];

    public CFDevice? SelectedPort { get; set; }

    public bool     IsConnected           { get; set; }
    public string   PortName              => SelectedPort?.Address ?? "";
    public string   PortStatus            { get; set; } = "";
    public double   OutputVoltageSetup    { get; set; } = 0;
    public double   OutputCurrentSetup    { get; set; } = 0;
    public string?  RunStatus             { get; set; }
    public bool?    RunStatusExpired      { get; set; }
    public double?  OutputVoltage         { get; set; }
    public bool?    OutputVoltageExpired  { get; set; }
    public double?  OutputCurrent         { get; set; }
    public bool?    OutputCurrentExpired  { get; set; }
    public double?  InputVoltageAB        { get; set; }
    public bool?    InputVoltageABExpired { get; set; }
    public double?  InputVoltageBC        { get; set; }
    public bool?    InputVoltageBCExpired { get; set; }
    public double?  InputVoltageCA        { get; set; }
    public bool?    InputVoltageCAExpired { get; set; }
    public string[] StatusTexts           { get; set; } = [];
    public bool?    StatusTextsExpired    { get; set; } = false;

    /// <summary>
    /// 已连接测试仪
    /// </summary>
    public bool ConnectedToTester { get; set; }

    /// <summary>
    /// 使用蓝牙连接
    /// </summary>
    public bool ConnectedByBluetooth { get; set; }

    /// <summary>
    /// 使用USB连接
    /// </summary>
    public bool ConnectedByUsb { get; set; }

    /// <summary>
    /// 已连接至待测设备
    /// </summary>
    public bool ConnectedToTarget { get; set; }

    /// <summary>
    /// 运行状态
    /// </summary>
    public RunningStatus RunningStatus
    {
        get
        {
            if (!ConnectedToTarget)
                return RunningStatus.NotConnected;

            if (!_increasePower.Enabled.Value)
                return RunningStatus.Stopped;

            if (_increasePower.StatusTexts.Value?.Any() == true)
                return RunningStatus.Warning;

            return RunningStatus.Running;
        }
    }
}

public enum RunningStatus
{
    NotConnected,
    Stopped,
    Running,
    Warning,
}
