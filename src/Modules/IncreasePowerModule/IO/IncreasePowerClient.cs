using System.Buffers.Binary;
using ChargerController;
using ChargerController.IO;
using ChargerController.Utils;
using DeviceGuard.Interface.Cloud;

namespace IncreasePower.IO;

/// <summary>
/// 英可瑞的充电模块
/// </summary>
public class IncreasePowerClient
{
    private readonly IChargerControllerClient _controller;

    /// <summary>
    /// 连接云端许可证的客户端
    /// </summary>
    private readonly ILicenseClient _licenseClient;

    public IncreasePowerClient(IChargerControllerClient controller, ILicenseClient licenseClient)
    {
        _controller    = controller;
        _licenseClient = licenseClient;
    }
    
    /// <summary>
    /// 传输对象
    /// </summary>
    public ITransport? Transport { get; private set; }

    public ChargerPropertyValue<double?>   OutputVoltage  { get; } = new();
    public ChargerPropertyValue<double?>   OutputCurrent  { get; } = new();
    public ChargerPropertyValue<double?>   OutputPower    { get; } = new();
    public ChargerPropertyValue<double>   InputVoltageAB { get; } = new();
    public ChargerPropertyValue<double>   InputVoltageBC { get; } = new();
    public ChargerPropertyValue<double>   InputVoltageCA { get; } = new();
    public ChargerPropertyValue<byte[]>   Status         { get; } = new();
    public ChargerPropertyValue<string[]> StatusTexts    { get; } = new();
    public ChargerPropertyValue<bool>     Enabled        { get; } = new();

    /// <summary>
    /// 端口状态
    /// </summary>
    public bool IsConnected => _controller.IsOpen;

    public string PortName { get; private set; } = "";

    /// <summary>
    /// 初始化底层, 包括打开端口, 订阅数据等
    /// </summary>
    /// <param name="transport"></param>
    /// <param name="scanInterval"></param>
    /// <param name="cancellationToken"></param>
    /// <exception cref="ArgumentNullException"></exception>
    public async Task ConnectAsync(ITransport transport, uint scanInterval, CancellationToken cancellationToken)
    {
        if (transport == null) throw new ArgumentNullException(nameof(transport));

        // 创建串口客户端
        _controller.Use(transport);
        PortName  = transport.Name;
        Transport = transport;

        try
        {
            if (!await _controller.IsActivatedAsync(cancellationToken))
            {
                // 获取序列号
                var serialNumber = await _controller.GetSerialNumberAsync(cancellationToken);

                // 去云端激活
                var productKey = await _licenseClient.ActivateLicenseAsync(_controller.Sku, serialNumber, cancellationToken);

                // 把激活码写入设备
                await _controller.ActivateAsync(productKey, cancellationToken);
                throw new ReconnectException("激活完成, 请稍等测试仪重启动");
            }

            // 清理缓存
            await _controller.CleanAsync(cancellationToken);

            // 设置关闭模块的遗嘱
            await _controller.SetupWillDataAsync(0,
                CanType.Extended,
                0x1307C081,
                [0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAA],
                1000,
                10,
                cancellationToken);

            // 读取模块1输出状态、电压、电流、故障 
            var subscribingId = await _controller.SubscriptDataAsync(CanType.Extended,
                0x1307C081,
                [0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00],
                scanInterval,
                0x1207C081,
                [0x01, 0x00],
                (data) =>
                {
                    if (data.Length < 8)
                    {
                        // todo 这里报警
                        return;
                    }

                    OutputCurrent.Value = BinaryPrimitives.ReadUInt16BigEndian(data.AsSpan(2, 2)) / 10.0;
                    OutputVoltage.Value = BinaryPrimitives.ReadUInt16BigEndian(data.AsSpan(4, 2)) / 10.0;
                    OutputPower.Value   = OutputVoltage.Value * OutputCurrent.Value;
                    Status.Value        = data.Skip(6).ToArray();
                    Enabled.Value       = (data[7] & 0x01) == 0x00;
                    // StatusTexts.Value = ["测试告警码"];
                    // https://kmsmg.coding.net/p/zhiliuzhuangceshiyi/files/all/46969421/preview/46969423
                    StatusTexts.Value = new List<string>()
                                       .AddWhen((data[7] & 0x01) > 0, "模块关机")
                                       .AddWhen((data[7] & 0x02) > 0, "模块故障")
                                       .AddWhen((data[7] & 0x04) > 0, "模块恒流")
                                       .AddWhen((data[7] & 0x08) > 0, "风扇故障")
                                       .AddWhen((data[7] & 0x10) > 0, "输入过压")
                                       .AddWhen((data[7] & 0x20) > 0, "输入欠压")
                                       .AddWhen((data[7] & 0x40) > 0, "输出过压")
                                       .AddWhen((data[7] & 0x80) > 0, "输出欠压")

                                       .AddWhen((data[6] & 0x01) > 0, "过流保护")
                                       .AddWhen((data[6] & 0x02) > 0, "过温保护")
                                       .AddWhen((data[6] & 0x04) > 0, "设置关机")
                                       .ToArray();
                },
                cancellationToken);
            if (subscribingId == null)
            {
                throw new Exception("订阅模块1输出电压电流、状态和故障失败");
            }

            // 读取模块1的输入电压
            subscribingId = await _controller.SubscriptDataAsync(CanType.Extended,
                0x1307A081,
                [0x31, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00],
                scanInterval,
                0x1207A081,
                [0x31, 0x01],
                (data) =>
                {
                    if (data.Length < 8)
                    {
                        // todo 这里报警
                        return;
                    }

                    InputVoltageAB.Value = BinaryPrimitives.ReadUInt16BigEndian(data.AsSpan(2, 2)) / 32.0;
                    InputVoltageBC.Value = BinaryPrimitives.ReadUInt16BigEndian(data.AsSpan(4, 2)) / 32.0;
                    InputVoltageCA.Value = BinaryPrimitives.ReadUInt16BigEndian(data.AsSpan(6, 2)) / 32.0;
                },
                cancellationToken);
            if (subscribingId == null)
            {
                throw new Exception("订阅模块1输出电压电流、状态和故障失败");
            }
        }
        catch (Exception)
        {
            _controller.Close();
            throw;
        }
    }

    public async Task DisconnectAsync(CancellationToken cancellationToken)
    {
        try
        {
            await _controller.CleanAsync(cancellationToken);
        }
        catch (Exception)
        {
            // todo 发出警告, 但是忽略异常
        }
        finally
        {
            _controller.Close();
        }
    }

    public async Task SetupAsync(double voltage, double current, CancellationToken cancellationToken)
    {
        if (_controller.IsOpen == false)
        {
            throw new Exception("检测仪没打开");
        }

        ushort voltageSetup = (ushort)(voltage * 1000);
        ushort currentSetup = (ushort)(current * 1000);

        var payload = new byte[8];
        payload[0] = 0x00;
        // 将 voltageSetup 存到从 4 字节开始的 4 字节空间
        BinaryPrimitives.WriteUInt32LittleEndian(payload.AsSpan(4), (uint)voltageSetup);
        // 将 currentSetup 存到从 1 字节开始的 3 字节空间
        payload[3] = (byte)(currentSetup & 0xFF);
        payload[2] = (byte)((currentSetup >> 8) & 0xFF);
        payload[1] = (byte)((currentSetup >> 16) & 0xFF);

        // 设置电压和电流 TODO 目前没有处理回应数据
        await _controller.SendAsync(CanType.Extended, 0x1307C081, payload, cancellationToken);
    }

    public async Task OpenAsync(CancellationToken cancellationToken)
    {
        if (_controller.IsOpen == false)
        {
            throw new Exception("检测仪没打开");
        }

        // 启动 TODO 目前没有处理回应数据
        var result = await _controller.SendAsync(CanType.Extended, 0x1307C081, [0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55], cancellationToken);
        if (!result)
        {
            throw new Exception("启动失败");
        }
    }

    public async Task CloseAsync(CancellationToken cancellationToken)
    {
        if (_controller.IsOpen == false)
        {
            throw new Exception("检测仪没打开");
        }

        var result = await _controller.SendAsync(CanType.Extended, 0x1307C081, [0x02, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xAA], cancellationToken);
        if (!result)
        {
            throw new Exception("停止失败");
        }
    }
}
