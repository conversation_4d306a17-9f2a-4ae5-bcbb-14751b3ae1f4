using DeviceGuard.Modules.Interface;
using DeviceGuard.Modules.Interface.Impl;
using IncreasePower.Pages.Index;

namespace IncreasePower;

public class IncreasePowerModuleLoader : IModuleLoader
{
    private readonly IRegionManager _regionManager;

    public IncreasePowerModuleLoader(IRegionManager regionManager)
    {
        _regionManager = regionManager;
    }

    public Task InitializeAsync(string? options, CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    public IModuleViewLoader[] GetViewLoaders(ModuleViewCategory viewCategory)
    {
        switch (viewCategory)
        {
            case ModuleViewCategory.Main:
                return
                [
                    // 首页
                    new DefaultModuleViewLoader(_regionManager, ModuleViewCategory.Main, "IncreasePower", typeof(IncreasePowerIndexView).FullName!)
                ];
            case ModuleViewCategory.Setting:
            default:
                return
                [
                ];
        }
    }
}
