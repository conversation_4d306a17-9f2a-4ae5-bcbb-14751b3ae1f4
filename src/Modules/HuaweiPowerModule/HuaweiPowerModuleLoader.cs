using DeviceGuard.Modules.Interface;
using DeviceGuard.Modules.Interface.Impl;
using HuaweiPower.Pages.Index;

namespace HuaweiPower;

public class HuaweiPowerModuleLoader : IModuleLoader
{
    private readonly IRegionManager _regionManager;

    public HuaweiPowerModuleLoader(IRegionManager regionManager)
    {
        _regionManager = regionManager;
    }

    public Task InitializeAsync(string? options, CancellationToken cancellationToken)
    {
        return Task.CompletedTask;
    }

    public IModuleViewLoader[] GetViewLoaders(ModuleViewCategory viewCategory)
    {
        switch (viewCategory)
        {
            case ModuleViewCategory.Main:
                return
                [
                    // 首页
                    new DefaultModuleViewLoader(_regionManager, ModuleViewCategory.Main, "HuaweiPower", typeof(HuaweiPowerIndexView).FullName!)
                ];
            case ModuleViewCategory.Setting:
            default:
                return
                [
                ];
        }
    }
}
