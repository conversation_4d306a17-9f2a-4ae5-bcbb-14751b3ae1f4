using System.IO;
using System.Reflection;
using System.Windows;
using System.Windows.Controls;

namespace HuaweiPower.Pages.Index;

/// <summary>
/// Interaction logic for PageNotFoundView.xaml
/// </summary>
public partial class HuaweiPowerIndexView : UserControl
{
    public HuaweiPowerIndexView()
    {
        InitializeComponent();

        // webView.EnsureCoreWebView2Async(null);
        var basePath = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location) ?? "./";
        var fullPath = Path.GetFullPath(Path.Combine(basePath, "docs/huawei_manual.pdf"));

        // 临时显示 PDF 文件, 目前布局不理想
        WebView.Address = fullPath;
    }
}
