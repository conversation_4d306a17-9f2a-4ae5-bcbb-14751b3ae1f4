using ChargerController.DependencyInjection;
using DeviceGuard.Interface.Prism;
using DeviceGuard.Modules.Interface;
using HuaweiPower.IO;
using HuaweiPower.Pages.Index;

namespace HuaweiPower;

public class HuaweiPowerModule : IModule
{
    public void OnInitialized(IContainerProvider containerProvider)
    {
    }

    public void RegisterTypes(IContainerRegistry containerRegistry)
    {
        // Bootstrap
        containerRegistry.Register<IModuleLoader, HuaweiPowerModuleLoader>();

        containerRegistry.RegisterForNavigation<HuaweiPowerIndexView>(typeof(HuaweiPowerIndexView).FullName);
        containerRegistry.Register<HuaweiPowerClient>();

        containerRegistry.RegisterComponent<ChargerControllerComponent>();
    }
}
