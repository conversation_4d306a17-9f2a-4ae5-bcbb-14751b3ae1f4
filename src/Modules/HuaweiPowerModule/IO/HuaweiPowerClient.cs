using System.Buffers.Binary;
using ChargerController;
using ChargerController.IO;
using ChargerController.Utils;
using DeviceGuard.Interface.Cloud;

namespace HuaweiPower.IO;

/// <summary>
/// 英飞源的充电模块
/// </summary>
public class HuaweiPowerClient
{
    private readonly IChargerControllerClient _controller;
    private readonly ILicenseClient           _licenseClient;

    public HuaweiPowerClient(IChargerControllerClient controller, ILicenseClient licenseClient)
    {
        _controller    = controller;
        _licenseClient = licenseClient;
    }

    /// <summary>
    /// 传输对象
    /// </summary>
    public ITransport? Transport { get; private set; }

    public ChargerPropertyValue<double?>   OutputVoltage  { get; } = new();
    public ChargerPropertyValue<double?>   OutputCurrent  { get; } = new();
    public ChargerPropertyValue<double?>   OutputPower    { get; } = new();
    public ChargerPropertyValue<double>   InputVoltageAB { get; } = new();
    public ChargerPropertyValue<double>   InputVoltageBC { get; } = new();
    public ChargerPropertyValue<double>   InputVoltageCA { get; } = new();
    public ChargerPropertyValue<byte[]>   Status         { get; } = new();
    public ChargerPropertyValue<string[]> StatusTexts    { get; } = new();
    public ChargerPropertyValue<bool>     Enabled        { get; } = new();

    /// <summary>
    /// 端口状态
    /// </summary>
    public bool IsConnected => _controller.IsOpen;

    public string PortName { get; private set; } = "";

    /// <summary>
    /// 初始化底层, 包括打开端口, 订阅数据等
    /// </summary>
    /// <param name="transport"></param>
    /// <param name="scanInterval"></param>
    /// <param name="cancellationToken"></param>
    /// <exception cref="ArgumentNullException"></exception>
    public async Task ConnectAsync(ITransport transport, uint scanInterval, CancellationToken cancellationToken)
    {
        if (transport == null) throw new ArgumentNullException(nameof(transport));

        // 创建串口客户端
        _controller.Use(transport);
        PortName  = transport.Name;
        Transport = transport;

        try
        {
            if (!await _controller.IsActivatedAsync(cancellationToken))
            {
                // 获取序列号
                var serialNumber = await _controller.GetSerialNumberAsync(cancellationToken);

                // 去云端激活
                var productKey = await _licenseClient.ActivateLicenseAsync(_controller.Sku, serialNumber, cancellationToken);

                // 把激活码写入设备
                await _controller.ActivateAsync(productKey, cancellationToken);
                throw new ReconnectException("激活完成, 请稍等测试仪重启动");
            }

            // 清理缓存
            await _controller.CleanAsync(cancellationToken);

            // 设置关闭模块的遗嘱
            await _controller.SetupWillDataAsync(0,
                CanType.Extended,
                0x068180FC,
                [0x01, 0x32, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00],
                1000,
                10,
                cancellationToken);

            // 读取模块输入电压
            var subscribingId = await _controller.SubscriptDataAsync(CanType.Extended,
                0x068182FC,
                [0x01, 0x79, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,],
                scanInterval,
                0x0681827C,
                [0x01, 0x79],
                (data) =>
                {
                    if (data.Length < 8)
                    {
                        // todo 这里报警
                        return;
                    }

                    InputVoltageAB.Value = BinaryPrimitives.ReadUInt32BigEndian(data.AsSpan(4, 4)) / 1024.0;
                },
                cancellationToken);
            if (subscribingId == null)
            {
                throw new Exception("订阅模块三项输入电压失败");
            }


            // 读取模块输入电压
            subscribingId = await _controller.SubscriptDataAsync(CanType.Extended,
                0x068182FC,
                [0x01, 0x7A, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,],
                scanInterval,
                0x0681827C,
                [0x01, 0x7A],
                (data) =>
                {
                    if (data.Length < 8)
                    {
                        // todo 这里报警
                        return;
                    }

                    InputVoltageBC.Value = BinaryPrimitives.ReadUInt32BigEndian(data.AsSpan(4, 4)) / 1024.0;
                    // InputVoltageBC.Value = BinaryPrimitives.ReadUInt16BigEndian(data.AsSpan(2, 2)) / 10.0;
                    // InputVoltageCA.Value = BinaryPrimitives.ReadUInt16BigEndian(data.AsSpan(4, 2)) / 10.0;
                },
                cancellationToken);
            if (subscribingId == null)
            {
                throw new Exception("订阅模块三项输入电压失败");
            }

            // 读取模块输入电压
            subscribingId = await _controller.SubscriptDataAsync(CanType.Extended,
                0x068182FC,
                [0x01, 0x7B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,],
                scanInterval,
                0x0681827C,
                [0x01, 0x7B],
                (data) =>
                {
                    if (data.Length < 8)
                    {
                        // todo 这里报警
                        return;
                    }

                    InputVoltageCA.Value = BinaryPrimitives.ReadUInt32BigEndian(data.AsSpan(4, 4)) / 1024.0;
                },
                cancellationToken);
            if (subscribingId == null)
            {
                throw new Exception("订阅模块三项输入电压失败");
            }

            // 读取模块输出电压
            subscribingId = await _controller.SubscriptDataAsync(CanType.Extended,
                0x068182FC,
                [0x01, 0x75, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,],
                scanInterval,
                0x0681827C,
                [0x01, 0x75],
                (data) =>
                {
                    if (data.Length < 8)
                    {
                        // todo 这里报警
                        return;
                    }

                    OutputVoltage.Value = BinaryPrimitives.ReadUInt32BigEndian(data.AsSpan(4, 4)) / 1024.0;
                },
                cancellationToken);
            if (subscribingId == null)
            {
                throw new Exception("订阅模块输出电压失败");
            }

            // 读取模块输出电流 
            subscribingId = await _controller.SubscriptDataAsync(CanType.Extended,
                0x068182FC,
                [0x01, 0x81, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,],
                scanInterval,
                0x0681827C,
                [0x01, 0x81],
                (data) =>
                {
                    if (data.Length < 8)
                    {
                        // todo 这里报警
                        return;
                    }

                    OutputCurrent.Value = BinaryPrimitives.ReadUInt32BigEndian(data.AsSpan(4, 4)) / 1024.0;
                },
                cancellationToken);
            if (subscribingId == null)
            {
                throw new Exception("订阅模块输出电流失败");
            }

            // 读取模块状态
            subscribingId = await _controller.SubscriptDataAsync(CanType.Extended,
                0x068182FC,
                [0x01, 0x83, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,],
                scanInterval,
                0x0681827C,
                [0x01, 0x83],
                (data) =>
                {
                    if (data.Length < 8)
                    {
                        // todo 这里报警
                        return;
                    }

                    Status.Value      = data.Skip(2).ToArray();
                    Enabled.Value     = (data[6] & 0x02) == 0;
                    // StatusTexts.Value = ["测试告警码"];
                    // https://kmsmg.coding.net/p/zhiliuzhuangceshiyi/files/all/45723271/preview/45729661
                    StatusTexts.Value = new List<string>()
                                        .AddWhen((data[2] & 0x01) > 0, "模块放电电路异常(保护)")
                                        .AddWhen((data[2] & 0x02) > 0, "模块高位硬件地址使能(设定状态)")
                                        .AddWhen((data[2] & 0x04) > 0, "模块短路锁死(故障)")
                                        .AddWhen((data[2] & 0x08) > 0, "模块内部继电器电路异常(保护)")
                                        .AddWhen((data[2] & 0x10) > 0, "放电电路(故障)")
                                        .AddWhen((data[2] & 0x20) > 0, "输出继电器(故障)")
                                        .AddWhen((data[2] & 0x40) > 0, "输出负载震荡(故障)")
                                        .AddWhen((data[2] & 0x80) > 0, "模块测试模式")

                                        .AddWhen((data[3] & 0x01) > 0, "模块未接插到位(保护)")
                                        .AddWhen((data[3] & 0x02) > 0, "模块输出模式：低压")
                                        .AddWhen((data[3] & 0x04) > 0, "模块输出电容过压(保护)")
                                        .AddWhen((data[3] & 0x08) > 0, "模块输出电容电压不平衡(保护)")
                                        .AddWhen((data[3] & 0x10) > 0, "模块与监控通讯失败关机(保护)")
                                        .AddWhen((data[3] & 0x20) > 0, "模块均流屏蔽")
                                        .AddWhen((data[3] & 0x40) > 0, "模块硬地址冲突")
                                        .AddWhen((data[3] & 0x80) > 0, "模块硬件地址异常")

                                        .AddWhen((data[7] & 0x01) > 0, "输出过压锁死")
                                        .AddWhen((data[7] & 0x02) > 0, "环温过温关机")
                                        .AddWhen((data[7] & 0x04) > 0, "模块故障关机")
                                        .AddWhen((data[7] & 0x08) > 0, "模块保护关机")
                                        .AddWhen((data[7] & 0x10) > 0, "模块风扇故障")
                                        .AddWhen((data[7] & 0x20) > 0, "EEPROM读写错误")
                                        .AddWhen((data[7] & 0x40) > 0, "输出过流")
                                        .AddWhen((data[7] & 0x80) > 0, "输出欠压")

                                        .AddWhen((data[6] & 0x01) > 0, "环温低温关机")
                                        .AddWhen((data[6] & 0x02) > 0, "模块关机")
                                        .AddWhen((data[6] & 0x04) > 0, "风扇固定全速状态")
                                        .AddWhen((data[6] & 0x08) > 0, "防尘网堵塞")
                                        .AddWhen((data[6] & 0x10) > 0, "模块内部过温")
                                        .AddWhen((data[6] & 0x20) > 0, "软地址重排中")
                                        .AddWhen((data[6] & 0x40) > 0, "输出模式自动切换使能")
                                        .AddWhen((data[6] & 0x80) > 0, "CAN通讯质量差")

                                        .AddWhen((data[5] & 0x01) > 0, "模块顺序起机功能使能")
                                        .AddWhen((data[5] & 0x02) > 0, "模块输入欠压")
                                        .AddWhen((data[5] & 0x04) > 0, "模块交流不平衡")
                                        .AddWhen((data[5] & 0x08) > 0, "模块交流缺相")
                                        .AddWhen((data[5] & 0x10) > 0, "模块严重不均流")
                                        .AddWhen((data[5] & 0x20) > 0, "模块序列号重复关机")
                                        .AddWhen((data[5] & 0x40) > 0, "模块输入过压")
                                        .AddWhen((data[5] & 0x80) > 0, "模块PFC故障")

                                        .AddWhen((data[4] & 0x01) > 0, "模块不均流")
                                        .AddWhen((data[4] & 0x02) > 0, "效率寻优关机")
                                        .AddWhen((data[4] & 0x04) > 0, "模块内部通信异常")
                                        .AddWhen((data[4] & 0x08) > 0, "输出短路限流")
                                        .AddWhen((data[4] & 0x10) > 0, "模块限流")
                                        .AddWhen((data[4] & 0x20) > 0, "模块输入停电")
                                        .AddWhen((data[4] & 0x40) > 0, "PFC母线不平衡")
                                        .AddWhen((data[4] & 0x80) > 0, "PFC母线电压过欠压")
                                        .ToArray();
                },
                cancellationToken);
            if (subscribingId == null)
            {
                throw new Exception("订阅模块输出状态失败");
            }
        }
        catch (Exception)
        {
            _controller.Close();
            throw;
        }
    }

    public async Task DisconnectAsync(CancellationToken cancellationToken)
    {
        try
        {
            await _controller.CleanAsync(cancellationToken);
        }
        catch (Exception)
        {
            // todo 发出警告, 但是忽略异常
        }
        finally
        {
            _controller.Close();
        }
    }

    public async Task SetupAsync(double voltage, double current, CancellationToken cancellationToken)
    {
        if (_controller.IsOpen == false)
        {
            throw new Exception("检测仪没打开");
        }

        uint voltageSetup = (uint)(voltage * 1024);

        var payload = new byte[8];
        payload[0] = 0x01;
        BinaryPrimitives.WriteUInt32BigEndian(payload.AsSpan(4), voltageSetup);
        if (!await _controller.SendAsync(CanType.Extended, 0x068180FC, payload, cancellationToken))
        {
            throw new Exception("设置电压失败");
        }

        // Set up the current
        uint currentSetup = (uint)(current * 1024);
        payload[0] = 0x01;
        payload[1] = 0x0F;

        BinaryPrimitives.WriteUInt32BigEndian(payload.AsSpan(4), currentSetup);
        await _controller.SendAsync(CanType.Extended, 0x068180FC, payload, cancellationToken);
    }

    public async Task OpenAsync(CancellationToken cancellationToken)
    {
        if (_controller.IsOpen == false)
        {
            throw new Exception("检测仪没打开");
        }

        // 启动 TODO 目前没有处理回应数据
        var result = await _controller.SendAsync(CanType.Extended,
            0x068180FC,
            [0x01, 0x32, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,],
            cancellationToken);
        if (!result)
        {
            throw new Exception("启动失败");
        }
    }

    public async Task CloseAsync(CancellationToken cancellationToken)
    {
        if (_controller.IsOpen == false)
        {
            throw new Exception("检测仪没打开");
        }

        var result = await _controller.SendAsync(CanType.Extended,
            0x068180FC,
            [0x01, 0x32, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00],
            cancellationToken);
        if (!result)
        {
            throw new Exception("停止失败");
        }
    }
}
