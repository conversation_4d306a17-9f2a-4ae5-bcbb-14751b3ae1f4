namespace ChargerController;

public interface ITransportFactoryProducer
{
    ITransportFactory CreateTransportFactory(TransportType type);
}

/// <summary>
/// 传入层发现 Facade 对象, 用来为 UI 提供自定义的传入层发现服务
/// </summary>
public interface ITransportFacade
{
    /// <summary>
    /// 查找发现支持的传输层
    /// </summary>
    /// <param name="transportTypes">要查找的, 传输层类型</param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public Task<CFDevice[]> DiscoverAsync(TransportType[] transportTypes, CancellationToken cancellationToken);

    /// <summary>
    /// 根据传入的Device对象创建传输层
    /// </summary>
    /// <param name="device"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<ITransport> CreateTransport(CFDevice device, CancellationToken cancellationToken);
}
