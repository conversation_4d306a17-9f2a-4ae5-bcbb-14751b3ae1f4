using ChargerController.Impl;

namespace ChargerController;

/// <summary>
/// ESCAPE 报文传输接口
/// 27 02 ...... 27 03
/// </summary>
public interface IEscapePacketTransport
{
    /// <summary>
    /// 是否已连接了设备
    /// </summary>
    bool Connected { get; }

    /// <summary>
    /// 通讯状态
    /// </summary>
    CommStatus Status { get; }

    /// <summary>
    /// 记录最后一次发送错误的状态
    /// </summary>
    CommSendingErrorStatus SendingErrorStatus { get; }

    /// <summary>
    /// 拥有发送错误
    /// </summary>
    bool HasSendingError => SendingErrorStatus != CommSendingErrorStatus.None;

    /// <summary>
    /// 记录最后一次接收错误的状态
    /// </summary>
    CommReceivingErrorStatus ReceivingErrorStatus { get; }

    /// <summary>
    /// 含有接收错误
    /// </summary>
    bool HasReceivingError => ReceivingErrorStatus != CommReceivingErrorStatus.None;

    /// <summary>
    /// 已接收的字节数
    /// </summary>
    int ReceivedPackets { get; }

    /// <summary>
    /// 已接收的字节数
    /// </summary>
    int ReceivedBytes { get; }

    /// <summary>
    /// 已发送报文的数量
    /// </summary>
    int SentPackets { get; }

    /// <summary>
    /// 已发送的字节数
    /// </summary>
    int SentBytes { get; }

    /// <summary>
    /// 接收超时时间 (ms)
    /// </summary>
    TimeSpan ReceiveTimeout { get; set; }

    /// <summary>
    /// 连接设备
    /// </summary>
    /// <param name="transport"></param>
    /// <returns></returns>
    void UseTransport(ITransport transport);

    /// <summary>
    /// 发送报文
    /// </summary>
    /// <param name="payload"></param>
    /// <param name="cancellationToken"></param>
    Task SendAsync(Memory<byte> payload, CancellationToken cancellationToken);

    /// <summary>
    /// 接受一个报文
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<byte[]> ReceivePacketAsync(CancellationToken cancellationToken);

    /// <summary>
    /// 清除接收缓冲区
    /// </summary>
    void ClearReceiveBuffer();
}
