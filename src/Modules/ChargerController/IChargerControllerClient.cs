using ChargerController.IO;

namespace ChargerController;

public interface IChargerControllerClient
{
    /// <summary>
    /// 是否已经连接了设备
    /// </summary>
    bool IsOpen { get; }

    /// <summary>
    /// 充电桩检测仪用来激活的SKU
    /// </summary>
    string Sku => "CFCD";

    /// <summary>
    /// 使用指定的串口
    /// </summary>
    /// <param name="transport"></param>
    /// <returns></returns>
    void Use(ITransport transport);

    /// <summary>
    /// 关闭串口
    /// </summary>
    void Close();

    /// <summary>
    /// 设置发送的遗嘱数据, 在上位机与检测仪直接意外断开连接时, 检测仪将按照该预先设定发送遗嘱报文, 例如停机命令
    /// </summary>
    /// <param name="id">ID 编号, [0,49]</param>
    /// <param name="type">CAN 类型</param>
    /// <param name="requestAddress">每次数据请求地址</param>
    /// <param name="requestPayload">请求数据</param>
    /// <param name="requestPeriod">请求间隔(ms)</param>
    /// <param name="requestTimes">总共请求的次数</param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task SetupWillDataAsync(int id,
        CanType                 type,
        uint                    requestAddress,
        byte[]                  requestPayload,
        uint                    requestPeriod,
        uint                    requestTimes,
        CancellationToken       cancellationToken);


    /// <summary>
    /// 订阅数据
    /// </summary>
    /// <param name="type">CAN 类型</param>
    /// <param name="requestPeriod">请求间隔</param>
    /// <param name="responseAddress">数据响应地址</param>
    /// <param name="responsePayloadExpected"></param>
    /// <param name="responseCallback">数据响应回调</param>
    /// <param name="cancellationToken"></param>
    /// <returns>订阅序号, 如果返回 null 则表示订阅失败</returns>
    Task<uint?> SubscriptDataAsync(
        CanType           type,
        uint              requestPeriod,
        uint              responseAddress,
        byte?[]           responsePayloadExpected,
        Action<byte[]>    responseCallback,
        CancellationToken cancellationToken);

    /// <summary>
    /// 发送订阅请求, 订阅数据
    /// </summary>
    /// <param name="type">CAN 类型</param>
    /// <param name="requestAddress">每次数据请求地址</param>
    /// <param name="requestPayload">请求数据</param>
    /// <param name="requestPeriod">请求间隔</param>
    /// <param name="responseAddress">数据响应地址</param>
    /// <param name="responsePayloadExpected"></param>
    /// <param name="responseCallback">数据响应回调</param>
    /// <param name="cancellationToken"></param>
    /// <returns>订阅序号, 如果返回 null 则表示订阅失败</returns>
    Task<uint?> SubscriptDataAsync(
        CanType           type,
        uint              requestAddress,
        byte[]            requestPayload,
        uint              requestPeriod,
        uint              responseAddress,
        byte?[]           responsePayloadExpected,
        Action<byte[]>    responseCallback,
        CancellationToken cancellationToken);

    /// <summary>
    /// 取消订阅
    /// </summary>
    /// <param name="subscribedId"></param>
    /// <param name="cancellationToken"></param>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    Task UnsubscribeData(uint subscribedId, CancellationToken cancellationToken);

    /// <summary>
    /// 发送数据
    /// </summary>
    /// <param name="type">CAN 类型</param>
    /// <param name="requestAddress">每次数据请求地址</param>
    /// <param name="requestPayload">请求数据</param>
    /// <param name="requestPeriod">请求间隔 (ms)</param>
    /// <param name="requestTimes">请求次数</param>
    /// <param name="cancellationToken"></param>
    /// <returns>发送序号, 如果返回 null 则表示发送失败</returns>
    Task<uint?> SendAsync(CanType type,
        uint                      requestAddress,
        byte[]                    requestPayload,
        uint                      requestPeriod,
        uint                      requestTimes,
        CancellationToken         cancellationToken);

    /// <summary>
    /// 按照指定周期, 无休止的发送数据
    /// </summary>
    /// <param name="type">CAN 类型</param>
    /// <param name="requestAddress">每次数据请求地址</param>
    /// <param name="requestPayload">请求数据</param>
    /// <param name="requestPeriod">请求间隔 (ms)</param>
    /// <param name="cancellationToken"></param>
    /// <returns>发送序号, 如果返回 null 则表示发送失败</returns>
    Task<uint?> SendAsync(CanType type,
        uint                      requestAddress,
        byte[]                    requestPayload,
        uint                      requestPeriod,
        CancellationToken         cancellationToken);

    /// <summary>
    /// 发送数据(一次)
    /// </summary>
    /// <param name="type">CAN 类型</param>
    /// <param name="requestAddress">每次数据请求地址</param>
    /// <param name="requestPayload">请求数据</param>
    /// <param name="cancellationToken"></param>
    Task<bool> SendAsync(CanType type, uint requestAddress, byte[] requestPayload, CancellationToken cancellationToken);

    /// <summary>
    /// 停止发送
    /// </summary>
    /// <param name="sendingId">发送序号</param>
    /// <param name="cancellationToken"></param>
    Task StopSending(uint sendingId, CancellationToken cancellationToken);

    /// <summary>
    /// 清理缓存
    /// </summary>
    /// <param name="cancellationToken"></param>
    Task CleanAsync(CancellationToken cancellationToken);

    /// <summary>
    /// 判断是否已经激活
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<bool> IsActivatedAsync(CancellationToken cancellationToken);

    /// <summary>
    /// 执行激活
    /// </summary>
    /// <param name="productKey"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task ActivateAsync(string productKey, CancellationToken cancellationToken);

    /// <summary>
    /// 获取 MCU 的 UID
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<string> GetSerialNumberAsync(CancellationToken cancellationToken);
}
