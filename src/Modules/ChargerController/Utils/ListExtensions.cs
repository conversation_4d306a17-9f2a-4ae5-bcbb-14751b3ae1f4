namespace ChargerController.Utils;

public static class ListExtensions
{
    public static List<T> AddWhen<T>(this List<T> list, bool condition, T newItem)
    {
        if (condition) list.Add(newItem);

        return list;
    }

    public static List<T> AddWhen<T>(this List<T> list, Func<T, bool> condition, T newItem)
    {
        if (condition == null) throw new ArgumentNullException(nameof(condition));
        if (condition(newItem))
            list.Add(newItem);

        return list;
    }
}
