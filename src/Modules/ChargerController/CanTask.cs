namespace ChargerController.IO;

public class CanTask
{
    /// <summary>
    /// 订阅Id
    /// </summary>
    public uint SubscriptingId { get; set; }

    /// <summary>
    /// 回调
    /// </summary>
    public Action<byte[]>? ResponseCallback { get; set; }

    /// <summary>
    /// 发送Id
    /// </summary>
    public uint? SendingId { get; set; }

    /// <summary>
    /// 周期
    /// </summary>
    public uint Period { get; set; }

    public DateTime NextQueryTime { get; set; }
}
