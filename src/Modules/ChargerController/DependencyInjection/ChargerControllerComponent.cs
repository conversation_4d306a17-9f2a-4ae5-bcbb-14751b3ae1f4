using ChargerController.Impl;
using DeviceGuard.Interface.Prism;

namespace ChargerController.DependencyInjection;

public class ChargerControllerComponent : IPrismComponent
{
    public void RegisterTypes(IContainerRegistry containerRegistry)
    {
        if (containerRegistry == null)
            throw new ArgumentNullException(nameof(containerRegistry));

        containerRegistry.Register<IChargerControllerClient, ChargerControllerClient>();
        containerRegistry.Register<IEscapePacketTransport, EscapePacketTransport>();
        containerRegistry.Register<IEscapePacketTransport, EscapePacketTransport>();
        containerRegistry.Register<ITransportFactory, BluetoothTransportFactory>("Bluetooth");
        containerRegistry.Register<ITransportFactory, SerialPortTransportFactory>("SerialPort");
        containerRegistry.Register<ITransportFactoryProducer, TransportFactoryProducer>();

        containerRegistry.Register<ITransportFacade, DefaultTransportFacade>();
    }
}
