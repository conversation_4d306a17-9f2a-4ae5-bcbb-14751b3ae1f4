syntax = "proto3";

// import "nanopb.proto";
package grpc;

service CanLogicService {
    // 读取CAN接收缓存
    rpc CanRxCacheRead(CanRxCacheReadRequest) returns (CanRxCacheReadResponse);

    // 读取CAN发送缓存
    rpc CanTxCacheRead(CanTxCacheReadRequest) returns (CanTxCacheReadResponse);

    // 配置CAN发送缓存
    rpc CanTxCacheCfg(CanTxCacheCfgRequest) returns (CanTxCacheCfgResponse);

    // 配置CAN接收缓存
    rpc CanRxCacheCfg(CanRxCacheCfgRequest) returns (CanRxCacheCfgResponse);

    // 清理CAN发送缓存
    rpc CanTxCacheClean(CanTxCacheCleanRequest) returns (CanTxCacheCleanResponse);

    // 清理CAN接收缓存
    rpc CanRxCacheClean(CanRxCacheCleanRequest) returns (CanRxCacheCleanResponse);
}

// 定义CAN接收缓存读取请求消息
message CanRxCacheReadRequest {
    uint32 cnt = 1;
}

// 定义CAN接收缓存读取响应消息
message CanRxCacheReadResponse {
    uint32 can_addr_type = 1;      // CAN地址类型
    uint32 can_id = 2;             // CAN消息ID
    bytes can_buff = 3;            // CAN缓冲区
    uint32 read_flag = 4;          // CAN读取标记 0：未读      1：已读
}

// 定义CAN发送缓存读取请求消息
message CanTxCacheReadRequest {
    uint32 cnt = 1;                 // 消息槽号
}

// 定义CAN发送缓存读取响应消息
message CanTxCacheReadResponse {
    uint32 can_addr_type = 1;        // CAN地址类型
    uint32 can_id = 2;               // CAN消息ID
    bytes can_buff = 3;              // CAN缓冲区
    uint32 tx_period = 4;            // CAN发送周期
    uint32 tx_times = 5;             // CAN发送次数
}

// 定义CAN发送缓存配置请求消息
message CanTxCacheCfgRequest {
    uint32 can_addr_type = 1;        // CAN地址类型
    uint32 can_id = 2;               // CAN消息ID
    bytes can_buff = 3;              // CAN缓冲区
    uint32 tx_period = 4;            // CAN发送周期
    uint32 tx_times = 5;             // CAN发送次数
    uint32 ID = 6;                   // 消息槽号
}

// 定义CAN发送缓存配置响应消息
message CanTxCacheCfgResponse {
    bool success = 1;                // 成功标记  1：成功   0：失败
    string message = 2;              // 预留
}

// 定义CAN发送遗嘱配置请求消息
message CanTxWillCfgRequest {
    uint32 can_addr_type = 1;        // CAN地址类型
    uint32 can_id = 2;               // CAN消息ID
    bytes can_buff = 3;              // CAN缓冲区
    uint32 tx_period = 4;            // CAN发送周期
    uint32 tx_times = 5;             // CAN发送次数
    uint32 ID = 6;                   // 消息槽号
}

// 定义CAN发送遗嘱配置响应消息
message CanTxWillCfgResponse {
    bool success = 1;                // 成功标记  1：成功   0：失败
    string message = 2;              // 预留
}

// 定义CAN接收缓存配置请求消息
message CanRxCacheCfgRequest {
    uint32 can_addr_type = 1;             // CAN地址类型
    uint32 can_id = 2;                    // CAN消息ID
    uint32 can_cont_fliter = 3;         // CAN内容过滤器
    bytes can_buff = 4;                   // CAN缓冲区
    uint32 ID = 5;                        // 消息槽号
}

// 定义CAN接收缓存配置响应消息
message CanRxCacheCfgResponse {
    bool success = 1;                   // 成功标记  1：成功   0：失败
    string message = 2;                 // 预留
}

// 定义CAN发送缓存清理请求消息
message CanTxCacheCleanRequest {
    bool status = 1;                    // 预留标记
}

// 定义CAN发送缓存清理响应消息
message CanTxCacheCleanResponse {
    bool success = 1;                   // 成功标记  1：成功   0：失败
    string message = 2;                 // 预留
}

// 定义CAN接收缓存清理请求消息
message CanRxCacheCleanRequest {
    bool status = 1;                   // 预留标记
}

// 定义CAN接收缓存清理响应消息
message CanRxCacheCleanResponse {
    bool success = 1;                   // 成功标记  1：成功   0：失败
    string message = 2;                 // 预留标记
}

// 定义CAN波特率设置请求消息
message CanBaudRateRequest {
    uint32 baud_rate = 1;               // CAN波特率 1:1Mbps 2:800Kbps 3:500Kbps 4:250Kbps 5:125Kbps
}

// 定义CAN波特率设置响应消息
message CanBaudRateResponse {
    bool success = 1;                   // 成功标记  1：成功   0：失败
}

// 定义激活UID请求消息
message ActiveUIDGetRequest {
    uint32 ask = 1;                      // ask
}

// 定义激活UID响应消息
message ActiveUIDGetResponse {
    uint32 uid_1 = 1;                    // uid
    uint32 uid_2 = 2;                    // uid
    uint32 uid_3 = 3;                    // uid
    uint32 uid_crc = 4;                  // uid-crc
}

// 定义激活码获取请求消息
message ActiveCodeGetRequest {
    uint32 ask = 1;                      // ask
}

// 定义激活码获取响应消息
message ActiveCodeGetResponse {
    uint32 code_1 = 1;                   // code1
    uint32 code_2 = 2;                   // code2
    uint32 code_3 = 3;                   // code3
    uint32 code_4 = 4;                   // code4
}

// 定义激活码配置请求信息
message ActiveCodeSetRequest {
    uint32 code_1 = 1;                   // code1
    uint32 code_2 = 2;                   // code2
    uint32 code_3 = 3;                   // code3
    uint32 code_4 = 4;                   // code4
}

// 定义激活码配置响应信息
message ActiveCodeSetResponse {
    uint32 code_1 = 1;                   // code1
    uint32 code_2 = 2;                   // code2
    uint32 code_3 = 3;                   // code3
    uint32 code_4 = 4;                   // code4
}

// 统一的请求消息
message Request {
    oneof msg {
        CanRxCacheReadRequest can_rx_cache_read_request = 1;
        CanTxCacheReadRequest can_tx_cache_read_request = 2;
        CanTxCacheCfgRequest can_tx_cache_cfg_request = 3;
        CanRxCacheCfgRequest can_rx_cache_cfg_request = 4;
        CanTxCacheCleanRequest can_tx_cache_clean_request = 5;
        CanRxCacheCleanRequest can_rx_cache_clean_request = 6;
        CanBaudRateRequest can_baud_rate_request = 7;
        ActiveUIDGetRequest active_uid_get_request = 8;
        ActiveCodeGetRequest active_code_get_request = 9;
        ActiveCodeSetRequest active_code_set_request = 10;
        CanTxWillCfgRequest can_tx_will_cfg_request = 11;
    }
}

// 统一的响应消息
message Response {
    oneof msg {
        CanRxCacheReadResponse can_rx_cache_read_response = 1;
        CanTxCacheReadResponse can_tx_cache_read_response = 2;
        CanTxCacheCfgResponse can_tx_cache_cfg_response = 3;
        CanRxCacheCfgResponse can_rx_cache_cfg_response = 4;
        CanTxCacheCleanResponse can_tx_cache_clean_response = 5;
        CanRxCacheCleanResponse can_rx_cache_clean_response = 6;
        CanBaudRateResponse can_baud_rate_response = 7;
        ActiveUIDGetResponse active_uid_get_response = 8;
        ActiveCodeGetResponse active_code_get_response = 9;
        ActiveCodeSetResponse active_code_set_response = 10;
        CanTxWillCfgResponse can_tx_will_cfg_response = 11;
    }
}