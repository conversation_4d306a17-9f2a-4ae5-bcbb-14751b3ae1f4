<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net6.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <LangVersion>13</LangVersion>
        <Platforms>AnyCPU;x64</Platforms>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="RequiredMemberAttribute" Version="1.0.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Doulex" Version="5.16.0"/>
        <PackageReference Include="InTheHand.Net.Bluetooth" Version="4.2.1"/>
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3"/>
        <PackageReference Include="Serilog" Version="4.2.0"/>
        <PackageReference Include="System.IO.Ports" Version="6.0.0" />
        <PackageReference Include="Google.Protobuf" Version="3.29.3"/>
        <PackageReference Include="Grpc.Net.Client" Version="2.67.0"/>
        <PackageReference Include="Grpc.Tools" Version="2.69.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="System.Management" Version="6.0.2" />
    </ItemGroup>

    <ItemGroup>
        <Protobuf Include="Protos\CanRpc.proto">
            <Generator>MSBuild:Compile</Generator>
        </Protobuf>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Interfaces\DeviceGuard.Interface\DeviceGuard.Interface.csproj"/>
    </ItemGroup>

</Project>
