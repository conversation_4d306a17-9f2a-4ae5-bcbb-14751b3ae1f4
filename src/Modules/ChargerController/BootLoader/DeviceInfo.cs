namespace ChargerController.BootLoader;

/// <summary>
/// 设备信息，包含设备的基本信息和硬件特性
/// </summary>
public class DeviceInfo
{
    // /// <summary>
    // /// 设备唯一标识符
    // /// </summary>
    // public string DeviceId { get; set; } = "";
    //
    // /// <summary>
    // /// 设备名称
    // /// </summary>
    // public string DeviceName { get; set; } = "";

    /// <summary>
    /// 硬件版本
    /// </summary>
    public string HardwareVersion { get; set; } = "";

    /// <summary>
    /// 固件版本
    /// </summary>
    public string FirmwareVersion { get; set; } = "";

    // /// <summary>
    // /// BootLoader 版本
    // /// </summary>
    // public string BootLoaderVersion { get; set; } = "";
    //
    // /// <summary>
    // /// 制造商
    // /// </summary>
    // public string Manufacturer { get; set; } = "";

    /// <summary>
    /// 产品型号
    /// </summary>
    public string ProductModel { get; set; } = "";

    // /// <summary>
    // /// 序列号
    // /// </summary>
    // public string SerialNumber { get; set; } = "";
    //
    // /// <summary>
    // /// Flash 存储器大小 (字节)
    // /// </summary>
    // public uint FlashSize { get; set; }
    //
    // /// <summary>
    // /// RAM 大小 (字节)
    // /// </summary>
    // public uint RamSize { get; set; }

    // /// <summary>
    // /// 应用程序起始地址
    // /// </summary>
    // public uint ApplicationStartAddress { get; set; }
    //
    // /// <summary>
    // /// 应用程序大小 (字节)
    // /// </summary>
    // public uint ApplicationSize { get; set; }

    // /// <summary>
    // /// 页大小 (字节) - 用于擦除操作
    // /// </summary>
    // public uint PageSize { get; set; }
    //
    // /// <summary>
    // /// 设备是否支持在线升级
    // /// </summary>
    // public bool SupportsOtaUpdate { get; set; }

    /// <summary>
    /// 设备当前状态
    /// </summary>
    // public DeviceStatus Status { get; set; } = DeviceStatus.Unknown;

    // /// <summary>
    // /// 最后通信时间
    // /// </summary>
    // public DateTime LastCommunicationTime { get; set; } = DateTime.MinValue;
    //
    // /// <summary>
    // /// 扩展属性，用于存储设备特定的额外信息
    // /// </summary>
    // public Dictionary<string, object> ExtendedProperties { get; set; } = new();
    //
    // public override string ToString()
    // {
    //     return $"{DeviceName} ({ProductModel}) - FW: {FirmwareVersion}, HW: {HardwareVersion}";
    // }
}

// /// <summary>
// /// 设备状态枚举
// /// </summary>
// public enum DeviceStatus
// {
//     /// <summary>
//     /// 未知状态
//     /// </summary>
//     Unknown,
//
//     /// <summary>
//     /// 设备正常运行
//     /// </summary>
//     Running,
//
//     /// <summary>
//     /// 设备处于 BootLoader 模式
//     /// </summary>
//     BootLoaderMode,
//
//     /// <summary>
//     /// 设备离线
//     /// </summary>
//     Offline,
//
//     /// <summary>
//     /// 设备错误
//     /// </summary>
//     Error,
//
//     /// <summary>
//     /// 设备正在更新固件
//     /// </summary>
//     Updating
// }
