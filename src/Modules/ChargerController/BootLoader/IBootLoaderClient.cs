namespace ChargerController.BootLoader;

/// <summary>
/// BootLoader 客户端接口，用于设备固件更新和引导加载程序通信
/// </summary>
public interface IBootLoaderClient
{
    /// <summary>
    /// 是否已经连接了设备
    /// </summary>
    bool IsOpen { get; }

    /// <summary>
    /// 使用指定的传输层
    /// </summary>
    /// <param name="transport">传输层接口</param>
    void Use(ITransport transport);

    /// <summary>
    /// 关闭连接
    /// </summary>
    void Close();

    /// <summary>
    /// 退出 BootLoader 模式，重启设备到应用程序模式
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否成功退出 BootLoader 模式</returns>
    Task ExitBootLoaderModeAsync(CancellationToken cancellationToken);

    /// <summary>
    /// 检查设备是否处于 BootLoader 模式
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>设备是否处于 BootLoader 模式</returns>
    Task<bool> IsInBootLoaderModeAsync(CancellationToken cancellationToken);

    /// <summary>
    /// 获取设备信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>设备信息</returns>
    Task<DeviceInfo> GetDeviceInfoAsync(CancellationToken cancellationToken);


    /// <summary>
    /// 写入固件数据
    /// </summary>
    /// <param name="version">软件版本号</param>
    /// <param name="data">固件数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否写入成功</returns>
    Task<bool> WriteFirmwareAsync(string version, byte[] data, CancellationToken cancellationToken);

    /// <summary>
    /// 写入 AES 密钥
    /// </summary>
    /// <param name="key">AES 密钥数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>表示异步操作的任务</returns>
    Task WriteAesKeyAsync(byte[] key, CancellationToken cancellationToken);


    /// <summary>
    /// 写入 AES 初始化向量（IV）
    /// </summary>
    /// <param name="iv">AES 初始化向量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>异步任务</returns>
    Task WriteAesIvAsync(byte[] iv, CancellationToken cancellationToken);
}
