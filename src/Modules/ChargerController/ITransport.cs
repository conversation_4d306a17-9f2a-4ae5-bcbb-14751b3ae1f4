using ChargerController.Impl;

// 定义一个命名空间，用于包含充电器控制器相关的类和接口
namespace ChargerController;

/// <summary>
/// 通讯接口
/// </summary>
public interface ITransport : IDisposable
{
    /// <summary>
    /// 名称
    /// </summary>
    string Name { get; }

    /// <summary>
    /// 获取当前串口是否处于打开状态
    /// </summary>
    public bool IsOpen { get; }

    /// <summary>
    /// TODO 后期重构, 取消串口参数
    /// 打开串口进行通讯
    /// </summary>
    /// <param name="parameter">串口参数</param>
    public void Open(ITransportParameter parameter);

    /// <summary>
    /// 关闭串口通讯
    /// </summary>
    public void Close();

    /// <summary>
    /// 获取当前串口接收缓冲区中等待读取的字节数
    /// </summary>
    public bool DataAvailable { get; }

    /// <summary>
    /// 从串口读取一个字节的数据
    /// </summary>
    /// <returns>读取到的字节</returns>
    public int ReadByte();

    int Read(byte[] buffer, int offset, int count);

    /// <summary>
    /// 向串口写入数据
    /// </summary>
    /// <param name="buffer">待写入的数据缓冲区</param>
    /// <param name="offset">缓冲区中的起始位置</param>
    /// <param name="count">要写入的字节数</param>
    public void Write(byte[] buffer, int offset, int count);

    /// <summary>
    /// 清空串口的接收缓冲区
    /// </summary>
    public void DiscardReceiveBuffer();
}
