using System.IO.Ports;

namespace ChargerController;

public static class SerialPortExtensions
{
    public static string ToShort(this Parity parity)
    {
        return parity switch
        {
            Parity.None => "N",
            Parity.Odd => "O",
            Parity.Even => "E",
            Parity.Mark => "M",
            Parity.Space => "S",
            _ => throw new ArgumentOutOfRangeException(nameof(parity), parity, null)
        };
    }

    public static string ToShort(this StopBits stopBits)
    {
        return stopBits switch
        {
            StopBits.None => "0",
            StopBits.One => "1",
            StopBits.Two => "2",
            StopBits.OnePointFive => "1.5",
            _ => throw new ArgumentOutOfRangeException(nameof(stopBits), stopBits, null)
        };
    }
}
