using System.Collections.Immutable;

namespace ChargerController;

public class TransportFactoryProducer : ITransportFactoryProducer
{
    private readonly ImmutableDictionary<TransportType, ITransportFactory> _transportFactories;

    public TransportFactoryProducer(IEnumerable<ITransportFactory> transportFactories)
    {
        _transportFactories = transportFactories.ToImmutableDictionary(t => t.SupportedType);
    }

    public ITransportFactory CreateTransportFactory(TransportType type)
    {
        if (!_transportFactories.TryGetValue(type, out var transportFactory))
        {
            throw new ArgumentException($"{type} is not supported");
        }

        return transportFactory;
    }
}