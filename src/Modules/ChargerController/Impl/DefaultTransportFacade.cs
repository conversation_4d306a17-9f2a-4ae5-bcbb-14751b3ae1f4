namespace ChargerController.Impl;

/// <inheritdoc />
public class DefaultTransportFacade : ITransportFacade
{
    private readonly ITransportFactoryProducer _transportFactoryProducer;

    public DefaultTransportFacade(ITransportFactoryProducer transportFactoryProducer)
    {
        _transportFactoryProducer = transportFactoryProducer;
    }

    /// <summary>
    /// 查找发现支持的传输层
    /// </summary>
    /// <param name="transportTypes">要查找的, 传输层类型</param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task<CFDevice[]> DiscoverAsync(TransportType[] transportTypes, CancellationToken cancellationToken)
    {
        var transportDiscoveries = transportTypes.Select(t => _transportFactoryProducer.CreateTransportFactory(t).CreateTransportDiscovery()).ToArray();

        var tasks   = transportDiscoveries.Select(dis => dis.DiscoverAsync(cancellationToken)).ToArray();
        var results = await Task.WhenAll(tasks);

        var q = from r in results.SelectMany(r => r)
                where r.DeviceType is DeviceType.ChargerDevice or DeviceType.Stm32Usb
                select r;

        return q.ToArray();
    }

    public Task<ITransport> CreateTransport(CFDevice device, CancellationToken cancellationToken)
    {
        var factory = _transportFactoryProducer.CreateTransportFactory(device.TransportType);
        return factory.CreateTransport(device.Address, cancellationToken);
    }
}
