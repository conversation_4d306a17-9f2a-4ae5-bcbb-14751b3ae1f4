namespace ChargerController.Impl;

public class BluetoothTransportFactory : ITransportFactory
{
    public TransportType SupportedType => TransportType.Bluetooth;

    public async Task<ITransport> CreateTransport(string portName, CancellationToken cancellationToken)
    {
        var transport = new BluetoothTransport();
        var parameter = new BluetoothParameter
        {
            DeviceAddress = portName
        };
        await Task.Run(() => transport.Open(parameter), cancellationToken);

        return transport;
    }

    public ITransportDiscovery CreateTransportDiscovery()
    {
        return new BluetoothDiscovery();
    }
}
