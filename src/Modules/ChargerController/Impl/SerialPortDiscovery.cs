using System.Diagnostics.CodeAnalysis;
using System.IO.Ports;
using System.Management;

namespace ChargerController.Impl;

[SuppressMessage("Interoperability", "CA1416:Validate platform compatibility")]
public class SerialPortDiscovery : ITransportDiscovery
{
    public Task<CFDevice[]> DiscoverAsync(CancellationToken cancellationToken)
    {
        var ports     = SerialPort.GetPortNames().ToList();
        var cfDevices = new List<CFDevice>();

        using (var searcher = new ManagementObjectSearcher(
                   "SELECT * FROM Win32_PnPEntity WHERE Name LIKE '%(COM%'"))
        {
            foreach (var device in searcher.Get())
            {
                string name     = device["Name"]?.ToString() ?? "";
                string deviceId = device["PNPDeviceID"]?.ToString() ?? "";

                var portName = ports.FirstOrDefault(x => name.Contains(x));
                if (string.IsNullOrEmpty(portName))
                {
                    continue;
                }

                ports.Remove(portName);

                // 提取 VID/PID
                string vid = ExtractFromDeviceId(deviceId, "VID_");
                string pid = ExtractFromDeviceId(deviceId, "PID_");

                if (vid == "0483" && pid == "5740")
                {
                    cfDevices.Add(new CFDevice
                    {
                        Name          = $"串口 - STM32 设备 ({portName})",
                        Address       = portName,
                        DeviceType    = DeviceType.Stm32Usb,
                        TransportType = TransportType.SerialPort,
                    });
                }
            }
        }

        // 把剩下的 COM 端口加入到结果中
        ports.ForEach(p => cfDevices.Add(new CFDevice
        {
            Name          = $"串口 ({p})",
            Address       = p,
            DeviceType    = DeviceType.Unknown,
            TransportType = TransportType.SerialPort,
        }));

        return Task.FromResult(cfDevices.ToArray());
    }

    static string ExtractFromDeviceId(string deviceId, string prefix)
    {
        var start = deviceId.IndexOf(prefix, StringComparison.Ordinal);
        if (start >= 0 && deviceId.Length >= start + 8)
        {
            return deviceId.Substring(start + 4, 4);
        }

        return "N/A";
    }
}
