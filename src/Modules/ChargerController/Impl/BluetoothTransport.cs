using System.Buffers;
using System.Net.Sockets;
using Doulex;
using InTheHand.Net.Sockets;
using InTheHand.Net.Bluetooth;
using InTheHand.Net;

namespace ChargerController.Impl
{
    public class BluetoothTransport : ITransport
    {
        private BluetoothClient? _bluetoothClient = null;

        private NetworkStream? _stream;

        public BluetoothTransport()
        {
            _bluetoothClient = new BluetoothClient();
        }

        public void Dispose()
        {
            Close();
            _bluetoothClient?.Dispose();
        }

        public string Name   { get; private set; } = "";
        public bool   IsOpen => _bluetoothClient is { Connected: true };

        public void Open(ITransportParameter parameter)
        {
            if (parameter is not BluetoothParameter bluetoothParam)
            {
                throw new ArgumentException("Invalid transport parameter type");
            }

            if (string.IsNullOrEmpty(bluetoothParam.DeviceAddress))
                throw new ArgumentException("Invalid device address");

            if (!BluetoothAddress.TryParse(bluetoothParam.DeviceAddress, out var address))
                throw new ArgumentException("Invalid bluetooth device address");

            var endpoint = new BluetoothEndPoint(address, BluetoothService.SerialPort);
            _bluetoothClient = new BluetoothClient();
            _bluetoothClient.Connect(endpoint);
            _stream = _bluetoothClient.GetStream();
            Name    = $"蓝牙 '{bluetoothParam.DeviceAddress}'";
        }

        public void Close()
        {
            _bluetoothClient?.Close();
            _bluetoothClient = null;
        }

        public bool DataAvailable => _stream?.DataAvailable == true;

        public int ReadByte()
        {
            if (_bluetoothClient is not { Connected: true })
                throw new InvalidOperationException("Bluetooth socket is not connected");

            if (_stream is null)
                throw new InvalidOperationException("Bluetooth stream is not available");

            return _stream.ReadByte();
        }

        public int Read(byte[] buffer, int offset, int count)
        {
            if (_bluetoothClient is not { Connected: true })
                throw new InvalidOperationException("Bluetooth socket is not connected");

            if (_stream is null)
                throw new InvalidOperationException("Bluetooth stream is not available");

            return _stream.Read(buffer, offset, count);
        }

        public void Write(byte[] buffer, int offset, int count)
        {
            if (_bluetoothClient is not { Connected: true })
                throw new InvalidOperationException("Bluetooth socket is not connected");

            if (_stream is null)
                throw new InvalidOperationException("Bluetooth stream is not available");

            _stream.Write(buffer, offset, count);
        }

        public void DiscardReceiveBuffer()
        {
            if (_bluetoothClient is not { Connected: true })
                throw new InvalidOperationException("Bluetooth socket is not connected");

            if (_stream is null)
                throw new InvalidOperationException("Bluetooth stream is not available");

            using var memory = MemoryPool<byte>.Shared.Rent(100);
            var       buffer = memory.Memory.Span;
            while (true)
            {
                if (!_stream.DataAvailable)
                    break;

                var read = _stream.Read(buffer);
                if (read <= 0)
                {
                    break;
                }
            }
        }
    }
}
