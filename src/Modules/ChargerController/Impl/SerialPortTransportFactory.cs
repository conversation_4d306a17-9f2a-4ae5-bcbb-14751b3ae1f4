using System.IO.Ports;

namespace ChargerController.Impl;

public class SerialPortTransportFactory : ITransportFactory
{
    public TransportType SupportedType => TransportType.SerialPort;

    public async Task<ITransport> CreateTransport(string portName, CancellationToken cancellationToken)
    {
        var transport = new SerialPortTransport();
        var parameter = new SerialPortParameter
        {
            PortName = portName,
            BaudRate = 115200,
            Parity   = Parity.None,
            StopBits = StopBits.One,
            DataBits = 8
        };
        await Task.Run(() => transport.Open(parameter), cancellationToken);

        return transport;
    }

    public ITransportDiscovery CreateTransportDiscovery()
    {
        return new SerialPortDiscovery();
    }
}
