using InTheHand.Net.Sockets;

namespace ChargerController.Impl;

public class BluetoothDiscovery : ITransportDiscovery
{
    public async Task<CFDevice[]> DiscoverAsync(CancellationToken cancellationToken)
    {
        var r = await Task.Run(() =>
            {
                var client = new BluetoothClient();
                var ports  = client.DiscoverDevices();
                if (ports == null)
                {
                    return [];
                }

                var results = new List<CFDevice>();
                foreach (var port in ports)
                {
                    if (!port.DeviceName.StartsWith("CF"))
                        continue;

                    switch (port.DeviceName)
                    {
                        case "CFCD":
                            results.Add(new CFDevice
                            {
                                Name          = $"蓝牙 - 充电桩设备卫士 ({port.DeviceAddress})",
                                Address       = port.DeviceAddress.ToString(),
                                DeviceType    = DeviceType.ChargerDevice,
                                TransportType = TransportType.Bluetooth,
                            });
                            break;
                    }
                }

                return results;
            },
            cancellationToken);

        return r.ToArray();
    }
}
