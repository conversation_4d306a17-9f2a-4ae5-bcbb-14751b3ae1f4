using System.Diagnostics.CodeAnalysis;
using Serilog;

namespace ChargerController.Impl;

/// <summary>
/// BS-BUS 协议服务，用于发送和接收数据
/// </summary>
public class EscapePacketTransport : IEscapePacketTransport, IDisposable
{
    private readonly ILogger     _logger;
    private          ITransport? _transport = null;

    public EscapePacketTransport(ILogger logger)
    {
        _logger = logger;
    }


    /// <summary>
    /// 用于通讯的信号量, 防止同时收发数据
    /// </summary>
    private readonly SemaphoreSlim _commSemaphore = new(1, 1);

    /// <summary>
    /// 当前状态
    /// </summary>
    public CommStatus Status { get; private set; }

    /// <summary>
    /// 记录最后一次发送错误的状态
    /// </summary>
    public CommSendingErrorStatus SendingErrorStatus { get; private set; }

    /// <summary>
    /// 记录最后一次接收错误的状态
    /// </summary>
    public CommReceivingErrorStatus ReceivingErrorStatus { get; private set; }

    /// <summary>
    /// 已接收的报文数量
    /// </summary>
    public int ReceivedPackets { get; private set; } = 0;

    /// <summary>
    /// 已接收的字节数
    /// </summary>
    public int ReceivedBytes { get; private set; } = 0;

    /// <summary>
    /// 已发送报文的数量
    /// </summary>
    public int SentPackets { get; private set; } = 0;

    /// <summary>
    /// 已发送的字节数
    /// </summary>
    public int SentBytes { get; private set; } = 0;


    /// <summary>
    /// 是否已连接了设备
    /// </summary>
    [MemberNotNullWhen(true, nameof(_transport))]
    public bool Connected => _transport?.IsOpen == true;

    /// <summary>
    /// 接收超时时间
    /// </summary>
    public TimeSpan ReceiveTimeout { get; set; } = TimeSpan.FromSeconds(2);

    #region Connection

    public void UseTransport(ITransport transport)
    {
        _transport = transport ?? throw new ArgumentNullException(nameof(transport));
    }

    #endregion

    #region Sending

    /// <summary>
    /// 发送报文
    /// </summary>
    /// <param name="payload"></param>
    /// <param name="cancellationToken"></param>
    /// <exception cref="Exception"></exception>
    public async Task SendAsync(Memory<byte> payload, CancellationToken cancellationToken)
    {
        if (!Connected)
            throw new Exception("The client is closed");

        // 生成数据包
        var buffer = GeneratePacket(payload.Span);

        await _commSemaphore.WaitAsync(cancellationToken);
        try
        {
            Status = CommStatus.Sending;

            _transport.Write(buffer, 0, buffer.Length);
            SendingErrorStatus = CommSendingErrorStatus.None;
        }
        catch (Exception ex)
        {
            SendingErrorStatus = ex switch
            {
                TimeoutException => CommSendingErrorStatus.Timeout,
                _                => CommSendingErrorStatus.Unknown
            };

            throw;
        }
        finally
        {
            Status = CommStatus.Idle;
            _commSemaphore.Release();
        }
    }

    /// <summary>
    /// 获取发送数据包
    /// </summary>
    /// <param name="payload"></param>
    /// <returns></returns>
    private byte[] GeneratePacket(Span<byte> payload)
    {
        var header = new byte[] { 0x27, 0x02 };
        var footer = new byte[] { 0x27, 0x03 };

        // 准备数据
        using var ms = new MemoryStream();
        ms.Write(header, 0, header.Length);

        foreach (var t in payload)
        {
            ms.WriteByte(t);

            // 内容如果是 0x27，需要转义
            if (t == 0x27)
                ms.WriteByte(t);
        }

        ms.Write(footer, 0, footer.Length);

        // 返回生成的数据
        var buf = ms.ToArray();
        return buf;
    }

    #endregion

    #region Receving

    /// <summary>
    /// 接受一个报文
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    public async Task<byte[]> ReceivePacketAsync(CancellationToken cancellationToken)
    {
        if (!Connected)
        {
            throw new Exception("The client is closed");
        }

        await _commSemaphore.WaitAsync(cancellationToken);
        try
        {
            Status = CommStatus.Receiving;
            var timeout = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);
            if (ReceiveTimeout > TimeSpan.Zero)
            {
                timeout.CancelAfter(ReceiveTimeout);
            }

            var buffer = await ReceivePacketCoreAsync(timeout.Token);
            ReceivingErrorStatus = CommReceivingErrorStatus.None;

            return buffer;
        }
        catch (Exception ex)
        {
            ReceivingErrorStatus = ex switch
            {
                TimeoutException => CommReceivingErrorStatus.Timeout,
                _                => CommReceivingErrorStatus.Unknown
            };

            throw;
        }
        finally
        {
            Status = CommStatus.Idle;
            _commSemaphore.Release();
        }
    }

    /// <summary>
    /// 接收一个报文逻辑
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    private async Task<byte[]> ReceivePacketCoreAsync(CancellationToken cancellationToken)
    {
        if (!Connected)
        {
            throw new Exception("The client is closed");
        }

        var buffer         = new byte[1024]; // todo 考虑租用内存
        var pos            = 0;
        var escapeMode     = false;
        var headerExpected = true;

        try
        {
            while (pos < buffer.Length && !cancellationToken.IsCancellationRequested)
            {
                if (!_transport.DataAvailable)
                {
                    await Task.Delay(10, cancellationToken);
                    continue;
                }

                var data = (byte)_transport.ReadByte();

                if (!escapeMode)
                {
                    if (data == 0x27)
                    {
                        escapeMode = true;
                        continue;
                    }

                    // 如果没有收到报文头，丢弃数据
                    if (headerExpected)
                    {
                        continue;
                    }

                    // 写入数据
                    buffer[pos++] = data;
                }
                else
                {
                    if (data == 0x02)
                    {
                        if (pos > 0)
                        {
                            var format = string.Join(" ", buffer[..pos].Select(x => x.ToString("X2")));
                            _logger.Warning($"Found new packet header. Drop current data ({pos}) {format}");
                        }

                        headerExpected = false; // 我们读取到了报文头
                        pos            = 0;
                        escapeMode     = false;
                        continue;
                    }

                    if (data == 0x27)
                    {
                        // 写入数据
                        buffer[pos++] = data;
                        escapeMode    = false;
                        continue;
                    }

                    if (data == 0x03)
                    {
                        if (headerExpected)
                        {
                            _logger.Warning(
                                $"Found packet footer, but header is expected, drop data, continue to receive");
                            continue;
                        }

                        return buffer[..pos];
                    }
                }
            }

            throw new OperationCanceledException();
        }
        catch (OperationCanceledException)
        {
            throw new TimeoutException("Receive timeout");
        }
    }

    /// <summary>
    /// 清除接收缓冲区
    /// </summary>
    public void ClearReceiveBuffer()
    {
        if (!Connected)
        {
            throw new Exception("The client is closed");
        }

        // 如果出现了异常，需要清除缓冲区
        _transport.DiscardReceiveBuffer();
    }

    #endregion

    public void Dispose()
    {
        _transport?.Dispose();
        _commSemaphore.Dispose();
    }
}
