using System.IO.Ports;


namespace ChargerController.Impl;

public class SerialPortParameter : ITransportParameter
{
    /// <summary>
    /// 串口名
    /// </summary>
    public string PortName { get; set; } = "";

    /// <summary>
    /// 波特率
    /// </summary>
    public int BaudRate { get; set; }

    /// <summary>
    /// 校验位 
    /// </summary>
    public Parity Parity { get; set; }

    /// <summary>
    /// 停止位
    /// </summary>
    public StopBits StopBits { get; set; }

    /// <summary>
    /// 数据位
    /// </summary>
    public int DataBits { get; set; }

    /// <summary>Returns a string that represents the current object.</summary>
    /// <returns>A string that represents the current object.</returns>
    public override string? ToString()
    {
        return $"{PortName} {BaudRate} {DataBits}{Parity.ToShort()}{StopBits.ToShort()}";
    }
}
