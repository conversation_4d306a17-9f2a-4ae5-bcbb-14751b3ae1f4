namespace ChargerController.Impl;

public class Crc16
{
    /// <summary>
    /// CRC 校验
    /// </summary>
    /// <param name="crcPtr">数组指针</param>
    /// <param name="len">长度</param>
    /// <returns>CRC 校验值</returns>
    public static ushort Calc(Span<byte> crcPtr)
    {
        ushort crcValue = 0;
        byte i = 0;
        byte j = 0;

        crcValue = 0xffff;
        for (i = 0; i < crcPtr.Length; i++)  // len 为数组长度
        {
            crcValue ^= crcPtr[i];
            for (j = 0; j < 8; j++)
            {
                if ((crcValue & 0x0001) != 0)
                    crcValue = (ushort)((crcValue >> 1) ^ 0xA001);
                else
                    crcValue = (ushort)(crcValue >> 1);
            }
        }
        crcValue = (ushort)((crcValue >> 8) + (crcValue << 8)); // 交换高低字节

        return crcValue;
    }
}
