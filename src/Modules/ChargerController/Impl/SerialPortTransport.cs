using System.IO.Ports;

namespace ChargerController.Impl;

/// <summary>
/// SystemSerialPort 类实现了 ISerialPort 接口，提供串口通信的功能。
/// </summary>
public class SerialPortTransport : ITransport
{
    /// <summary>
    /// _com 是 System.IO.Ports.SerialPort 的一个实例，用于实际的串口通信。
    /// </summary>
    private readonly SerialPort _com = new();

    public string Name { get; private set; } = "";

    /// <summary>
    /// 获取当前串口是否处于打开状态。
    /// </summary>
    public bool IsOpen => _com.IsOpen;

    /// <summary>
    /// 打开串口进行通信。
    /// </summary>
    /// <param name="parameter">包含串口初始化的所有参数，如波特率、奇偶校验位等。</param>
    public void Open(ITransportParameter parameter)
    {
        var para = parameter as SerialPortParameter;
        if (para == null)
        {
            throw new ArgumentException("参数类型不匹配");
        }

        // 根据参数配置串口
        _com.PortName = para.PortName;
        _com.BaudRate = para.BaudRate;
        _com.Parity   = para.Parity;
        _com.StopBits = para.StopBits;
        _com.DataBits = para.DataBits;

        // 打开串口
        _com.Open();
        Name = $"串口 '{para.PortName}'";
    }

    /// <summary>
    /// 关闭已打开的串口。
    /// </summary>
    public void Close()
    {
        _com.Close();
    }

    /// <summary>
    /// 获取当前串口缓冲区中可用于读取的字节数。
    /// </summary>
    public bool DataAvailable => _com.BytesToRead > 0;

    /// <summary>
    /// 从串口读取一个字节的数据。
    /// </summary>
    /// <returns>读取到的字节数据。</returns>
    public int ReadByte()
    {
        return _com.ReadByte();
    }

    /// <summary>
    /// 从串口读取一系列字节的数据。
    /// </summary>
    /// <param name="buffer">存储读取数据的缓冲区。</param>
    /// <param name="offset">缓冲区的起始位置。</param>
    /// <param name="count">要读取的字节数。</param>
    /// <returns>实际读取到的字节数。</returns>
    public int Read(byte[] buffer, int offset, int count)
    {
        return _com.Read(buffer, offset, count);
    }

    /// <summary>
    /// 向串口写入一系列字节的数据。
    /// </summary>
    /// <param name="buffer">待写入的数据缓冲区。</param>
    /// <param name="offset">缓冲区的起始位置。</param>
    /// <param name="count">要写入的字节数。</param>
    public void Write(byte[] buffer, int offset, int count)
    {
        _com.Write(buffer, offset, count);
    }

    /// <summary>
    /// 清除串口的输入缓冲区。
    /// </summary>
    public void DiscardReceiveBuffer()
    {
        _com.DiscardInBuffer();
    }

    /// <summary>
    /// 清除串口的输出缓冲区。
    /// </summary>
    public void DiscardOutBuffer()
    {
        _com.DiscardOutBuffer();
    }

    /// <summary>
    /// 释放串口资源。
    /// </summary>
    public void Dispose()
    {
        _com.Dispose();
    }
}
