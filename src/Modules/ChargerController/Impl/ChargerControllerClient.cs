using System.Buffers;
using System.Buffers.Binary;
using System.Collections.Concurrent;
using System.Globalization;
using System.Text.RegularExpressions;
using ChargerController.IO;
using Google.Protobuf;
using Grpc;
using Newtonsoft.Json;

namespace ChargerController.Impl;

public class ChargerControllerClient : IAsyncDisposable, IChargerControllerClient
{
    private readonly IEscapePacketTransport _client;

    private Task? _workingThread;

    private CancellationTokenSource? _workingThreadTokenSource;
    public bool IsOpen { get; private set; }

    public ChargerControllerClient(IEscapePacketTransport client)
    {
        _client = client;
        Start();
    }

    public void Use(ITransport transport)
    {
        if (!transport.IsOpen)
        {
            throw new ArgumentException("Transport is not open", nameof(transport));
        }

        _transport = transport;
        _client.UseTransport(transport);
        IsOpen = true;
    }

    public void Close()
    {
        _transport?.Close();
        IsOpen = false;
    }

    /// <summary>
    /// 设置发送的遗嘱数据, 在上位机与检测仪直接意外断开连接时, 检测仪将按照该预先设定发送遗嘱报文, 例如停机命令
    /// </summary>
    /// <param name="id">写入到控制板保存的序号, [0,49] 一共50个槽</param>
    /// <param name="type">CAN 类型</param>
    /// <param name="requestAddress">每次数据请求地址</param>
    /// <param name="requestPayload">请求数据</param>
    /// <param name="requestPeriod">请求间隔(ms)</param>
    /// <param name="requestTimes">总共请求的次数</param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task SetupWillDataAsync(int id,
        CanType type,
        uint requestAddress,
        byte[] requestPayload,
        uint requestPeriod,
        uint requestTimes,
        CancellationToken cancellationToken)
    {
        if (requestPeriod == 0) throw new ArgumentOutOfRangeException(nameof(requestPeriod));
        if (requestAddress == 0) throw new ArgumentOutOfRangeException(nameof(requestAddress));
        if (id < 0 || id > 49) throw new ArgumentOutOfRangeException(nameof(id));

        try
        {
            var payload = new byte[8];
            Buffer.BlockCopy(requestPayload, 0, payload, 0, Math.Min(requestPayload.Length, 8));

            var request = new Request()
            {
                CanTxWillCfgRequest = new CanTxWillCfgRequest()
                {
                    ID = (uint)id,
                    CanAddrType = (uint)(type == CanType.Standard ? 0x00 : 0x01),
                    CanId = requestAddress,
                    CanBuff = ByteString.CopyFrom(payload),
                    TxPeriod = requestPeriod,
                    TxTimes = requestTimes,
                }
            };

            var response = await ExecuteMessageAsync(request, cancellationToken);
            var resp = response.CanTxWillCfgResponse;
            if (resp is not { Success: true })
            {
                throw new InvalidOperationException($"Setup WILL data failed: {resp.Message}");
            }
        }
        catch (Exception )
        {
            // BUG: 临时关闭设置遗嘱失败的报错, 后期请打开该功能, 增加 throw 或者取消 try
            //throw;
        }
    }

    private void Start()
    {
        if (_workingThread != null)
        {
            // TODO 提示首先停止
            return;
        }

        _workingThreadTokenSource = new CancellationTokenSource();
        _workingThread = Task.Run(async () => { await WorkingThread(_workingThreadTokenSource.Token); },
            _workingThreadTokenSource.Token);
    }

    private async Task StopAsync(CancellationToken cancellationToken)
    {
        var workingThread = _workingThread;
        var workingCancellationToken = _workingThreadTokenSource;

        if (workingCancellationToken != null)
        {
            workingCancellationToken.Cancel();
        }

        if (workingThread != null)
        {
            await workingThread.WaitAsync(TimeSpan.FromMilliseconds(500), cancellationToken);
        }

        _workingThreadTokenSource?.Dispose();
        _workingThread?.Dispose();

        _workingThreadTokenSource = null;
        _workingThread = null;
    }

    private async Task<Response> ExecuteMessageAsync(Request message, CancellationToken cancellationToken)
    {
        if (message == null) throw new ArgumentNullException(nameof(message));

        await _executeLock.WaitAsync(cancellationToken);
        int size = message.CalculateSize();
        var buffer = message.ToByteArray();
        using var request = MemoryPool<byte>.Shared.Rent(size + 4);
        byte[]? response = null;
        try
        {
            // The protocol see ref https://kmsmg.coding.net/p/zhiliuzhuangceshiyi/wiki/202

            // Encode the message
            var requestBuffer = request.Memory.Slice(0, size + 4);
            BinaryPrimitives.WriteInt16BigEndian(requestBuffer.Span, (short)size); // Length
            message.WriteTo(requestBuffer[2..^2].Span); // Payload
            var crcRequest = Crc16.Calc(requestBuffer[2..^2].Span);
            BinaryPrimitives.WriteUInt16BigEndian(requestBuffer[^2..].Span, crcRequest); // CRC

            // Prepare 
            _client.ClearReceiveBuffer();
            _client.ReceiveTimeout = TimeSpan.FromSeconds(2);

            // Send request
            await _client.SendAsync(requestBuffer, cancellationToken);

            // Receive response
            response = await _client.ReceivePacketAsync(cancellationToken);
            if (response.Length < 4)
            {
                throw new InvalidDataException("Invalid response, response too short");
            }

            var responseLength = BinaryPrimitives.ReadInt16BigEndian(response); // Length
            if (responseLength != response.Length - 4)
            {
                throw new InvalidDataException(
                    $"Invalid response, response length mismatch: {responseLength} != {response.Length - 4}");
            }

            var crcResponse = BinaryPrimitives.ReadUInt16BigEndian(response[^2..]);
            var crcActual = Crc16.Calc(response[2..^2]);
            if (crcResponse != crcActual)
            {
                throw new InvalidDataException($"Invalid response, CRC mismatch: {crcResponse} != {crcActual}");
            }

            return (Response)Response.Descriptor.Parser.ParseFrom(response, 2, response.Length - 4);
        }
        catch (OperationCanceledException ce)
        {
            if (ce.CancellationToken != cancellationToken)
            {
                throw new TimeoutException("Timeout when executing function", ce);
            }

            throw;
        }
        catch (Exception ex)
        {
            var jsonText = JsonConvert.SerializeObject(message,
                new JsonSerializerSettings { NullValueHandling = NullValueHandling.Ignore });
            var msg = $"Execute func error: {ex.Message}\nMessage: {jsonText}";
            msg += $"\nTX: {BitConverter.ToString(request.Memory.ToArray()).Replace("-", " ")}";

            if (response != null)
            {
                msg += $"\nRX: {BitConverter.ToString(response).Replace("-", " ")}";
            }

            throw new CommException(msg, ex);
        }
        finally
        {
            _executeLock.Release();
        }
    }

    private async Task WorkingThread(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                if (!_subscribingTasks.Any())
                {
                    await Task.Delay(TimeSpan.FromMilliseconds(500), stoppingToken);
                    continue;
                }

                // 获取到期任务
                var now = DateTime.Now;
                foreach (var task in _subscribingTasks.Values.ToArray())
                {
                    if (task.NextQueryTime <= now)
                    {
                        task.NextQueryTime = now.AddMilliseconds(task.Period);
                        var request = new Request()
                        {
                            CanRxCacheReadRequest = new()
                            {
                                Cnt = task.SubscriptingId
                            }
                        };

                        var response = await ExecuteMessageAsync(request, stoppingToken);
                        var resp = response.CanRxCacheReadResponse;
                        do
                        {
                            if (resp == null)
                                break;

                            // // 地址不对 TODO 这里应该考虑重新订阅, 还是报错
                            // if (resp.CanId != task.SubscriptingId)
                            //     break;

                            // 不是新数据
                            if (resp.ReadFlag != 0)
                                break;

                            // Callback
                            task.ResponseCallback?.Invoke(resp.CanBuff.ToByteArray());
                        } while (false);
                    }
                }
            }
            catch (Exception)
            {
                // TODO 日志记录, 提示错误
            }
            finally
            {
                await Task.Delay(TimeSpan.FromMilliseconds(10), stoppingToken);
            }
        }
    }

    /// <summary>
    /// 订阅数据, 0 表示空闲, 1 表示占用
    /// </summary>
    private readonly int[] _subscribingUsing = new int[50];

    private readonly ConcurrentDictionary<uint, CanTask> _subscribingTasks = new();

    /// <summary>
    /// 订阅数据 (不发送, 仅接收)
    /// </summary>
    /// <param name="type">CAN 类型</param>
    /// <param name="requestPeriod">读取接收缓冲区的请求间隔</param>
    /// <param name="responseAddress">数据响应地址</param>
    /// <param name="responsePayloadExpected"></param>
    /// <param name="responseCallback">数据响应回调</param>
    /// <param name="cancellationToken"></param>
    /// <returns>订阅序号, 如果返回 null 则表示订阅失败</returns>
    public Task<uint?> SubscriptDataAsync(CanType type,
        uint requestPeriod,
        uint responseAddress,
        byte?[] responsePayloadExpected,
        Action<byte[]> responseCallback,
        CancellationToken cancellationToken)
    {
        return SubscriptDataCoreAsync(type, null, [], requestPeriod, responseAddress, responsePayloadExpected,
            responseCallback, cancellationToken);
    }

    /// <summary>
    /// 订阅数据 (发送并接收)
    /// </summary>
    /// <param name="type">CAN 类型</param>
    /// <param name="requestAddress">每次数据请求地址</param>
    /// <param name="requestPayload">请求数据</param>
    /// <param name="requestPeriod">请求间隔</param>
    /// <param name="responseAddress">数据响应地址</param>
    /// <param name="responsePayloadExpected">响应的数据如果都是同一个地址, 那么我应该从响应的净荷中判断订阅的内容</param>
    /// <param name="responseCallback">数据响应回调</param>
    /// <param name="cancellationToken"></param>
    /// <returns>订阅序号, 如果返回 null 则表示订阅失败</returns>
    public Task<uint?> SubscriptDataAsync(
        CanType type,
        uint requestAddress,
        byte[] requestPayload,
        uint requestPeriod,
        uint responseAddress,
        byte?[] responsePayloadExpected,
        Action<byte[]> responseCallback,
        CancellationToken cancellationToken)
    {
        return SubscriptDataCoreAsync(type, requestAddress, requestPayload, requestPeriod, responseAddress,
            responsePayloadExpected, responseCallback, cancellationToken);
    }

    /// <summary>
    /// 订阅数据
    /// </summary>
    /// <param name="type">CAN 类型</param>
    /// <param name="requestAddressOrNull">每次数据请求地址, 如果请求地址为 null , 则不进行请求, 仅订阅接收</param>
    /// <param name="requestPayload">请求数据</param>
    /// <param name="requestPeriod">请求间隔</param>
    /// <param name="responseAddress">数据响应地址</param>
    /// <param name="responsePayloadExpected">响应的数据如果都是同一个地址, 那么我应该从响应的净荷中判断订阅的内容</param>
    /// <param name="responseCallback">数据响应回调</param>
    /// <param name="cancellationToken"></param>
    /// <returns>订阅序号, 如果返回 null 则表示订阅失败</returns>
    private async Task<uint?> SubscriptDataCoreAsync(
        CanType type,
        uint? requestAddressOrNull,
        byte[] requestPayload,
        uint requestPeriod,
        uint responseAddress,
        byte?[] responsePayloadExpected,
        Action<byte[]> responseCallback,
        CancellationToken cancellationToken)
    {
        if (responseAddress == 0) throw new ArgumentOutOfRangeException(nameof(responseAddress));
        if (requestPeriod == 0) throw new ArgumentOutOfRangeException(nameof(requestPeriod));

        uint subscribingId = uint.MaxValue;
        for (uint i = 0; i < _subscribingUsing.Length; i++)
        {
            if (Interlocked.CompareExchange(ref _subscribingUsing[i], 1, 0) == 0)
            {
                subscribingId = i;
                break;
            }
        }

        if (subscribingId == uint.MaxValue)
        {
            return null;
        }

        // 发送接收订阅
        Array.Resize(ref responsePayloadExpected, 8);
        int bitEnabled = 0;
        for (int i = 0; i < responsePayloadExpected.Length; i++)
        {
            if (responsePayloadExpected[i] != null)
            {
                bitEnabled |= (1 << i);
                continue;
            }

            responsePayloadExpected[i] = 0x00;
        }

        var request = new Request()
        {
            CanRxCacheCfgRequest = new CanRxCacheCfgRequest
            {
                CanAddrType = (uint)(type == CanType.Extended ? 0x01 : 0x00),
                CanId = responseAddress,
                ID = subscribingId,
                CanBuff = ByteString.CopyFrom(responsePayloadExpected.Cast<byte>().ToArray()),
                CanContFliter = (uint)bitEnabled,
            },
        };
        var response = await ExecuteMessageAsync(request, cancellationToken);
        var resp = response.CanRxCacheCfgResponse;
        if (resp is not { Success: true })
        {
            Interlocked.Exchange(ref _subscribingUsing[subscribingId], 0);
            return null;
        }

        // 发送请求
        uint? sendingId = null;
        if (requestAddressOrNull is { } requestAddress)
        {
            sendingId = await SendAsync(type, requestAddress, requestPayload, requestPeriod, cancellationToken);
            if (sendingId == null)
            {
                // 取消订阅
                Interlocked.Exchange(ref _subscribingUsing[subscribingId], 0);
                return null;
            }
        }

        _subscribingTasks[subscribingId] = new CanTask
        {
            SubscriptingId = subscribingId,
            ResponseCallback = responseCallback,
            SendingId = sendingId,
            Period = requestPeriod,
        };

        return subscribingId;
    }

    /// <summary>
    /// 取消订阅
    /// </summary>
    /// <param name="subscribedId"></param>
    /// <param name="cancellationToken"></param>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    public async Task UnsubscribeData(uint subscribedId, CancellationToken cancellationToken)
    {
        if (subscribedId >= _subscribingUsing.Length)
        {
            throw new ArgumentOutOfRangeException(nameof(subscribedId));
        }

        if (Interlocked.Exchange(ref _subscribingUsing[subscribedId], 0) == 0)
        {
            return;
        }

        // 读取任务, 如果含有发送任务, 取消发送任务
        var subscribingTask = _subscribingTasks.TryRemove(subscribedId, out var task) ? task : null;
        if (subscribingTask is not { SendingId: not null })
        {
            return;
        }

        await StopSending(subscribingTask.SendingId.Value, cancellationToken);
    }

    #region 发送槽管理

    /// <summary>
    /// 发送槽的占用情况, 0 表示空闲, 1 表示占用
    /// </summary>
    private readonly SendingSlot[] _sendingSlots = new SendingSlot[50];

    /// <summary>
    /// 发送槽并发锁定
    /// </summary>
    private readonly SemaphoreSlim _sendingSlotLock = new(1, 1);

    /// <summary>
    /// 租用发送槽
    /// </summary>
    /// <param name="duration">租用时间长度, 单位毫秒</param>
    /// <returns>返回得到的发送槽, 如果发送槽满, 返回 null</returns>
    private uint? RentSendingSlot(double duration)
    {
        _sendingSlotLock.Wait();
        try
        {
            var now = DateTime.Now;
            for (uint i = 0; i < _sendingSlots.Length; i++)
            {
                if (_sendingSlots[i].InUsed == false || _sendingSlots[i].Expired < now)
                {
                    _sendingSlots[i].Expired = now.AddMilliseconds(duration);
                    _sendingSlots[i].InUsed = true;
                    return i;
                }
            }
        }
        finally
        {
            _sendingSlotLock.Release();
        }

        return null;
    }

    /// <summary>
    /// 释放发送槽
    /// </summary>
    /// <param name="slot"></param>
    /// <exception cref="ArgumentOutOfRangeException"></exception>
    private void FreeSendingSlot(uint slot)
    {
        if (slot >= _sendingSlots.Length)
        {
            throw new ArgumentOutOfRangeException(nameof(slot));
        }

        _sendingSlotLock.Wait();
        try
        {
            _sendingSlots[slot].InUsed = false;
        }
        finally
        {
            _sendingSlotLock.Release();
        }
    }

    #endregion

    #region 发送数据

    /// <summary>
    /// 发送数据
    /// </summary>
    /// <param name="type">CAN 类型</param>
    /// <param name="requestAddress">每次数据请求地址</param>
    /// <param name="requestPayload">请求数据</param>
    /// <param name="requestPeriod">请求间隔 (ms)</param>
    /// <param name="requestTimes">请求次数</param>
    /// <param name="cancellationToken"></param>
    /// <returns>发送序号, 如果返回 null 则表示发送失败</returns>
    public async Task<uint?> SendAsync(CanType type,
        uint requestAddress,
        byte[] requestPayload,
        uint requestPeriod,
        uint requestTimes,
        CancellationToken cancellationToken)
    {
        if (requestPeriod == 0) throw new ArgumentOutOfRangeException(nameof(requestPeriod));
        if (requestAddress == 0) throw new ArgumentOutOfRangeException(nameof(requestAddress));

        var payload = new byte[8];
        Buffer.BlockCopy(requestPayload, 0, payload, 0, Math.Min(requestPayload.Length, 8));

        // 查找空闲的发送序号
        uint? sendingId = RentSendingSlot((double)requestPeriod * (requestTimes == 0 ? uint.MaxValue : requestTimes));

        if (!sendingId.HasValue)
        {
            return null;
        }

        var request = new Request()
        {
            CanTxCacheCfgRequest = new CanTxCacheCfgRequest()
            {
                CanAddrType = (uint)(type == CanType.Standard ? 0x00 : 0x01),
                CanId = requestAddress,
                CanBuff = ByteString.CopyFrom(payload),
                ID = (uint)sendingId,
                TxPeriod = requestPeriod,
                TxTimes = requestTimes,
            }
        };

        var response = await ExecuteMessageAsync(request, cancellationToken);
        var resp = response.CanTxCacheCfgResponse;
        if (resp is not { Success: true })
        {
            FreeSendingSlot(sendingId.Value);
            return null;
        }

        return sendingId;
    }


    /// <summary>
    /// 按照指定周期, 无休止的发送数据
    /// </summary>
    /// <param name="type">CAN 类型</param>
    /// <param name="requestAddress">每次数据请求地址</param>
    /// <param name="requestPayload">请求数据</param>
    /// <param name="requestPeriod">请求间隔 (ms)</param>
    /// <param name="cancellationToken"></param>
    /// <returns>发送序号, 如果返回 null 则表示发送失败</returns>
    public Task<uint?> SendAsync(CanType type,
        uint requestAddress,
        byte[] requestPayload,
        uint requestPeriod,
        CancellationToken cancellationToken)
    {
        return SendAsync(type, requestAddress, requestPayload, requestPeriod, 0, cancellationToken);
    }

    /// <summary>
    /// 发送数据(一次)
    /// </summary>
    /// <param name="type">CAN 类型</param>
    /// <param name="requestAddress">每次数据请求地址</param>
    /// <param name="requestPayload">请求数据</param>
    /// <param name="cancellationToken"></param>
    public async Task<bool> SendAsync(CanType type,
        uint requestAddress,
        byte[] requestPayload,
        CancellationToken cancellationToken)
    {
        var sendingId = await SendAsync(type, requestAddress, requestPayload, 2, 1, cancellationToken);
        return sendingId.HasValue;
    }

    #endregion

    /// <summary>
    /// 通讯锁定
    /// </summary>
    private readonly SemaphoreSlim _executeLock = new(1, 1);

    private ITransport? _transport = null;

    /// <summary>
    /// 停止发送
    /// </summary>
    /// <param name="sendingId">发送序号</param>
    /// <param name="cancellationToken"></param>
    public async Task StopSending(uint sendingId, CancellationToken cancellationToken)
    {
        if (sendingId >= _sendingSlots.Length)
            throw new ArgumentOutOfRangeException(nameof(sendingId));

        var request = new Request()
        {
            CanTxCacheCfgRequest = new CanTxCacheCfgRequest()
            {
                ID = sendingId,
                TxPeriod = 0
            }
        };
        var response = await ExecuteMessageAsync(request, cancellationToken);
        var resp = response.CanTxCacheCfgResponse;
        if (resp is not { Success: true })
        {
            throw new CommException("发送'停止发送'指令失败");
        }

        FreeSendingSlot(sendingId);
    }

    /// <summary>
    /// 清理缓存
    /// </summary>
    /// <param name="cancellationToken"></param>
    public async Task CleanAsync(CancellationToken cancellationToken)
    {
        var request = new Request()
        {
            CanTxCacheCleanRequest = new CanTxCacheCleanRequest
            {
                Status = true
            }
        };
        var response = await ExecuteMessageAsync(request, cancellationToken);
        if (response.CanTxCacheCleanResponse?.Success != true)
        {
            throw new CommException("清理TX缓存失败");
        }

        request = new Request()
        {
            CanRxCacheCleanRequest = new CanRxCacheCleanRequest
            {
                Status = true
            }
        };
        response = await ExecuteMessageAsync(request, cancellationToken);
        if (response.CanRxCacheCleanResponse?.Success != true)
        {
            throw new CommException("清理RX缓存失败");
        }

        for (uint i = 0; i < _sendingSlots.Length; i++)
        {
            FreeSendingSlot(i);
        }

        for (uint i = 0; i < _subscribingUsing.Length; i++)
        {
            Interlocked.Exchange(ref _subscribingUsing[i], 0);
        }

        _subscribingTasks.Clear();
    }

    /// <summary>
    /// 获取 MCU 的 UID
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task<string> GetSerialNumberAsync(CancellationToken cancellationToken)
    {
        var request = new Request()
        {
            ActiveUidGetRequest = new()
            {
                Ask = 1,
            }
        };
        var response = await ExecuteMessageAsync(request, cancellationToken);
        var resp = response.ActiveUidGetResponse;

        // TODO  后期考虑计算下 CRC
        return $"{resp.Uid1:X8}-{resp.Uid2:X8}-{resp.Uid3:X8}-{resp.UidCrc:X8}";
    }

    public async Task<bool> IsActivatedAsync(CancellationToken cancellationToken)
    {
        var request = new Request
        {
            ActiveCodeGetRequest = new()
            {
                Ask = 1
            }
        };

        var response = await ExecuteMessageAsync(request, cancellationToken);
        var r = response.ActiveCodeGetResponse.Code1 != 0 ||
                response.ActiveCodeGetResponse.Code2 != 0 ||
                response.ActiveCodeGetResponse.Code3 != 0 ||
                response.ActiveCodeGetResponse.Code4 != 0;

        return r;
    }

    public async Task ActivateAsync(string productKey, CancellationToken cancellationToken)
    {
        string[] parts = productKey.Split('-');

        if (parts.Length != 4)
        {
            Console.WriteLine("格式错误：应包含4段16进制字符串");
            return;
        }

        uint[] result = new uint[4];
        for (int i = 0; i < 4; i++)
        {
            result[i] = uint.Parse(parts[i], NumberStyles.AllowHexSpecifier);
        }


        // 把激活码字符串转换为 4 个 uint, 他的存储格式为: 1234ABCD-1234ABCD-1234ABCD-1234ABCD
        var textArray = productKey.Split('-');
        if (textArray.Length != 4)
        {
            throw new ArgumentException("激活码格式错误");
        }

        var activateCodeArray = new UInt32[4];
        for (int i = 0; i < 4; i++)
        {
            // 正则表达式检查格式正确否
            if (!Regex.IsMatch(textArray[i], @"^[0-9A-F]{8}$"))
            {
                throw new ArgumentException("激活码格式错误");
            }

            activateCodeArray[i] = uint.Parse(textArray[i], NumberStyles.AllowHexSpecifier);
        }

        var request = new Request()
        {
            ActiveCodeSetRequest = new ActiveCodeSetRequest()
            {
                Code1 = activateCodeArray[0],
                Code2 = activateCodeArray[1],
                Code3 = activateCodeArray[2],
                Code4 = activateCodeArray[3],
            }
        };

        var response = await ExecuteMessageAsync(request, cancellationToken);
    }

    public async ValueTask DisposeAsync()
    {
        await StopAsync(CancellationToken.None);
    }
}