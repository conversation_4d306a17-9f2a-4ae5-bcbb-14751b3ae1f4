# Summary

模块开发指南

模块项目的根目录需要包含一个 `manifest.json` 文件，该文件记录了模块的基本信息，包括入口 DLL、模块 ID、版本号等。

> 注意：
> `"Version"` 节点的信息会在编译时根据项目设置动态更新，因此无需手动更新版本号。

该文件内容如下所示：

```json
{
  "ModuleId": "InfyPower",
  "EntryPoint": "InfyPower.dll"
}
```

### 说明：
1. **ModuleId**：该字段用于标识模块的唯一 ID，通常是模块的名称或标识符，可以与模块的功能相关联。
2. **Version**：该字段记录模块的版本号。在编译过程中，版本号会根据项目的设置动态更新，因此开发者无需手动更新。若需要更改版本号，可以通过以下方式更新：
    - 修改项目文件中的 `<Version>` 节点。
    - 使用属性对话框来更改版本号。

版本号可以在项目的 `.csproj` 文件中设置，例如：

```xml
<PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <Version>1.0.0</Version>
</PropertyGroup>
```

该设置会确保在编译时自动更新 `manifest.json` 文件中的版本信息。

### 编写模块生成后的构建事件：
为了便于调试和打包，模块编写后需要设置生成后事件。该事件会自动生成调试的动态加载和打包内容，并为将来上传至云端做准备。

```bash
$(SolutionDir)utils\PackModule.exe $(OutDir) $(SolutionDir)bin\$(Configuration)\net8.0-windows\Modules
```

### 说明：
- `PackModule.exe` 工具负责将模块打包并准备上传至云端。
- `$(OutDir)` 和 `$(SolutionDir)` 是构建过程中使用的 Visual Studio 变量，分别代表输出目录和解决方案目录。
- `$(Configuration)` 代表当前构建配置（如 `Debug` 或 `Release`）。
