# 插件包管理目录

本目录用于管理和打包各种设备检测插件的配置文件。每个插件包含特定设备型号的配置参数，这些配置与对应的模块（Module）结合使用，形成完整的设备检测解决方案。

## 📁 目录结构

```
src/Modules/PluginPackages/
├── pack-all.bat              # 批量打包所有插件的脚本
├── InfyPower/                # 英飞源电源插件包
│   ├── pack.bat              # 英飞源插件打包脚本
│   ├── dgwin.power.infy.heg123/
│   │   └── manifest.json     # 插件配置文件
│   └── [其他型号目录]/
├── UUGreenPower/             # 优优绿能电源插件包
│   ├── pack.bat
│   └── [型号目录]/
├── TonHePower/               # 通合电源插件包
├── HuaweiPower/              # 华为电源插件包
├── IncreasePower/            # 英创电源插件包
└── UUGreen/                  # 优优绿能插件包
```

## 🔧 如何添加新插件

### 步骤 1: 创建插件品牌目录

1. 在 `src/Modules/PluginPackages/` 下创建新的品牌目录，例如：
   ```
   src/Modules/PluginPackages/NewBrandPower/
   ```

2. 在品牌目录下创建 `pack.bat` 文件，复制现有的打包脚本, 你可以从其他目录复制一个 pack.bat 文件至该目录

### 步骤 2: 创建具体型号插件

1. 在品牌目录下为每个设备型号创建子目录，目录名建议格式：
   ```
   dgwin.power.[brand].[model]-[version]
   ```
   例如：`dgwin.power.newbrand.model123-1.0.0`

2. 在型号目录中创建 `manifest.json` 文件：
   ```json
   {
     "id": "dgwin.power.newbrand.model123",
     "version": "1.0.0",
     "module": {
       "id": "NewBrandPowerModule",
       "version": "1.0.0"
     },
     "moduleParameter": "{\"deviceType\":\"Model123\",\"baudRate\":9600,\"timeout\":5000}"
   }
   ```

### 步骤 3: 配置插件参数

`manifest.json` 文件字段说明：

- **id**: 插件的唯一标识符，建议使用 `dgwin.power.[brand].[model]` 格式
- **version**: 插件版本号，遵循语义化版本规范 (SemVer)
- **module**: 关联的模块信息
  - **id**: 模块名称，必须与 `src/Modules/` 下的模块项目名称一致
  - **version**: 模块版本号
- **moduleParameter**: JSON 字符串格式的模块参数，用于配置设备特定参数

### 步骤 4: 配置模块参数示例

根据不同设备类型，`moduleParameter` 可能包含以下参数：

```json
{
  "deviceType": "设备型号",
  "baudRate": 9600,
  "timeout": 5000,
  "portSettings": {
    "dataBits": 8,
    "parity": "None",
    "stopBits": 1
  },
  "deviceAddress": 1,
  "customSettings": {
    "maxVoltage": 50.0,
    "maxCurrent": 10.0,
    "testDuration": 30
  }
}
```

## 📦 打包插件

### 单个品牌打包

进入品牌目录，执行打包脚本：
```bash
cd src/Modules/PluginPackages/NewBrandPower
pack.bat
```

### 批量打包所有插件

在 `PluginPackages` 根目录执行：
```bash
cd src/Modules/PluginPackages
pack-all.bat
```

打包完成后，每个型号目录下会生成对应的 `.zip` 文件，例如：
```
dgwin.power.newbrand.model123/
├── manifest.json
└── dgwin.power.newbrand.model123.zip
```

## 🌐 发布到云端

### 发布目标

- **API 地址**: `dgc.chkfun.com/swagger`
- **插件上传端点**: `/api/v1/plugins/<pluginCode>/upload`

### 发布流程

1. **确保模块已发布**: 首先确保对应的模块（如 `NewBrandPowerModule`）已经编译并发布到云端

2. **上传插件包**: 使用以下命令上传插件包到云端：
   ```bash
   curl -X POST "https://dgc.chkfun.com/api/v1/plugins/dgwin.power.newbrand.model123/upload" \
        -H "Authorization: Bearer <your-token>" \
        -H "Content-Type: multipart/form-data" \
        -F "zipPackage=@dgwin.power.newbrand.model123.zip"
   ```

3. **验证上传**: 检查云端插件数据库是否更新成功

## 🔍 插件命名规范

### 插件 ID 命名规范

建议使用以下格式：
```
dgwin.power.[brand].[model]
```

示例：
- `dgwin.power.infy.heg75050f3` - 英飞源 HEG75050F3 型号
- `dgwin.power.uugreen.up5000` - 优优绿能 UP5000 型号
- `dgwin.power.tonhe.th2000` - 通合 TH2000 型号

### 目录命名规范

- 品牌目录：使用品牌英文名，首字母大写，如 `InfyPower`、`UUGreenPower`
- 型号目录：使用完整的插件 ID，如 `dgwin.power.infy.heg75050f3`

## ⚠️ 注意事项

1. **模块依赖**: 插件必须依赖已存在的模块，确保 `manifest.json` 中的 `module.id` 与实际模块在云端的名称一致

2. **版本兼容性**: 插件版本应与对应模块版本兼容

3. **参数格式**: `moduleParameter` 必须是有效的 JSON 字符串格式

4. **唯一性**: 插件 ID 必须全局唯一，避免冲突

5. **测试验证**: 发布前在本地环境测试插件功能是否正常

## 🛠️ 故障排除

### 常见问题

1. **打包失败**
   - 检查 PowerShell 执行策略
   - 确认目录结构正确
   - 验证 `manifest.json` 格式

2. **上传失败**
   - 检查网络连接
   - 验证 API Token 有效性
   - 确认插件包格式正确

3. **插件加载失败**
   - 检查模块是否存在
   - 验证版本兼容性
   - 检查参数格式是否正确

### 调试方法

1. 使用 `pack-all.bat` 批量打包测试
2. 检查生成的 `.zip` 文件内容
3. 在本地环境测试插件加载
4. 查看系统日志获取详细错误信息
