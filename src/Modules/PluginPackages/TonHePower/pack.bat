@echo off
setlocal enabledelayedexpansion

echo Starting auto-packing directories...
echo.

REM Traverse all subdirectories in current directory
for /d %%i in (*) do (
    echo Processing directory: %%i
    
    REM Enter directory
    cd "%%i"
    
    REM Check if zip file already exists
    if exist "%%i.zip" (
        echo   Skip: %%i.zip already exists
    ) else (
        REM Check if directory contains content
        if exist "*" (
            echo   Packing directory content to: %%i.zip
            
            REM Use PowerShell to create zip, pack content directly to avoid nested structure
            powershell -Command "Compress-Archive -Path '*' -DestinationPath '%%i.zip' -Force"
            
            if !errorlevel! equ 0 (
                echo   Success: %%i.zip created
            ) else (
                echo   Failed: %%i packing failed
            )
        ) else (
            echo   Warning: Directory is empty, skipping
        )
    )
    
    REM Return to parent directory
    cd ..
    echo.
)

echo All directories processed!
pause
