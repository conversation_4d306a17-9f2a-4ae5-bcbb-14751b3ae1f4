@echo off
setlocal enabledelayedexpansion

echo Starting pack-all script...
echo Searching for pack.bat files in subdirectories...
echo.

REM Traverse all subdirectories
for /d %%i in (*) do (
    echo Checking directory: %%i
    
    REM Check if pack.bat exists in this directory
    if exist "%%i\pack.bat" (
        echo   Found pack.bat in %%i, executing...
        
        REM Enter directory and execute pack.bat
        cd "%%i"
        call pack.bat
        
        REM Return to root directory
        cd ..
        echo   Completed: %%i
        echo.
    ) else (
        echo   No pack.bat found in %%i, skipping
    )
)

echo All pack.bat files executed!
pause