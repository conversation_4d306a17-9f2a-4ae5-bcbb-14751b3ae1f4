# Summary

本项目提供客户端的模块管理的代码, 用于

- 从云端下载插件, 并安装到本地.
- 提供插件管理功能, 包括本地插件的安装、卸载、更新、列表等操作。

# Architecture

概念如下:

- Plugin: 一个插件是一个程序代码包(Module)以及相关的运行参数的组合, 用户接触到的具体的一个型号的检测程序称为插件
- Module: 一个包是一个程序代码, 他不能是一个完整的插件, 他只是插件的可执行部分, 要运行一个Module, 还需要这个包的执行的参数

```mermaid
classDiagram
    class Plugin {
    }
    class Module {
    }
    Plugin "n" -- "1" Module
```

在定义插件的时候, 需要指定软件包, 以及运行这个软件包的参数

例如:
英飞源的电源含有A型号和B型号, 他们的功能都是一样的, 只是参数有一些区别

我们可以设计一个 Module, 叫做 InfyPowerModule, 他负责实现所有的英飞源的电源模块的程序,
然后定义两个插件, 插件 InfyPowerA, 插件 InfyPowerB, 分别对应A型号和B型号的英飞源电源模块, 
这两个插件引用了包 InfyPowerModule, 以及在启动这个包时候提供的参数有所区别.
