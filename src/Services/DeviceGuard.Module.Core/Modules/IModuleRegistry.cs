namespace DeviceGuard.ModuleCore.Modules;

public interface IModuleRegistry
{
    /// <summary>
    /// 获取所有在本机开发需要调试的模块
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<ModuleMetaInfo[]> GetDevModulesAsync(CancellationToken cancellationToken);

    /// <summary>
    /// 获取所有已安装的模块信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>已安装模块的列表</returns>
    Task<ModuleMetaInfo[]> GetInstalledModulesAsync(CancellationToken cancellationToken);

    /// <summary>
    /// 通过模块 ID 获取模块信息
    /// </summary>
    /// <param name="moduleKey"></param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>模块信息，如果不存在则返回 null</returns>
    Task<ModuleMetaInfo?> GetModuleAsync(ModuleKey moduleKey, CancellationToken cancellationToken);

    /// <summary>
    /// 安装或更新模块
    /// </summary>
    /// <param name="modulePath">模块文件路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否需要重启</returns>
    Task<bool> InstallModuleAsync(ModuleKey modulePath, CancellationToken cancellationToken);

    /// <summary>
    /// 卸载模块
    /// </summary>
    /// <param name="moduleKey"></param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否成功</returns>
    Task<bool> UninstallModuleAsync(ModuleKey moduleKey, CancellationToken cancellationToken);

    /// <summary>
    /// 加载模块至内存
    /// </summary>
    /// <param name="moduleKey"></param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>返回模块对象</returns>
    Task<IModuleContext> LoadModuleAsync(ModuleKey moduleKey, CancellationToken cancellationToken);
}
