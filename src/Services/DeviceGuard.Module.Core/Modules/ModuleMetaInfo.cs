namespace DeviceGuard.ModuleCore.Modules;

/// <summary>
/// 模块的元信息
/// </summary>
public class ModuleMetaInfo
{
    /// <summary>
    /// 模块的 Key
    /// </summary>
    public required ModuleKey Key { get; init; }

    /// <summary>
    /// 模块名
    /// </summary>
    public required string Name { get; init; }

    /// <summary>
    /// 模块的简介
    /// </summary>
    public string? Brief { get; set; }

    /// <summary>
    /// 模块的详细介绍网址
    /// </summary>
    public string? Url { get; set; }

    /// <summary>
    /// 模块的图标
    /// </summary>
    public string? Icon { get; set; }

    /// <summary>
    /// 模块文件以及全路径
    /// </summary>
    public required string EntryPoint { get; init; }

    /// <summary>
    /// 模块所在文件夹
    /// </summary>
    public required string Folder { get; init; }

    /// <summary>
    /// 是否是调试中的模块, 这些模块只有在开发计算机上才会出现
    /// </summary>
    public bool IsDevModule { get; set; }

    /// <summary>
    /// 获取模块主文件的全路径
    /// </summary>
    /// <returns></returns>
    public string GetFullPath()
    {
        return Path.Combine(Folder, EntryPoint);
    }
}
