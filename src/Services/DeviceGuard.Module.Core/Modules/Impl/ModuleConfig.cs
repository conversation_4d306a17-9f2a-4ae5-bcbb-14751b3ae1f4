using Oulida.Configuration;

namespace DeviceGuard.ModuleCore.Modules.Impl;

public class ModuleConfig : ConfigBase
{
    /// <summary>
    /// 构造函数，
    /// </summary>
    /// <param name="config">强命名该分组</param>
    public ModuleConfig(Config config) : base(config)
    {
    }

    #region Overrides of ConfigBase

    /// <summary>
    /// 定义作用域(包)
    /// 在多个配置文件合并的时候，用来区分不同的作用域
    /// </summary>
    public override string PathName => "Modules";

    #endregion
}