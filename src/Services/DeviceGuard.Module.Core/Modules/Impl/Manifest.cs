namespace DeviceGuard.ModuleCore.Modules.Impl;

public class ModuleManifest
{
    /// <summary>
    /// 模块 Id
    /// </summary>
    public required string ModuleName { get; init; }

    /// <summary>
    /// 模块简介
    /// </summary>
    public string? Brief { get; init; }

    /// <summary>
    /// 模块的官网地址
    /// </summary>
    public string? Url { get; set; }

    /// <summary>
    /// 模块图标
    /// </summary>
    public string? Icon { get; set; }

    /// <summary>
    /// 入口文件
    /// </summary>
    public required string EntryPoint { get; init; }
}
