using System.IO.Compression;
using DeviceGuard.Interface.Cloud;
using DeviceGuard.Interface.FileSystem;

namespace DeviceGuard.ModuleCore.Modules.Impl;

public class ModulePackageManager : IModulePackageManager
{
    private readonly IAppFolderService _appFolderService;
    private readonly IModuleClient     _moduleClient;

    public ModulePackageManager(IAppFolderService appFolderService, IModuleClient moduleClient)
    {
        _appFolderService = appFolderService;
        _moduleClient     = moduleClient;
    }

    /// <summary>
    /// 通过 ModuleKey 获取模块的安装包
    /// 目前从本地的 .publish 目录获取, 后期从云端获取
    /// </summary>
    /// <param name="moduleKey"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task<string> DownloadModulePackageAsync(ModuleKey moduleKey, CancellationToken cancellationToken)
    {
        var packagePath = Path.Combine(_appFolderService.GetOrCreateFolder(AppFolder.ModulePackages), GetPackageFileName(moduleKey));
        if (File.Exists(packagePath) && new FileInfo(packagePath).Length > 0)
        {
            return packagePath;
        }

        // 从云端下载后存储在本地的目录
        var content = await _moduleClient.DownloadAsync(moduleKey.Id, moduleKey.Version, cancellationToken);
        if (content.Length == 0)
        {
            throw new ModuleNotFoundException($"模块 {moduleKey.Id} {moduleKey.Version} 不存在");
        }

        // 保存到缓存目录
        await File.WriteAllBytesAsync(packagePath, content, cancellationToken);
        return packagePath;
    }


    private string GetPackageFileName(ModuleKey moduleKey)
    {
        return $"{moduleKey.Id}-{moduleKey.Version}-win64.zip";
    }
}
