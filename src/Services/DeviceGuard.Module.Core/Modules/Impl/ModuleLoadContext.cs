using System.Reflection;
using System.Runtime.InteropServices;
using System.Runtime.Loader;
using DeviceGuard.Modules.Interface;
using Microsoft.Extensions.DependencyModel;

namespace DeviceGuard.ModuleCore.Modules.Impl;

public class ModuleLoadContext : AssemblyLoadContext, IModuleContext
{
    private readonly Assembly          _mainAssembly;
    private readonly DependencyContext _deps;

    /// <summary>
    /// 创建一个库加载上下文
    /// </summary>
    /// <remarks>
    /// 注意: 设计为可回收, 但事实上, 这个库必须到重启动程序, 才能被卸载
    /// </remarks>
    public ModuleLoadContext(ModuleMetaInfo info) : base(isCollectible: true)
    {
        Module        = info ?? throw new ArgumentNullException(nameof(info));
        _mainAssembly = LoadFromAssemblyPath(Module.GetFullPath()) ?? throw new FileNotFoundException($"{Module.GetFullPath()} not found.");
        _deps         = DependencyContext.Load(_mainAssembly) ?? throw new InvalidOperationException($"{Module.GetFullPath()} is not a valid assembly.");
    }

    protected override Assembly? Load(AssemblyName assemblyName)
    {
        // 如果 assemblyName 在EXE的主上下文已存在, 则忽略加载, 直接使用目前上下文中的 DLL
        var loadedAssembly = Default.Assemblies.FirstOrDefault(a => a.GetName().Name == assemblyName.Name);
        if (loadedAssembly != null)
        {
            return loadedAssembly;
        }

        // 查找当前依赖项上下文中的库
        var library = _deps.RuntimeLibraries.FirstOrDefault(lib => lib.Name == assemblyName.Name);
        if (library != null)
        {
            // 获取当前运行时环境
            string currentRuntime = GetCurrentRuntime();

            // 查找与当前运行时环境匹配的程序集路径
            var runtimeAssemblies = library.RuntimeAssemblyGroups
                                           .Where(g => string.IsNullOrEmpty(g.Runtime) || g.Runtime == currentRuntime)
                                           .SelectMany(g => g.AssetPaths)
                                           .Select(path => Path.Combine(Module.Folder, path));
            var runtimeAssembly = runtimeAssemblies.FirstOrDefault(File.Exists);

            if (runtimeAssembly != null)
            {
                return LoadFromAssemblyPath(runtimeAssembly);
            }
        }

        string assemblyPath = Path.Combine(Module.Folder, assemblyName.Name + ".dll");
        assemblyPath = Path.GetFullPath(assemblyPath);
        if (File.Exists(assemblyPath))
        {
            return LoadFromAssemblyPath(assemblyPath);
        }

        return null;
    }

    private static string GetCurrentRuntime()
    {
        if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux) || RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
        {
            return "unix"; // Linux 或 macOS
        }
        else if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
        {
            return "win"; // Windows
        }
        else
        {
            throw new PlatformNotSupportedException("不支持的操作系统。");
        }
    }

    public void RegisterModule(IContainerExtension containerExtension)
    {
        if (containerExtension == null) throw new ArgumentNullException(nameof(containerExtension));

        // 试验结果, 无法实现, .NET 不支持
        // var assembly = this.LoadMyModule();
        // var diType   = assembly.GetType("MyModule.DI");
        // var method   = diType.GetMethod("Register", BindingFlags.Public | BindingFlags.Instance);
        // method.Invoke(diType, [containerRegistry]);

        var moduleTypes = (from t in _mainAssembly.GetExportedTypes()
                           where typeof(IModule).IsAssignableFrom(t)
                           select t).ToArray();

        if (!moduleTypes.Any())
        {
            throw new InvalidOperationException($"Cannot find any class implementing 'IModule' in '{Module.Name}'.");
        }

        // 手工加载模块
        foreach (var type in moduleTypes)
        {
            if (containerExtension.Resolve(type) is not IModule module)
            {
                throw new InvalidProgramException($"{type.FullName} is not a module.");
            }

            module.RegisterTypes(containerExtension);
            module.OnInitialized(containerExtension);
        }
    }

    public ModuleMetaInfo Module { get; }

    public Type GetBootstrapType()
    {
        var types = _mainAssembly.GetExportedTypes();
        var moduleLoader = (from t in types
                            where typeof(IModuleLoader).IsAssignableFrom(t)
                            select t).SingleOrDefault() ?? throw new InvalidOperationException($"No module loader found in '{Module.GetFullPath()}'");

        return moduleLoader;

        // var inst = Activator.CreateInstance(moduleLoader) as IModuleBootstrap ?? throw new InvalidOperationException($"{moduleLoader.FullName} is not a module loader.");
        // return inst;
    }
}
