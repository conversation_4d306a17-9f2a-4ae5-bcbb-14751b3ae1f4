using System.Collections.Concurrent;
using System.IO.Compression;
using System.Reflection;
using System.Runtime.Loader;
using DeviceGuard.Interface.FileSystem;
using DeviceGuard.ModuleCore.Utils;
using Newtonsoft.Json;
using Semver;

namespace DeviceGuard.ModuleCore.Modules.Impl;

/// <summary>
/// 模块管理器, 同时保存着所有加载了的模块, 单件运行
/// </summary>
public class ModuleRegistry : IModuleRegistry
{
    private readonly IContainerExtension                         _containerExtension;
    private readonly IModulePackageManager                       _modulePackageManager;
    private readonly IAppFolderService                           _appFolderService;
    private readonly ConcurrentDictionary<ModuleKey, ModuleData> _modules; // key is: libraryId-version

    public ModuleRegistry(
        IContainerExtension   containerExtension,
        IModulePackageManager modulePackageManager,
        IAppFolderService     appFolderService)
    {
        _containerExtension   = containerExtension;
        _modulePackageManager = modulePackageManager;
        _appFolderService     = appFolderService;

        // 扫描本地已经安装的模块
        var modules    = ScanModules(GetModuleBasePath(),        true,  false).ToList();
        var devModules = ScanModules(AppFolder.DebuggingModules, false, true).ToList();
        devModules.ForEach(x =>
        {
            x.IsDevModule = true;
        });
        modules.AddRange(devModules);

        _modules = new ConcurrentDictionary<ModuleKey, ModuleData>(
            modules.ToDictionary(
                c => c.Key,
                c => new ModuleData(c)
            )
        );
    }

    #region 扫描包

    /// <summary>
    /// 获取模块安装的根目录
    /// </summary>
    /// <returns></returns>
    /// <exception cref="InvalidOperationException"></exception>
    private string GetModuleBasePath()
    {
        return _appFolderService.GetOrCreateFolder(AppFolder.Modules);
    }

    /// <summary>
    /// 扫描本地已经安装的模块
    /// </summary>
    /// <returns></returns>
    private ModuleMetaInfo[] ScanModules(string basePath, bool removeUnknownModule, bool isDevModule)
    {
        var modules = new List<ModuleMetaInfo>();

        // 准备根目录
        if (!Directory.Exists(basePath))
        {
            Directory.CreateDirectory(basePath);
        }

        // 获取所有模块目录的第一层子目录（libraryId）
        var moduleIds = Directory.GetDirectories(basePath);

        foreach (var moduleIdPath in moduleIds)
        {
            var moduleId = Path.GetFileName(moduleIdPath);

            // 获取每个 libraryId 下的版本目录（libraryVersion）
            var versionPaths = Directory.GetDirectories(moduleIdPath);

            foreach (var versionPath in versionPaths)
            {
                // 读取 manfest.json 文件, 得到包的基础信息
                var manifestPath = Path.Combine(versionPath, "manifest.json");
                if (!File.Exists(manifestPath))
                {
                    if (removeUnknownModule)
                        Directory.Delete(versionPath, true);
                    // TODO 日志警告
                    continue;
                }

                var manifestContent = File.ReadAllText(manifestPath);
                var manifest        = JsonConvert.DeserializeObject<ModuleManifest>(manifestContent);
                if (manifest == null)
                {
                    if (removeUnknownModule)
                        Directory.Delete(versionPath, true);
                    // TODO 日志警告
                    continue;
                }

                // 使用 ModuleName 作为入口 DLL 名称
                var entryDll = manifest.EntryPoint;
                if (!File.Exists(Path.Combine(versionPath, entryDll)))
                {
                    if (removeUnknownModule)
                        Directory.Delete(versionPath, true);
                    // TODO 日志警告
                    continue;
                }

                // 读取 entryDll 的库, 读取版本号
                var entryDllPath    = Path.Combine(versionPath, entryDll);
                var entryDllVersion = LoadModuleVersion(entryDllPath) ?? "0.0.0";

                if (isDevModule)
                {
                    entryDllVersion += "-dev";
                }

                // 创建 LibraryMetaInfo 对象
                var moduleMetaInfo = new ModuleMetaInfo
                {
                    Key         = new ModuleKey(moduleId, entryDllVersion),
                    Name        = moduleId,
                    Brief       = manifest.Brief,
                    Url         = manifest.Url,
                    Icon        = manifest.Icon is { Length: > 0 } ico ? Path.Combine(versionPath, ico) : null,
                    EntryPoint  = entryDll,
                    Folder      = versionPath,
                    IsDevModule = isDevModule,
                };

                modules.Add(moduleMetaInfo);
            }

            if (!Directory.EnumerateFileSystemEntries(moduleIdPath, "*", SearchOption.AllDirectories).Any())
            {
                Directory.Delete(moduleIdPath, true);
            }
        }

        return modules.ToArray();
    }

    #endregion

    #region 查找调试包

    /// <summary>
    /// 获取所有在本机开发需要调试的模块
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public Task<ModuleMetaInfo[]> GetDevModulesAsync(CancellationToken cancellationToken)
    {
        var q = from m in _modules.Values
                where m.Info.IsDevModule
                select m.Info;

        return Task.FromResult(q.ToArray());
    }

    #endregion

    #region 查询包

    /// <summary>
    /// 获取所有已安装的模块信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>已安装模块的列表</returns>
    public Task<ModuleMetaInfo[]> GetInstalledModulesAsync(CancellationToken cancellationToken)
    {
        return Task.FromResult(_modules.Values.Select(x => x.Info).ToArray());
    }

    /// <summary>
    /// 通过模块 ID 获取模块信息
    /// </summary>
    /// <param name="moduleKey"></param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>模块信息，如果不存在则返回 null</returns>
    public Task<ModuleMetaInfo?> GetModuleAsync(ModuleKey moduleKey, CancellationToken cancellationToken)
    {
        var r = _modules.GetValueOrDefault(moduleKey);
        return Task.FromResult(r?.Info);
    }

    #endregion

    #region 安装 / 卸载包

    /// <summary>
    /// 安装或更新模块
    /// 从指定的文件解压缩包, 并安装到目标目录
    /// </summary>
    /// <param name="moduleKey">模块文件路径</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否需要重启动 APP 生效</returns>
    public async Task<bool> InstallModuleAsync(ModuleKey moduleKey, CancellationToken cancellationToken)
    {
        if (moduleKey == null) throw new ArgumentNullException(nameof(moduleKey));

        if (_modules.ContainsKey(moduleKey))
        {
            return false;
        }

        // 获取模块的安装包的路径
        var modulePath = await _modulePackageManager.DownloadModulePackageAsync(moduleKey, cancellationToken);

        // 1. 检查文件是否存在
        if (!File.Exists(modulePath))
        {
            throw new FileNotFoundException("Library file not found.", modulePath);
        }

        // 2. 解压缩到一个临时目录
        var tempDir = Path.Combine(Path.GetTempPath(), Path.GetRandomFileName());
        Directory.CreateDirectory(tempDir);
        try
        {
            // 解压缩
            ZipFile.ExtractToDirectory(modulePath, tempDir);

            // 3. 读取 manifest.json 文件, 得到包的基础信息
            var manifestPath = Path.Combine(tempDir, "manifest.json");
            if (!File.Exists(manifestPath))
            {
                throw new FileNotFoundException("Manifest file not found in the library.", manifestPath);
            }

            var manifestContent = await File.ReadAllTextAsync(manifestPath, cancellationToken);
            var manifest        = JsonConvert.DeserializeObject<ModuleManifest>(manifestContent);
            if (manifest == null || string.IsNullOrEmpty(manifest.ModuleName))
            {
                throw new InvalidDataException("Invalid manifest file.");
            }

            var entryPointFilePath = Path.Combine(tempDir, manifest.EntryPoint);
            if (!File.Exists(entryPointFilePath))
            {
                throw new FileNotFoundException($"模块 '{modulePath}' 中没有找到入口文件 '{manifest.EntryPoint}'.");
            }

            // 临时加载 manifest.EntryPoint 读取版本号
            var moduleVersion = LoadModuleVersion(entryPointFilePath) ?? throw new InvalidDataException("Invalid manifest file, require 'ModuleVersion'.");

            // 4. 将模块解压缩到目标目录, 目录结构为 Modules/<ModuleId>/<ModuleVersion>/
            var basePath   = GetModuleBasePath();
            var libraryDir = Path.Combine(basePath, manifest.ModuleName, moduleVersion);

            if (Directory.Exists(libraryDir))
            {
                // 如果目标目录已经存在, 则删除它
                Directory.Delete(libraryDir, true);
            }

            Directory.CreateDirectory(libraryDir);
            foreach (var file in Directory.GetFiles(tempDir))
            {
                var fileName = Path.GetFileName(file);
                var destFile = Path.Combine(libraryDir, fileName);
                File.Copy(file, destFile, true);
            }

            // 5. 检查该包是否已经加载, 如果已经加载, 则提示用户重启后生效
            var libraryKey = new ModuleKey(manifest.ModuleName, moduleVersion);
            var p = _modules.GetOrAdd(libraryKey,
                new ModuleData(new ModuleMetaInfo
                {
                    Key        = libraryKey,
                    Name       = manifest.ModuleName,
                    EntryPoint = $"{manifest.EntryPoint}",
                    Folder     = libraryDir,
                }));

            return p.Module != null;
        }
        finally
        {
            Directory.Delete(tempDir, true);
        }
    }

    private string? LoadModuleVersion(string entryPoint)
    {
        var context = new AssemblyLoadContext(entryPoint, true);
        try
        {
            // var assembly = context.LoadFromAssemblyPath(entryPoint);
            using var stream = File.OpenRead(entryPoint);

            var assembly = context.LoadFromStream(stream);
            var version  = assembly.GetCustomAttributes<AssemblyInformationalVersionAttribute>().SingleOrDefault()?.InformationalVersion;

            // 测试版本号格式正确性, 然后去除编译部分
            if (!SemVersion.TryParse(version, out var semver))
                return null;

            return semver.ToStringWithoutMetadata();
        }
        catch
        {
            return null;
        }
        finally
        {
            context.Unload();
        }
    }

    /// <summary>
    /// 卸载模块
    /// </summary>
    /// <param name="moduleKey"></param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否需要重启软件</returns>
    public Task<bool> UninstallModuleAsync(ModuleKey moduleKey, CancellationToken cancellationToken)
    {
        try
        {
            var basePath   = GetModuleBasePath();
            var moduleDir  = Path.Combine(basePath,  moduleKey.Id);
            var libraryDir = Path.Combine(moduleDir, moduleKey.Version);

            if (!Directory.Exists(libraryDir))
            {
                return Task.FromResult(false); // 包不存在
            }

            // 删除该包的目录及其所有文件
            Directory.Delete(libraryDir, true);

            // 如果所有的子目录都没了, 删除父一级目录
            if (!Directory.EnumerateFileSystemEntries(moduleDir, "*", SearchOption.TopDirectoryOnly).Any())
            {
                Directory.Delete(moduleDir, true);
            }

            // 更新已安装的包列表
            _modules.TryRemove(moduleKey, out _);
            return Task.FromResult(false);
        }
        catch (Exception)
        {
            // TODO LOG IT
        }

        return Task.FromResult(true);
    }

    #endregion

    #region 加载

    /// <summary>
    /// 加载模块至内存
    /// </summary>
    /// <param name="moduleKey"></param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>返回模块对象</returns>
    public Task<IModuleContext> LoadModuleAsync(ModuleKey moduleKey, CancellationToken cancellationToken)
    {
        var moduleData = _modules.GetValueOrDefault(moduleKey) ?? throw new ArgumentException($"Library {moduleKey} not found.", nameof(moduleKey));
        if (moduleData.Module != null)
        {
            return Task.FromResult(moduleData.Module);
        }

        lock (moduleData)
        {
            if (moduleData.Module == null)
            {
                var context = new ModuleLoadContext(moduleData.Info);
                context.RegisterModule(_containerExtension);
                moduleData.Module = context;
            }
        }

        return Task.FromResult(moduleData.Module);
    }

    #endregion
}

// 添加 Manifest 类用于解析 manifest.json
