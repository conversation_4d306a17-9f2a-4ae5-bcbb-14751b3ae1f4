namespace DeviceGuard.ModuleCore.Modules;

/// <summary>
/// 模块的包管理接口, 负责管理包文件查找, 下载等操作
/// </summary>
public interface IModulePackageManager
{
    /// <summary>
    /// 通过 ModuleKey 获取模块的安装包, 如果本地不存在, 下载包
    /// </summary>
    /// <param name="moduleKey"></param>
    /// <param name="cancellationToken"></param>
    /// <returns>返回包的本地保存的路径</returns>
    Task<string> DownloadModulePackageAsync(ModuleKey moduleKey, CancellationToken cancellationToken);
}
