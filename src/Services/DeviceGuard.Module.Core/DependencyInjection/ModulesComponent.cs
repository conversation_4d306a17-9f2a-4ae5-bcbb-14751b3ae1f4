using DeviceGuard.Interface.Prism;
using DeviceGuard.ModuleCore.Modules;
using DeviceGuard.ModuleCore.Modules.Impl;
using DeviceGuard.ModuleCore.Plugins;
using DeviceGuard.ModuleCore.Plugins.Impl;

namespace DeviceGuard.ModuleCore.DependencyInjection;

/// <summary>
/// 基础设施部件
/// </summary>
public class ModulesComponent : IPrismComponent
{
    public void RegisterTypes(IContainerRegistry containerRegistry)
    {
        containerRegistry.RegisterSingleton<IPluginManager, PluginManager>();
        containerRegistry.RegisterSingleton<IPluginPackageManager, PluginPackageManager>();

        containerRegistry.RegisterSingleton<IModuleRegistry, ModuleRegistry>();
        containerRegistry.RegisterSingleton<IModulePackageManager, ModulePackageManager>();

        containerRegistry.Register<IPluginOwnedManager, PluginOwnedManager>();
    }
}
