<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net6.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <LangVersion>13</LangVersion>
        <Version>1.0.0</Version>
        <AssemblyVersion>1.0.0</AssemblyVersion>
        <FileVersion>1.0.0</FileVersion>
        <RootNamespace>DeviceGuard.ModuleCore</RootNamespace>
        <Platforms>AnyCPU;x64</Platforms>
        <AssemblyName>DeviceGuard.ModuleCore</AssemblyName>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\Interfaces\DeviceGuard.Interface\DeviceGuard.Interface.csproj" />
        <ProjectReference Include="..\..\Interfaces\DeviceGuard.Modules.Interface\DeviceGuard.Modules.Interface.csproj" />
        <ProjectReference Include="..\Configuration\Configuration.csproj" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="RequiredMemberAttribute" Version="1.0.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="PropertyChanged.Fody" Version="4.1.0">
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="LinqAsync" Version="1.1.0" />
        <PackageReference Include="Microsoft.Extensions.DependencyModel" Version="6.0.2" />
        <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
        <PackageReference Include="PinyinNet" Version="1.0.0" />
        <PackageReference Include="Semver" Version="3.0.0" />
    </ItemGroup>
    <ItemGroup>
        <None Update="Mocks\Packages\InfyPower\1.0.0\ChargerController.dll">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Mocks\Packages\InfyPower\1.0.0\ChargerController.pdb">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Mocks\Packages\InfyPower\1.0.0\ChargerControllerModule.deps.json">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Mocks\Packages\InfyPower\1.0.0\Configuration.dll">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Mocks\Packages\InfyPower\1.0.0\Configuration.pdb">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Mocks\Packages\InfyPower\1.0.0\DeviceGuard.Framework.dll">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Mocks\Packages\InfyPower\1.0.0\DeviceGuard.Framework.pdb">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Mocks\Packages\InfyPower\1.0.0\DeviceGuard.Windows.Controls.dll">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Mocks\Packages\InfyPower\1.0.0\DeviceGuard.Windows.Controls.pdb">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Mocks\Packages\InfyPower\1.0.0\DeviceGuard.Windows.Framework.dll">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Mocks\Packages\InfyPower\1.0.0\DeviceGuard.Windows.Framework.pdb">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Mocks\Packages\InfyPower\1.0.0\Hotwheels.Wpf.dll">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Mocks\Packages\InfyPower\1.0.0\Hotwheels.Wpf.pdb">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Mocks\Packages\InfyPower\1.0.0\InfyPowerModule.dll">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Mocks\Packages\InfyPower\1.0.0\InfyPowerModule.pdb">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Mocks\Packages\InfyPower\1.0.0\InfyPowerModule.deps.json">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="Mocks\infy-net8.0-windows.zip">
            <CopyToOutputDirectory>Never</CopyToOutputDirectory>
        </None>
    </ItemGroup>
    <ItemGroup>
      <Resource Include="Resources\Debug.ico" />
    </ItemGroup>

</Project>
