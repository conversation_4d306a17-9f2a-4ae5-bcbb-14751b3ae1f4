using Semver;

namespace DeviceGuard.ModuleCore.Plugins;

/// <summary>
/// 表示插件的特定版本信息
/// </summary>
public class PluginVersion
{
    public static PluginVersion Empty { get; } = new()
    {
        Version = "0.0.0",
    };

    /// <summary>版本号</summary>
    public required string Version { get; init; }

    public SemVersion SemVersion => Semver.SemVersion.Parse(Version);

    /// <summary>版本类型（稳定版/测试版等）</summary>
    public bool IsRelease => SemVersion.IsRelease;

    /// <summary>发布日期</summary>
    public DateTime? Date { get; set; }


    public int CompareWith(PluginInstalled installed)
    {
        return SemVersion.ComparePrecedenceTo(installed.SemVersion);
    }
}
