using Semver;

namespace DeviceGuard.ModuleCore.Plugins;

/// <summary>
/// Interface that defines methods for managing plugin packages.
/// </summary>
public interface IPluginPackageManager
{
    /// <summary>
    /// Updates the plugin database from the cloud.
    /// </summary>
    /// <param name="cancellationToken">A token to monitor for cancellation requests.</param>
    /// <returns>A task that represents the asynchronous operation.</returns>
    Task UpdatePluginDbFromCloudAsync(CancellationToken cancellationToken);

    /// <summary>
    /// Installs a plugin.
    /// </summary>
    /// <param name="pluginId">The unique identifier of the plugin to install.</param>
    /// <param name="version">The version of the plugin to install.</param>
    /// <param name="cancellationToken">A token to monitor for cancellation requests.</param>
    /// <returns>Returns true if a restart is required to apply the changes; otherwise, false.</returns>
    Task<bool> InstallAsync(string pluginId, SemVersion version, CancellationToken cancellationToken);

    /// <summary>
    /// 卸载插件
    /// </summary>
    /// <param name="pluginId">插件的唯一标识符</param>
    /// <param name="cancellationToken">操作取消标记</param>
    /// <returns>一个异步任务</returns>
    Task UninstallAsync(string pluginId, CancellationToken cancellationToken);
}
