using Newtonsoft.Json;

namespace DeviceGuard.ModuleCore.Plugins;

/// <summary>
/// 分类节点类型，可包含子节点
/// </summary>
public class CategoryNode : TreeNode
{
    public static CategoryNode Empty { get; } = new CategoryNode
    {
        Id = "Null",
    };

    /// <summary>
    /// 子节点集合（可包含分类或插件）
    /// </summary>
    public List<TreeNode> Children { get; set; } = [];

    public override NodeType Type => NodeType.Category;
}
