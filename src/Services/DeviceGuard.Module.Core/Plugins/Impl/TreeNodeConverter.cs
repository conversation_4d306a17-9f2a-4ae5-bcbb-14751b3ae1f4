using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace DeviceGuard.ModuleCore.Plugins.Impl;

/// <summary>
/// JSON转换器，处理TreeNode的多态反序列化
/// </summary>
public class TreeNodeConverter : JsonConverter
{
    /// <summary>
    /// 判断是否支持转换指定类型
    /// </summary>
    public override bool CanConvert(Type objectType)
    {
        return objectType == typeof(TreeNode);
    }

    /// <summary>
    /// 根据Type字段值反序列化为具体子类
    /// </summary>
    /// <returns>Category或Plugin实例，无法识别时返回null</returns>
    public override object? ReadJson(JsonReader reader, Type objectType, object? existingValue, JsonSerializer serializer)
    {
        var obj  = JObject.Load(reader);
        var type = obj["Type"]?.ToString();

        return type switch
        {
            "Category" => obj.ToObject<CategoryNode>(serializer),
            "Plugin"   => obj.ToObject<PluginNode>(serializer),
            _          => null
        };
    }

    /// <summary>
    /// 执行标准序列化（未特殊处理）
    /// </summary>
    public override void WriteJson(JsonWriter writer, object? value, JsonSerializer serializer)
    {
        serializer.Serialize(writer, value);
    }
}