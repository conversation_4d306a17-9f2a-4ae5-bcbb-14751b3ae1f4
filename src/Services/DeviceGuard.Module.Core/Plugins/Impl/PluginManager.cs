using System.Security.Authentication;
using DeviceGuard.Interface.Cloud;
using DeviceGuard.Interface.FileSystem;
using DeviceGuard.ModuleCore.Modules;
using DeviceGuard.Modules.Interface;
using LinqAsync.Pages;
using Newtonsoft.Json;
using Semver;
using Serilog;

namespace DeviceGuard.ModuleCore.Plugins.Impl;

/// <summary>
/// 基于 JSON 实现的插件管理器,
/// 这个版本所有的插件信息保存在内存中, 
/// </summary>
public class PluginManager : IPluginManager
{
    private readonly IPluginPackageManager _pluginPackageManager;
    private readonly IAppFolderService     _appFolderService;
    private readonly IModuleRegistry       _moduleRegistry;
    private readonly IContainerProvider    _containerProvider;
    private readonly ILogger               _logger;
    private readonly ILoginSession         _loginSession;
    private readonly IPluginOwnedManager   _pluginOwnedManager;


    public PluginManager(IAppFolderService appFolderService,
        IModuleRegistry                    moduleRegistry,
        IContainerProvider                 containerProvider,
        ILogger                            logger,
        IPluginPackageManager              pluginPackageManager,
        ILoginSession                      loginSession,
        IPluginOwnedManager                pluginOwnedManager)
    {
        _appFolderService     = appFolderService;
        _moduleRegistry       = moduleRegistry;
        _containerProvider    = containerProvider;
        _logger               = logger;
        _pluginPackageManager = pluginPackageManager;
        _loginSession         = loginSession;
        _pluginOwnedManager   = pluginOwnedManager;
    }

    #region 插件加载

    /// <summary>
    /// 在内存缓存的所有支持的插件, 目前考虑不超过 10,000 种
    /// </summary>
    private PluginNode[]? _plugins;

    /// <summary>
    /// 保存树的根
    /// </summary>
    private TreeNode? _root;

    /// <summary>
    /// 加载数据时候的锁
    /// </summary>
    private readonly SemaphoreSlim _loadingLocker = new(1, 1);


    /// <summary>
    /// 主动更新插件
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    // [MemberNotNull(nameof(_plugins), nameof(_root))]
    private async Task UpdatePluginCacheAsync(CancellationToken cancellationToken)
    {
        var ownedPlugins = await _loginSession.IsLoggedInAsync(cancellationToken) ? await _pluginOwnedManager.GetMyOwnedPluginsAsync(cancellationToken) : null;

        await _loadingLocker.WaitAsync(cancellationToken);
        try
        {
            if (_plugins == null || _root == null)
            {
                var root = await LoadPluginInfosFromFileAsync(cancellationToken);

                // 添加开发中的模块
                await ApplyDevModulesAsync(root, cancellationToken);

                // 附加安装的插件信息
                await ApplyInstalledPluginsAsync(root, cancellationToken);

                _root    = root;
                _plugins = FlattenPluginTree(root);

                // 循环把 ownedPlugins 加入到 _plugins 中
                if (ownedPlugins != null)
                {
                    foreach (var plugin in _plugins)
                    {
                        if (ownedPlugins.Plugins.Any(x => x.PluginCode == plugin.Id))
                        {
                            plugin.IsOwned = true;
                        }
                    }
                }

                // remove all not necessary modules
                await RemoveUnusedModuleAsync(_plugins, cancellationToken);
            }
        }
        finally
        {
            _loadingLocker.Release();
        }
    }

    private PluginNode[] FlattenPluginTree(TreeNode root)
    {
        return root switch
        {
            PluginNode pluginNode     => [pluginNode],
            CategoryNode categoryNode => categoryNode.Children.SelectMany(FlattenPluginTree).ToArray(),
            _                         => throw new NotSupportedException(),
        };
    }

    /// <summary>
    /// 把开发中的模块添加到插件树中
    /// </summary>
    /// <param name="root"></param>
    /// <param name="cancellationToken"></param>
    private async Task ApplyDevModulesAsync(TreeNode root, CancellationToken cancellationToken)
    {
        if (root is CategoryNode categoryNode)
        {
            var devModules = await _moduleRegistry.GetDevModulesAsync(cancellationToken);
            if (devModules.Any())
            {
                var devNode = new CategoryNode
                {
                    Id    = "debug",
                    Name  = "开发调试",
                    Brief = "提供所有开发中的节点, 仅供开发调试使用",
                    Children = new List<TreeNode>(devModules.Select(devModule => new PluginNode
                    {
                        Id        = DevModuleKeyToPluginId(devModule.Key),
                        Debugging = true,
                        Name      = devModule.Name,
                        Brief     = devModule.Brief ?? "作者未填写简介",
                        Icon      = devModule.Icon,
                        Url       = devModule.Url,
                        IsOwned   = true,
                        Versions =
                        [
                            new() { Version = devModule.Key.Version, Date = DateTime.Now }
                        ],
                        Installed = new PluginInstalled
                        {
                            Id               = DevModuleKeyToPluginId(devModule.Key),
                            Version          = devModule.Key.Version,
                            Module           = devModule.Key,
                            ModuleParameters = "DEBUG",
                        }
                    })),
                };

                categoryNode.Children.Insert(0, devNode);
            }
        }
    }

    /// <summary>
    /// 格式化插件对象
    /// </summary>
    /// <param name="root"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    private async Task ApplyInstalledPluginsAsync(TreeNode root, CancellationToken cancellationToken)
    {
        var pluginsInstalled = await LoadInstalledPluginsAsync(cancellationToken);

        Stack<TreeNode> stack = new([root]);
        while (stack.Count > 0)
        {
            var node = stack.Pop();
            switch (node)
            {
                case PluginNode pluginNode:
                {
                    if (pluginNode.Debugging)
                        continue;

                    pluginNode.Installed = pluginsInstalled.FirstOrDefault(p => p.Id == pluginNode.Id);
                    break;
                }
                case CategoryNode categoryNode:
                {
                    foreach (var child in categoryNode.Children)
                    {
                        stack.Push(child);
                    }

                    break;
                }
            }
        }
    }

    /// <summary>
    /// 更新不再使用的模块
    /// </summary>
    /// <param name="plugins"></param>
    /// <param name="cancellationToken"></param>
    /// <exception cref="NotImplementedException"></exception>
    private async Task RemoveUnusedModuleAsync(PluginNode[] plugins, CancellationToken cancellationToken)
    {
        var modules = await _moduleRegistry.GetInstalledModulesAsync(cancellationToken);
        foreach (var module in modules)
        {
            if (module.IsDevModule)
                continue;

            if (plugins.Any(p => p.Installed?.Module == module.Key))
                continue;

            await _moduleRegistry.UninstallModuleAsync(module.Key, cancellationToken);
        }
    }

    /// <summary>
    /// 从本地的磁盘加载插件数据 (每一个需要读取缓存的函数, 都应该从这里读取)
    /// </summary>
    /// <param name="cancellationToken"></param>
    private async Task<PluginNode[]> GetPluginsFromCacheAsync(CancellationToken cancellationToken)
    {
        if (_plugins == null)
        {
            await UpdatePluginCacheAsync(cancellationToken);
        }

        return _plugins?.ToArray() ?? [];
    }

    /// <summary>
    /// 加载所有已安装的插件
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    /// <exception cref="NotImplementedException"></exception>
    private async Task<PluginInstalled[]> LoadInstalledPluginsAsync(CancellationToken cancellationToken)
    {
        var plugins = new List<PluginInstalled>();

        var pluginDir = _appFolderService.GetOrCreateFolder(AppFolder.Plugins);
        foreach (var pluginDirInfo in Directory.GetDirectories(pluginDir))
        {
            try
            {
                var pluginId = Path.GetFileName(pluginDirInfo);

                var plugin = await GetInstalledPluginAsync(pluginId, cancellationToken) ?? throw new Exception($"插件 '{pluginId}' 安装信息加载失败!");
                plugins.Add(plugin);
            }
            catch (Exception e)
            {
                _logger.Warning($"Load plugin '{pluginDirInfo}' failed: {e.Message}");
            }
        }

        return plugins.ToArray();
    }

    /// <summary>
    /// 获取已安装的插件信息
    /// </summary>
    /// <param name="pluginId"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    /// <exception cref="Exception"></exception>
    /// <exception cref="ModuleNotFoundException"></exception>
    private async Task<PluginInstalled?> GetInstalledPluginAsync(string pluginId, CancellationToken cancellationToken)
    {
        var pluginDir            = _appFolderService.GetOrCreateFolder(AppFolder.Plugins);
        var manifestJsonFilePath = Path.Combine(pluginDir, pluginId, "manifest.json");
        if (!File.Exists(manifestJsonFilePath))
            return null;

        var jsonText = await File.ReadAllTextAsync(manifestJsonFilePath, cancellationToken);
        var manifest = JsonConvert.DeserializeObject<PluginManifest>(jsonText) ?? throw new Exception($"插件 '{pluginId}' manifest.json 文件无法解析!");

        if (manifest.Module == null) throw new ModuleNotFoundException($"插件 '{pluginId}' manifest.json 文件中 Module 字段为空!");
        if (manifest.Id == null) throw new ModuleNotFoundException($"插件 '{pluginId}' manifest.json 文件中 Id 字段为空!");
        if (manifest.Version == null) throw new ModuleNotFoundException($"插件 '{pluginId}' manifest.json 文件中 Version 字段为空!");

        if (manifest.Id != pluginId) throw new Exception($"插件 '{pluginId}' manifest.json 文件中 Id 字段与目录名不一致!");

        var plugin = new PluginInstalled
        {
            Id               = pluginId,
            Version          = manifest.Version,
            Module           = manifest.Module,
            ModuleParameters = manifest.ModuleParameter
        };
        return plugin;
    }


    /// <summary>
    /// 从文件读取插件信息并返回
    /// 本函数不更新对象成员
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    private async Task<TreeNode> LoadPluginInfosFromFileAsync(CancellationToken cancellationToken)
    {
        var manifestFilePath = Path.Combine(_appFolderService.GetOrCreateFolder(AppFolder.PluginDb), AppFolder.PluginDbIndexFileName);
        if (!File.Exists(manifestFilePath))
        {
            throw new Exception($"插件数据库不存在: '{manifestFilePath}'!");
        }

        var json = await File.ReadAllTextAsync(manifestFilePath, cancellationToken);
        var root = JsonConvert.DeserializeObject<TreeNode>(json,
                       new JsonSerializerSettings()
                       {
                           Converters = [new TreeNodeConverter(), new SemVersionConverter()],
                       }) ??
                   throw new InvalidOperationException($"Cannot deserialize plugin release from file '{manifestFilePath}'");

        // 递归更新图标的路径
        Stack<TreeNode> stack = new();
        stack.Push(root);
        while (stack.Count > 0)
        {
            var node = stack.Pop();
            if (node is PluginNode pluginNode)
            {
                // 插件的目录
                var pluginPath = Path.Combine(_appFolderService.GetOrCreateFolder(AppFolder.PluginDb), pluginNode.Id);

                // 查找这个目录是否含有 plugin.png, plugin.ico, plugin.jpg 如果有, 则为按钮文件
                var iconFiles = Directory.GetFiles(pluginPath, "plugin.*");
                if (iconFiles.Length > 0)
                {
                    pluginNode.Icon = new Uri(iconFiles[0]).AbsoluteUri;
                }

                continue;
            }

            if (node is CategoryNode categoryNode)
            {
                foreach (var child in categoryNode.Children)
                {
                    stack.Push(child);
                }

                continue;
            }

            throw new NotSupportedException($"不支持的节点类型: {node.GetType().Name}");
        }

        return root;
    }


    /// <summary>
    /// 把 Dev 模块的 key 转换为插件的 id
    /// </summary>
    /// <param name="key"></param>
    /// <returns></returns>
    private string DevModuleKeyToPluginId(ModuleKey key) => $"{key.Id}-dev";

    #endregion

    /// <summary>
    /// 从云端更新插件数据库
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task UpdatePluginDbFromCloudAsync(CancellationToken cancellationToken)
    {
        await _pluginPackageManager.UpdatePluginDbFromCloudAsync(cancellationToken);
        await UpdatePluginCacheAsync(cancellationToken);
    }

    /// <summary>
    /// 从本地插件库中搜索插件
    /// </summary>
    /// <param name="query">查询条件对象</param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task<Page<PluginNode>> SearchAsync(PluginSearchPredicate query, CancellationToken cancellationToken)
    {
        var pluginInfos = await GetPluginsFromCacheAsync(cancellationToken);
        var q = from p in pluginInfos
                where query.Text == null ||
                      query.Text.Split(' ').All(x => p.GetSearchKeywords().Any(k => k.StartsWith(x)))
                where query.Installed == null || true // TODO 目前没有安装信息
                select p;

        return await q.AsQueryable().ToPageAsync(query, cancellationToken);
    }

    /// <summary>
    /// 获取根分类
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns>返回分类列表, 按照拼音排序</returns>
    public async Task<TreeNode> GetPluginTreeAsync(CancellationToken cancellationToken)
    {
        if (_root == null)
        {
            await UpdatePluginCacheAsync(cancellationToken);
        }

        return _root ?? throw new InvalidOperationException("插件树为空!");
    }

    /// <summary>
    /// 初始化系统
    /// 包括:
    /// - 从云端下载数据库(如果本地不存在)
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task InitializeAsync(CancellationToken cancellationToken)
    {
        var manifestFilePath = Path.Combine(_appFolderService.GetOrCreateFolder(AppFolder.PluginDb), AppFolder.PluginDbIndexFileName);
        // TODO s如果文件超过7天没有更新, 则删除
        if (File.Exists(manifestFilePath) &&
            File.GetLastWriteTime(manifestFilePath) < DateTime.Now.AddDays(-7))
        {
            File.Delete(manifestFilePath);
        }

        if (!File.Exists(manifestFilePath))
        {
            await UpdatePluginDbFromCloudAsync(cancellationToken);
        }
    }

    /// <summary>
    /// 加载插件, 并获取插件的启动接口
    /// </summary>
    /// <param name="plugin"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task<IModuleLoader> LoadPluginAsync(PluginNode plugin, CancellationToken cancellationToken)
    {
        if (plugin == null) throw new ArgumentNullException(nameof(plugin));

        if (plugin.RestartRequired)
        {
            throw new InvalidOperationException($"请重启动软件后重新运行插件!");
        }

        if (plugin.Installed is not { } installed)
        {
            throw new InvalidOperationException($"插件 '{plugin.Id}' 未安装");
        }

        await _loginSession.EnsureLoginAsync(cancellationToken);

        // 登录后检查授权
        if (!plugin.IsOwned)
            throw new InvalidOperationException($"插件 '{plugin.Id}' 未授权");

        // TODO 考虑是否需要缓存
        var module        = await _moduleRegistry.LoadModuleAsync(installed.Module, cancellationToken);
        var bootstrapType = module.GetBootstrapType();
        if (bootstrapType is null)
        {
            throw new InvalidOperationException($"Module '{installed.Module.Id}' does not have a bootstrap type.");
        }

        var bootstrap = _containerProvider.Resolve(bootstrapType) as IModuleLoader ??
                        throw new InvalidOperationException(
                            $"Module '{installed.Module.Id}' bootstrap cannot be resolved.");

        await bootstrap.InitializeAsync(installed.ModuleParameters, cancellationToken);
        return bootstrap;
    }

    public Task OwnAsync(PluginNode pluginInfo)
    {
        pluginInfo.IsOwned = true;
        return Task.CompletedTask;
    }

    public async Task<PluginNode?> GetPluginAsync(string id, CancellationToken cancellationToken)
    {
        var plugins = await GetPluginsFromCacheAsync(cancellationToken);
        return plugins.FirstOrDefault(p => p.Id == id);
    }

    /// <summary>
    /// 安装插件
    /// </summary>
    /// <param name="pluginId"></param>
    /// <param name="version"></param>
    /// <param name="cancellationToken"></param>
    /// <returns>返回是否软件需要重启动, true=需要重启动, false=不需要重启动</returns>
    public async Task<bool> InstallAsync(string pluginId, SemVersion version, CancellationToken cancellationToken)
    {
        var plugin = await GetPluginAsync(pluginId, cancellationToken);
        if (plugin == null)
        {
            throw new ModuleNotFoundException($"插件 '{pluginId}' 版本 '{version}' 不存在!");
        }

        if (plugin.RestartRequired)
        {
            throw new InvalidOperationException($"请重启动软件后再安装插件!");
        }

        // TODO 如果版本不一致, 应该卸载安装的版本
        if (plugin.Installed != null)
        {
            return false;
        }

        var restart = await _pluginPackageManager.InstallAsync(pluginId, version, cancellationToken);

        plugin.Installed = await GetInstalledPluginAsync(pluginId, cancellationToken);

        return restart;
    }

    public async Task<bool> UninstallAsync(string pluginId, CancellationToken cancellationToken)
    {
        var plugin = await GetPluginAsync(pluginId, cancellationToken);
        if (plugin == null)
        {
            throw new ModuleNotFoundException($"插件 '{pluginId}' 不存在!");
        }

        if (plugin.Installed is not { } installed)
        {
            throw new ModuleNotFoundException($"插件 '{pluginId}' 没有安装!");
        }

        await _pluginPackageManager.UninstallAsync(pluginId, cancellationToken);
        plugin.RestartRequired = await _moduleRegistry.UninstallModuleAsync(installed.Module, cancellationToken);
        plugin.Installed       = null;

        return plugin.RestartRequired;
    }
}
