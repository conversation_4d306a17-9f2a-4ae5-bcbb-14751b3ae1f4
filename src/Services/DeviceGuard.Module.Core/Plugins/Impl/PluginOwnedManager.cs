using DeviceGuard.Interface.Cloud;
using DeviceGuard.Interface.FileSystem;
using DeviceGuard.Interface.Licenses;
using DeviceGuard.Interface.Security;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Serilog;

namespace DeviceGuard.ModuleCore.Plugins.Impl;

public class PluginOwnedManager : IPluginOwnedManager
{
    private readonly IAppFolderService  _appFolderService;
    private readonly ILoginSession      _loginSession;
    private readonly IPcSerialNumber    _pcSerialNumber;
    private readonly ISignatureVerifier _signatureVerifier;
    private readonly IUserPluginClient  _userPluginClient;
    private readonly ILogger            _logger;

    public PluginOwnedManager(
        IAppFolderService  appFolderService,
        ILoginSession      loginSession,
        IPcSerialNumber    pcSerialNumber,
        ISignatureVerifier signatureVerifier,
        IUserPluginClient  userPluginClient,
        ILogger            logger)
    {
        _appFolderService  = appFolderService;
        _loginSession      = loginSession;
        _pcSerialNumber    = pcSerialNumber;
        _signatureVerifier = signatureVerifier;
        _userPluginClient  = userPluginClient;
        _logger            = logger;
    }

    public async Task<UserOwnedPluginsData?> GetMyOwnedPluginsAsync(CancellationToken cancellationToken)
    {
        var pluginDbPath = _appFolderService.GetOrCreateFolder(AppFolder.PluginDb);
        var filePath     = Path.Combine(pluginDbPath, "plugin-owned.json");

        // 尝试从本地文件加载和验证
        var validationResult = await TryLoadAndValidateLocalFileAsync(filePath, cancellationToken);

        if (validationResult.IsValid && validationResult.Data != null)
        {
            _logger.Information("Successfully loaded and validated plugin data from local file");
            return validationResult.Data;
        }

        // 本地文件不存在或验证失败，从网络重新下载
        _logger.Information("Local file validation failed, downloading from server. Reason: {Reason}", validationResult.FailureReason);

        try
        {
            await DownloadAndSavePluginDataAsync(filePath, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Failed to download plugin data from server");
            throw new InvalidOperationException("无法从服务器下载插件数据", ex);
        }

        // 重新验证下载的文件
        var retryValidationResult = await TryLoadAndValidateLocalFileAsync(filePath, cancellationToken);

        if (!retryValidationResult.IsValid || retryValidationResult.Data == null)
        {
            _logger.Error("Downloaded plugin data still failed validation. Reason: {Reason}", retryValidationResult.FailureReason);
            throw new InvalidOperationException($"下载的插件数据验证失败: {retryValidationResult.FailureReason}");
        }

        _logger.Information("Successfully downloaded and validated plugin data from server");
        return retryValidationResult.Data;
    }

    /// <summary>
    /// 尝试从本地文件加载并验证插件数据
    /// </summary>
    private async Task<ValidationResult> TryLoadAndValidateLocalFileAsync(string filePath, CancellationToken cancellationToken)
    {
        try
        {
            // 1. 检查文件是否存在
            if (!File.Exists(filePath))
            {
                return ValidationResult.Failed("文件不存在");
            }

            // 2. 读取并解析文件
            var json    = await File.ReadAllTextAsync(filePath, cancellationToken);
            var request = JsonConvert.DeserializeObject<SignatureRequest>(json);

            if (request?.Data == null)
            {
                return ValidationResult.Failed("文件内容无效或数据为空");
            }

            // 3. 验证签名
            bool isSignatureValid = await _signatureVerifier.ValidateAsync(request, cancellationToken);
            if (!isSignatureValid)
            {
                return ValidationResult.Failed("签名验证失败");
            }

            // 4. 验证签名时间戳（不超过7天）
            var signatureAge = DateTime.UtcNow - request.SignatureTimestamp;
            if (signatureAge.TotalDays > 7)
            {
                return ValidationResult.Failed($"签名已过期，签名时间: {request.SignatureTimestamp:yyyy-MM-dd HH:mm:ss}, 已过期 {signatureAge.TotalDays - 7:F1} 天");
            }

            var response = request.Data.ToObject<UserOwnedPluginsData>() ?? throw new Exception("无法解析插件数据");

            // 5. 验证计算机序列号
            var currentSerialNumber = _pcSerialNumber.GetSerialNumber();
            if (response.SerialNumber != currentSerialNumber)
            {
                return ValidationResult.Failed($"计算机序列号不匹配: 期望 {currentSerialNumber}, 实际 {response.SerialNumber}");
            }

            // 6. 验证登录用户
            var loginUser = await _loginSession.GetLoginUserAsync(cancellationToken);
            if (loginUser == null)
            {
                return ValidationResult.Failed("用户未登录");
            }

            if (string.IsNullOrEmpty(response.UserPhoneNumber) || response.UserPhoneNumber != loginUser.PhoneNumber)
            {
                return ValidationResult.Failed($"用户不匹配: 期望 {loginUser.PhoneNumber}, 实际 {response.UserPhoneNumber}");
            }

            return ValidationResult.Success(response);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "验证本地插件文件时发生异常: {FilePath}", filePath);
            return ValidationResult.Failed($"验证过程中发生异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 从服务器下载插件数据并保存到本地文件
    /// </summary>
    private async Task DownloadAndSavePluginDataAsync(string filePath, CancellationToken cancellationToken)
    {
        var currentSerialNumber = _pcSerialNumber.GetSerialNumber();

        _logger.Information("开始从服务器下载插件数据，机器序列号: {SerialNumber}", currentSerialNumber);

        var jsonContent = await _userPluginClient.DownloadMyOwnedPluginsAsync(PluginHost.DgWin, currentSerialNumber, cancellationToken);

        if (string.IsNullOrEmpty(jsonContent))
        {
            throw new InvalidOperationException("从服务器下载的插件数据为空");
        }

        // 确保目录存在
        var directory = Path.GetDirectoryName(filePath);
        if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
        {
            Directory.CreateDirectory(directory);
        }

        // 保存到文件
        await File.WriteAllTextAsync(filePath, jsonContent, cancellationToken);

        _logger.Information("插件数据已保存到本地文件: {FilePath}", filePath);
    }

    /// <summary>
    /// 验证结果辅助类
    /// </summary>
    private class ValidationResult
    {
        public bool                  IsValid       { get; private set; }
        public string                FailureReason { get; private set; } = "";
        public UserOwnedPluginsData? Data          { get; private set; }

        private ValidationResult()
        {
        }

        public static ValidationResult Success(UserOwnedPluginsData data)
        {
            return new ValidationResult
            {
                IsValid = true,
                Data    = data
            };
        }

        public static ValidationResult Failed(string reason)
        {
            return new ValidationResult
            {
                IsValid       = false,
                FailureReason = reason
            };
        }
    }
}
