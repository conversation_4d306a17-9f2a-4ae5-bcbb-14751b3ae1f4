using Newtonsoft.Json;
using Semver;

namespace DeviceGuard.ModuleCore.Plugins.Impl;

public class SemVersionConverter : JsonConverter
{
    public override bool CanConvert(Type objectType)
    {
        return objectType == typeof(SemVersion);
    }

    public override object? Read<PERSON><PERSON>(JsonReader reader, Type objectType, object? existingValue, JsonSerializer serializer)
    {
        var value = reader.Value?.ToString();
        return value == null ? null : SemVersion.Parse(value);
    }

    public override void Write<PERSON><PERSON>(JsonWriter writer, object? value, JsonSerializer serializer)
    {
        serializer.Serialize(writer, value?.ToString());
        writer.Flush();
    }
}
