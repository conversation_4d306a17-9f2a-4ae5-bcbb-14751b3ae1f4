using System.IO.Compression;
using System.Security.Cryptography;
using DeviceGuard.Interface.Cloud;
using DeviceGuard.Interface.FileSystem;
using DeviceGuard.ModuleCore.Modules;
using Newtonsoft.Json;
using Semver;

namespace DeviceGuard.ModuleCore.Plugins.Impl;

public class PluginPackageManager : IPluginPackageManager
{
    private readonly IPluginClient     _pluginClient;
    private readonly IPluginDbClient   _pluginDbClient;
    private readonly IAppFolderService _appFolderService;
    private readonly IModuleRegistry   _moduleRegistry;

    public PluginPackageManager(
        IAppFolderService appFolderService,
        IModuleRegistry   moduleRegistry,
        IPluginDbClient   pluginDbClient,
        IPluginClient     pluginClient)
    {
        _pluginDbClient   = pluginDbClient;
        _pluginClient     = pluginClient;
        _appFolderService = appFolderService;
        _moduleRegistry   = moduleRegistry;
        _pluginDbClient   = pluginDbClient;
    }

    /// <summary>
    /// 获取指定Key的插件, 并返回插件文件路径
    /// 这个方法
    /// </summary>
    /// <param name="pluginId"></param>
    /// <param name="version"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    private async Task<string> DownloadPluginAsync(string pluginId, SemVersion version, CancellationToken cancellationToken)
    {
        var packageDir     = _appFolderService.GetOrCreateFolder(AppFolder.PluginPackages);
        var pluginFilePath = Path.Combine(packageDir, GetPluginFileName(pluginId, version));

        // 如果文件存在, 检查文件一致性

        if (!File.Exists(pluginFilePath))
        {
            var content = await _pluginClient.DownloadPluginAsync(pluginId, version.ToString(), cancellationToken);
            if (content.Length == 0)
            {
                throw new ModuleNotFoundException($"插件 '{pluginId}' 版本 '{version}' 的安装包无法下载!");
            }

            await File.WriteAllBytesAsync(pluginFilePath, content, cancellationToken);
        }

        return pluginFilePath;
    }

    private string GetPluginFileName(string pluginId, SemVersion version)
    {
        return $"{pluginId}-{version}.zip";
    }

    /// <summary>
    /// 安装插件
    /// </summary>
    /// <param name="pluginId"></param>
    /// <param name="version"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task<bool> InstallAsync(string pluginId, SemVersion version, CancellationToken cancellationToken)
    {
        // 下载包
        var pluginFilePath = await DownloadPluginAsync(pluginId, version, cancellationToken);
        if (!File.Exists(pluginFilePath))
        {
            throw new ModuleNotFoundException($"插件 '{pluginId}' 版本 '{version}' 的安装包不存在!");
        }

        // 文件解压缩到临时目录
        var tempDir = Path.Combine(Path.GetTempPath(), Path.GetRandomFileName());
        try
        {
            await Task.Run(() => ZipFile.ExtractToDirectory(pluginFilePath, tempDir), cancellationToken);

            // 读取 manifest.json 文件,
            if (!File.Exists(Path.Combine(tempDir, "manifest.json")))
            {
                throw new ModuleNotFoundException($"插件 '{pluginId}' 版本 '{version}' 的安装包 manifest.json 文件不存在!");
            }

            var text     = await File.ReadAllTextAsync(Path.Combine(tempDir, "manifest.json"), cancellationToken);
            var manifest = JsonConvert.DeserializeObject<PluginManifest>(text);
            if (manifest == null)
                throw new ModuleNotFoundException($"插件 '{pluginId}' 版本 '{version}' 的安装包 manifest.json 文件无法解析!");

            if (manifest.Module == null) throw new ModuleNotFoundException($"插件 '{pluginId}' 版本 '{version}' 的安装包 manifest.json 文件中 Module 字段为空!");
            if (manifest.Id == null) throw new ModuleNotFoundException($"插件 '{pluginId}' 版本 '{version}' 的安装包 manifest.json 文件中 Id 字段为空!");
            if (manifest.Version == null) throw new ModuleNotFoundException($"插件 '{pluginId}' 版本 '{version}' 的安装包 manifest.json 文件中 Version 字段为空!");

            if (manifest.Id != pluginId)
                throw new ModuleNotFoundException($"插件 '{pluginId}' 版本 '{version}' 的安装包 manifest.json 文件中 Id 字段 '{manifest.Id}' 与期望不一致!");

            if (SemVersion.Parse(manifest.Version) != version)
                throw new ModuleNotFoundException($"插件 '{pluginId}' 版本 '{version}' 的安装包 manifest.json 文件中 Version 字段 '{manifest.Version}' 与期望不一致!");

            // 安装 Module
            var restartRequired = await _moduleRegistry.InstallModuleAsync(manifest.Module, cancellationToken);

            // 复制插件所有文件至目标目录
            var pluginTargetDir = Path.Combine(_appFolderService.GetOrCreateFolder(AppFolder.Plugins), pluginId);
            Directory.CreateDirectory(pluginTargetDir);
            foreach (var file in Directory.GetFiles(tempDir))
            {
                var fileName = Path.GetFileName(file);
                var destFile = Path.Combine(pluginTargetDir, fileName);
                File.Copy(file, destFile, true);
            }

            return restartRequired;
        }
        finally
        {
            try
            {
                Directory.Delete(tempDir, true);
            }
            catch (Exception)
            {
                // ignored
            }
        }
    }

    public Task UninstallAsync(string pluginId, CancellationToken cancellationToken)
    {
        // 复制插件所有文件至目标目录
        var pluginTargetDir = Path.Combine(_appFolderService.GetOrCreateFolder(AppFolder.Plugins), pluginId);
        Directory.Delete(pluginTargetDir, true);
        return Task.CompletedTask;
    }

    /// <summary>
    /// 更新插件数据库
    /// </summary>
    /// <returns></returns>
    public async Task UpdatePluginDbFromCloudAsync(CancellationToken cancellation)
    {
        var pluginDbPath = _appFolderService.GetOrCreateFolder(AppFolder.PluginDb);
        var hashFilePath = Path.Combine(pluginDbPath, "sha1");

        if (File.Exists(hashFilePath))
        {
            // 比对文件的 sha1
            var existSha1 = await File.ReadAllTextAsync(hashFilePath, cancellation);
            var model     = await _pluginDbClient.GetPluginDbMetaAsync(PluginHost.DgWin, cancellation);
            if (model.Sha1 == existSha1)
            {
                return;
            }
        }

        // 如果不存在, 直接下载
        var content = await _pluginDbClient.DownloadPluginDbAsync(PluginHost.DgWin, cancellation);
        if (content.Length == 0)
        {
            throw new ModuleNotFoundException("插件数据库下载失败");
        }

        using var stream = new MemoryStream(content);

        // 2. 解压到临时目录
        var tempDir = Path.Combine(Path.GetTempPath(), Path.GetRandomFileName());
        try
        {
            Directory.CreateDirectory(tempDir);

            // 解压 content 这个 zip 文件到 pluginDbPath
            using var zipArchive = new ZipArchive(stream, ZipArchiveMode.Read);
            zipArchive.ExtractToDirectory(tempDir, true);

            // 计算 sha1 并写到 sha1 文件
            var sha1 = await SHA1.Create().ComputeHashAsync(stream, cancellation);
            await File.WriteAllTextAsync(hashFilePath, BitConverter.ToString(sha1).Replace("-", ""), cancellation);

            // 3. 验证 plugins-db.json
            var jsonPath = Path.Combine(tempDir, AppFolder.PluginDbIndexFileName);
            if (!File.Exists(jsonPath))
            {
                throw new ModuleNotFoundException($"插件数据库缺失 '{AppFolder.PluginDbIndexFileName}' 文件");
            }

            var json = await File.ReadAllTextAsync(jsonPath, cancellation);
            if (!IsValidPluginDb(json)) // 需要实现验证逻辑
            {
                throw new ModuleNotFoundException($"文件 '{AppFolder.PluginDbIndexFileName}' 无效");
            }

            // 4. 删除旧数据库
            var dbDir = _appFolderService.GetOrCreateFolder(AppFolder.PluginDb);
            if (Directory.Exists(dbDir))
            {
                Directory.Delete(dbDir, true);
            }

            Directory.CreateDirectory(dbDir);

            // 5. 递归目录复制新文件
            foreach (var filePath in Directory.GetFiles(tempDir, "*", SearchOption.AllDirectories))
            {
                var relativePath = Path.GetRelativePath(tempDir, filePath);
                var destFilePath = Path.Combine(dbDir, relativePath);

                var destFileDir = Path.GetDirectoryName(destFilePath);
                if (!string.IsNullOrEmpty(destFileDir) && !Directory.Exists(destFileDir))
                {
                    Directory.CreateDirectory(destFileDir);
                }

                File.Copy(filePath, destFilePath, true);
            }
        }
        finally
        {
            try
            {
                Directory.Delete(tempDir, true);
            }
            catch (Exception)
            {
                // 忽略清理异常
            }
        }
    }

    /// <summary>
    /// 验证插件数据库
    /// </summary>
    /// <param name="json"></param>
    /// <returns></returns>
    private bool IsValidPluginDb(string json)
    {
        try
        {
            var settings = new JsonSerializerSettings()
            {
                Converters = [new TreeNodeConverter(), new SemVersionConverter()]
            };
            var plugins = JsonConvert.DeserializeObject<CategoryNode>(json, settings);
            return plugins != null;
        }
        catch
        {
            return false;
        }
    }
}
