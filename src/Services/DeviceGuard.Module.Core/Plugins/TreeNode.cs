using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace DeviceGuard.ModuleCore.Plugins;

/// <summary>
/// 抽象树节点基类，表示插件系统的可嵌套元素
/// </summary>
public abstract class TreeNode : INotifyPropertyChanged
{
    /// <summary>节点唯一标识</summary>
    public string Id { get; init; } = "";

    /// <summary>显示名称</summary>
    public string Name { get; init; } = string.Empty;

    /// <summary>节点类型（Category/Plugin）</summary>
    public abstract NodeType Type { get; }

    public bool IsCategoryNode => Type == NodeType.Category;

    /// <summary>关联标签集合</summary>
    public string[] Tags { get; set; } = [];

    /// <summary>简要描述</summary>
    public string Brief { get; set; } = string.Empty;
    
    /// <summary>相关链接地址</summary>
    public string? Url { get; set; }

    /// <summary>
    /// 角标信息
    /// </summary>
    public virtual object? Badge { get; } = null;

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    protected bool SetField<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
    {
        if (EqualityComparer<T>.Default.Equals(field, value)) return false;
        field = value;
        OnPropertyChanged(propertyName);
        return true;
    }
}
