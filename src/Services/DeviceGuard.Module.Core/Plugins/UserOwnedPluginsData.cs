namespace DeviceGuard.ModuleCore.Plugins;

/// <summary>
/// 用户拥有的插件数据
/// </summary>
public class UserOwnedPluginsData
{
    /// <summary>
    /// 机器序列号
    /// </summary>
    public string SerialNumber { get; set; } = "";

    /// <summary>
    /// 用户ID
    /// </summary>
    public string UserId { get; set; } = "";

    /// <summary>
    /// 用户名
    /// </summary>
    public string UserPhoneNumber { get; set; } = "";

    /// <summary>
    /// 插件列表
    /// </summary>
    public UserOwnedPlugin[] Plugins { get; set; } = [];
}
