using DeviceGuard.Modules.Interface;
using LinqAsync.Pages;
using Semver;

namespace DeviceGuard.ModuleCore.Plugins;

/// <summary>
/// 插件管理器, 管理安装在本机的插件
/// </summary>
public interface IPluginManager
{
    /// <summary>
    /// 初始化系统
    /// 包括:
    /// - 从云端下载数据库(如果本地不存在)
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public Task InitializeAsync(CancellationToken cancellationToken);

    /// <summary>
    /// 获取根分类
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns>返回分类列表, 按照拼音排序</returns>
    public Task<TreeNode> GetPluginTreeAsync(CancellationToken cancellationToken);


    /// <summary>
    /// 从本地插件库中搜索插件
    /// </summary>
    /// <param name="query">查询条件对象</param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public Task<Page<PluginNode>> SearchAsync(PluginSearchPredicate query, CancellationToken cancellationToken);

    /// <summary>
    /// 获取指定Id的插件
    /// </summary>
    /// <returns></returns>
    public Task<PluginNode?> GetPluginAsync(string id, CancellationToken cancellationToken);

    /// <summary>
    /// 安装插件
    /// </summary>
    /// <param name="id">插件的Id</param>
    /// <param name="version">插件的版本</param>
    /// <param name="cancellationToken"></param>
    /// <returns>返回是否软件需要重启动, true=需要重启动, false=不需要重启动</returns>
    public Task<bool> InstallAsync(string id, SemVersion version, CancellationToken cancellationToken);

    /// <summary>
    /// 从本地移除该插件
    /// </summary>
    /// <returns></returns>
    public Task<bool> UninstallAsync(string id, CancellationToken cancellationToken);

    /// <summary>
    /// 从云端更新插件数据库
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task UpdatePluginDbFromCloudAsync(CancellationToken cancellationToken);

    /// <summary>
    /// 加载插件, 并获取插件的启动接口
    /// </summary>
    /// <param name="plugin"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task<IModuleLoader> LoadPluginAsync(PluginNode plugin, CancellationToken cancellationToken);

    /// <summary>
    /// 获取插件
    /// TODO 后期考虑续费
    /// </summary>
    /// <param name="pluginInfo"></param>
    /// <returns></returns>
    Task OwnAsync(PluginNode pluginInfo);
}
