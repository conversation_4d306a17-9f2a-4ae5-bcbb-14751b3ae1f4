// using System.ComponentModel;
// using System.Diagnostics.CodeAnalysis;
//
// namespace DeviceGuard.ModuleCore.Plugins;
//
// /// <summary>
// /// 插件信息
// /// </summary>
// public class PluginInfo : INotifyPropertyChanged
// {
//     public static PluginInfo Empty { get; } = new([
//         new PluginRelease
//         {
//             Id       = "Null",
//             Version  = new Version(0, 0, 0),
//             Type     = VersionType.Dev,
//             Path     = [],
//             Brief    = "",
//             Platform = PlatformType.Windows,
//         }
//     ]);
//
//     private readonly Dictionary<PluginVersionKey, PluginRelease> _releaseDict;
//
//     public PluginInfo(PluginRelease[] releases)
//     {
//         if (releases == null) throw new ArgumentNullException(nameof(releases));
//         if (releases.Length == 0) throw new ArgumentException("Value cannot be an empty collection.", nameof(releases));
//
//         _releaseDict = releases.ToDictionary(x => new PluginVersionKey(x.Version, x.Type), x => x);
//         Releases     = releases.OrderBy(x => x.Version).ThenBy(x => x.Type).ToArray();
//     }
//
//
//     /// <summary>
//     /// Id
//     /// </summary>
//     public string Id => Latest.Id;
//
//     /// <summary>
//     /// 插件名称
//     /// </summary>
//     public string Name => (InstalledRelease ?? Latest).Name;
//
//     /// <summary>
//     /// 插件路径
//     /// </summary>
//     public string[] Path => (InstalledRelease ?? Latest).Path;
//
//     /// <summary>
//     /// 插件简介
//     /// </summary>
//     public string? Brief => (InstalledRelease ?? Latest).Brief;
//
//     public string? Url => (InstalledRelease ?? Latest).Url;
//
//     /// <summary>
//     /// 插件的版本
//     /// </summary>
//     public PluginRelease Latest => Releases.Last();
//
//     /// <summary>
//     /// 已安装在本地的版本
//     /// </summary>
//     public PluginInstalled? Installed { get; set; }
//
//     /// <summary>
//     /// 已安装在本地的版本的 info
//     /// </summary>
//     public PluginRelease? InstalledRelease => Installed is not null && _releaseDict.TryGetValue(new PluginVersionKey(Installed.Version, Installed.Type), out var release)
//         ? release
//         : null;
//
//     /// <summary>
//     /// 软件是否已经安装
//     /// </summary>
//     [MemberNotNullWhen(true, nameof(Installed))]
//     public bool IsInstalled => Installed != null;
//
//     /// <summary>
//     /// 是否已经拥有插件
//     /// </summary>
//     public bool IsOwned { get; set; }
//
//     public PluginState State
//     {
//         get
//         {
//             if (!IsOwned)
//             {
//                 return PluginState.None;
//             }
//
//             if (!IsInstalled)
//             {
//                 return PluginState.Owned;
//             }
//
//             return NewVersionAvailable ? PluginState.NewVersionAvailable : PluginState.Installed;
//         }
//     }
//
//     /// <summary>
//     /// 是否可以获取
//     /// </summary>
//     public bool CanOwn => !IsOwned;
//
//     /// <summary>
//     /// 是否可以安装
//     /// </summary>
//     public bool CanInstall => IsOwned && !IsInstalled;
//
//     /// <summary>
//     /// 是否可以运行
//     /// </summary>
//     public bool CanRun => IsInstalled;
//
//     /// <summary>
//     /// 是否可以卸载
//     /// </summary>
//     public bool CanUninstall => IsInstalled && Installed.Type != VersionType.Dev;
//
//     /// <summary>
//     /// 是否可以升级
//     /// </summary>
//     public bool CanUpgrade => IsInstalled && NewVersionAvailable;
//
//     /// <summary>
//     /// 是否有新版本
//     /// </summary>
//     public bool NewVersionAvailable => Installed is not null && Releases.Last().Version > Installed.Version;
//
//     /// <summary>
//     /// 所有的发行版本信息
//     /// </summary>
//     public PluginRelease[] Releases { get; }
//
//
//     #region 比较器
//
//     protected bool Equals(PluginInfo other)
//     {
//         return Id.Equals(other.Id);
//     }
//
//     public override bool Equals(object? obj)
//     {
//         if (obj is null) return false;
//         if (ReferenceEquals(this, obj)) return true;
//         if (obj.GetType() != GetType()) return false;
//         return Equals((PluginInfo)obj);
//     }
//
//     public override int GetHashCode()
//     {
//         return Id.GetHashCode();
//     }
//
//     #endregion
//
//     public event PropertyChangedEventHandler? PropertyChanged;
// }
//
// public record PluginVersionKey
// {
//     public PluginVersionKey(Version version, VersionType type)
//     {
//         Version = version;
//         Type    = type;
//     }
//
//     public Version Version { get; }
//
//     public VersionType Type { get; }
// }
