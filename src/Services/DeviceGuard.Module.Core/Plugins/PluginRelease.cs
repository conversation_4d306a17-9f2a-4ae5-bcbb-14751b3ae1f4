using Semver;

namespace DeviceGuard.ModuleCore.Plugins;

/// <summary>
/// 一个安装在本地的插件, 就是一个对具体型号设备完成检测与维修的功能.
/// </summary>
/// <remarks>
/// 他由一个模块和相应的配置参数组成, 一个系列的检修软件可能除参数以外几乎完全相同, 我们可以为这一个系列的产品开发一个模块,
/// 同时使用不同的参数为不同序号的产品进行具体配置, 配置了具体运行参数的模块我们称为插件.
/// 例如: 一个英飞源的 REG1K0135A2 PCS (https://www.infypower.cn/three-phase-input/46) 的维修检测仪
/// 需要一个通用的英飞源的维修检测仪模块, 以及该型号对应的运行参数组成,
///
/// 同时我们在为该 PCS 检测仪设置了导航, 该导航级数至少为一级, 允许设置为二级, 三级, 四级等, 不做限制 
/// 例如: 电源模块检测仪 -> 英飞源 -> REG1K0135A2
/// 或者: 电源模块检测仪 -> 英飞源 -> 充电模块 -> REG1K0135A2
/// </remarks>
public class PluginRelease
{
    /// <summary>
    /// 插件 Id, 唯一识别插件的标识
    /// </summary>
    public required string Id { get; init; }

    /// <summary>
    /// 版本号
    /// </summary>
    public required SemVersion Version { get; init; }

    /// <summary>
    /// 版本类型
    /// </summary>
    public bool IsRelease => Version.IsRelease;

    /// <summary>
    /// 发布时间
    /// </summary>
    public DateOnly? PublishDate { get; init; }


    /// <summary>
    /// 插件路径
    /// </summary>
    public required string[] Path { get; init; }

    /// <summary>
    /// 插件名称
    /// </summary>
    public string Name => Path.Last();

    /// <summary>
    /// 为插件的打的标签, 用于搜索过滤
    /// </summary>
    public string[] Tags { get; init; } = [];

    /// <summary>
    /// 详细介绍的网址
    /// </summary>
    public required string Brief { get; init; }

    /// <summary>
    /// 运行插件的平台
    /// </summary>
    public required PlatformType Platform { get; init; }

    /// <summary>
    /// 详细介绍的网址
    /// </summary>
    public string? Url { get; init; }

    /// <summary>
    /// 缓存的搜索关键字
    /// </summary>
    private string[]? _searchKeywords;

    /// <summary>
    /// 搜索关键字
    /// </summary>
    public string[] GetSearchKeywords()
    {
        return _searchKeywords ??= MakeCache();

        string[] MakeCache()
        {
            var keywords = new List<string>([
                Id, Name, PinyinNet.PinyinConvert.GetPinyin(Name), PinyinNet.PinyinConvert.GetPinyinFirstLetter(Name)
            ]);
            keywords.AddRange(Path);
            keywords.AddRange(Tags);
            keywords.AddRange(Brief?.Split(' ') ?? []);

            return keywords.ToArray();
        }
    }

    public bool MatchPath(string[] path, bool recursion = true)
    {
        if (path.Length > Path.Length)
        {
            return false;
        }

        if (path.Where((t, i) => t != Path[i]).Any())
        {
            return false;
        }

        if (!recursion && path.Length < Path.Length)
        {
            return false;
        }

        return true;
    }
}
