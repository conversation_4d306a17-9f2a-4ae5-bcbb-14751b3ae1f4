using DeviceGuard.ModuleCore.Modules;
using Semver;

namespace DeviceGuard.ModuleCore.Plugins;

/// <summary>
/// 插件配置文件, 这个文件的信息来自每一个插件的目录中的 manifest.json 文件
/// </summary>
public class PluginInstalled
{
    /// <summary>
    /// 插件 Id, 唯一识别插件的标识
    /// </summary>
    public required string Id { get; init; }

    /// <summary>
    /// 版本号
    /// </summary>
    public required string Version { get; init; }

    public SemVersion SemVersion => Semver.SemVersion.Parse(Version);

    /// <summary>
    /// 版本类型
    /// </summary>
    public bool IsRelease => SemVersion.IsRelease;

    /// <summary>
    /// 模块名称, 系统中的模块名不可重复
    /// </summary>
    public required ModuleKey Module { get; set; }

    /// <summary>
    /// 插件启动时要传入模块的参数(加密)
    /// </summary>
    public string? ModuleParameters { get; set; }
}
