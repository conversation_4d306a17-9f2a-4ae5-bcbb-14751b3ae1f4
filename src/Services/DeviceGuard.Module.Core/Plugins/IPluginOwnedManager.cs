using DeviceGuard.Interface.Cloud;

namespace DeviceGuard.ModuleCore.Plugins;

public interface IPluginOwnedManager
{
    /// <summary>
    /// 获取登录用户拥有的插件数据
    /// 从保存的 plugin-owned.json 文件中加载, 存在以下情况, 重新从网络下载
    /// 1. 该文件不存在
    /// 2. 该文件无法通过验证
    ///     1. 签名证书无法通过 ISignatureVerifier.ValidateAsync
    ///     2. 签名时间超过7天
    ///     3. 里边的 serialNumber 不等于当前计算机获取的序列号. (通过 IPcSerialNumber 获取)
    ///     4. 里边的 userId 或者 userName 无法与当前登录用户匹配
    /// 如果是重新下载的插件拥有者模块, 重新执行检查 然后重新执行文件的加载和检查, 要是还错, 就抛异常
    /// </summary>
    /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
    /// <returns>The user owned plugin data structure.</returns>
    Task<UserOwnedPluginsData?> GetMyOwnedPluginsAsync(CancellationToken cancellationToken);
}
