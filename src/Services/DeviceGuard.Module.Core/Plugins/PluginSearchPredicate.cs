using LinqAsync.Pages;

namespace DeviceGuard.ModuleCore.Plugins;

/// <summary>
/// 插件查找的条件对象模型
/// </summary>
public class PluginSearchPredicate : PageFilter
{
    /// <summary>
    /// 查询条件
    /// </summary>
    public string? Text { get; set; }

    /// <summary>
    /// 在指定的路径中搜索
    /// </summary>
    public string[]? Path { get; set; }

    /// <summary>
    /// 递归搜索子路径
    /// </summary>
    public bool Recursion { get; set; }

    /// <summary>
    /// 仅仅查询已经(没有)安装的插件
    /// </summary>
    public bool? Installed { get; set; }
}
