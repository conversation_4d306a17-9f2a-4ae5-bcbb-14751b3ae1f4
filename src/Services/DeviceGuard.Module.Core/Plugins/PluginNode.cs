using Newtonsoft.Json;

namespace DeviceGuard.ModuleCore.Plugins;

/// <summary>
/// 插件节点类型，包含版本信息
/// </summary>
public class PluginNode : TreeNode
{
    public static PluginNode Empty { get; } = new PluginNode
    {
        Id = "Null",
    };

    /// <summary>图标资源路径</summary>
    public string? Icon { get; set; }

    /// <summary>
    /// 是否为处于调试期间的插件
    /// </summary>
    [JsonIgnore]
    public bool Debugging { get; init; }

    /// <summary>
    /// 插件版本列表
    /// </summary>
    public PluginVersion[] Versions { get; init; } = [];

    /// <summary>
    /// 最新版本
    /// </summary>
    public PluginVersion Latest => Versions.LastOrDefault() ?? PluginVersion.Empty;

    /// <summary>
    /// 当前安装的插件
    /// </summary>
    [JsonIgnore]
    public PluginInstalled? Installed { get; set; }

    #region Searchs

    /// <summary>
    /// 缓存的搜索关键字
    /// </summary>
    private string[]? _searchKeywords;

    /// <summary>
    /// 搜索关键字
    /// </summary>
    public string[] GetSearchKeywords()
    {
        return _searchKeywords ??= MakeCache();

        string[] MakeCache()
        {
            var keywords = new List<string>([
                Id, Name, PinyinNet.PinyinConvert.GetPinyin(Name), PinyinNet.PinyinConvert.GetPinyinFirstLetter(Name)
            ]);
            keywords.AddRange(Tags);
            keywords.AddRange(Brief?.Split(' ') ?? []);

            return keywords.ToArray();
        }
    }

    #endregion

    #region 提供给UI的状态

    /// <summary>
    /// 插件状态的显示
    /// </summary>
    public override object? Badge => State;

    /// <summary>
    /// 插件的状态
    /// </summary>
    public PluginState State
    {
        get
        {
            if (!IsOwned)
            {
                return PluginState.None;
            }

            if (!IsInstalled)
            {
                return PluginState.Owned;
            }

            return NewVersionAvailable ? PluginState.NewVersionAvailable : PluginState.Installed;
        }
    }

    /// <summary>
    /// 是否已经拥有插件
    /// </summary>
    public bool IsOwned { get; set; } = false;

    /// <summary>
    /// 软件是否已经安装
    /// </summary>
    public bool IsInstalled => Installed != null;

    /// <summary>
    /// 是否可以获取
    /// </summary>
    public bool CanOwn => !IsOwned;

    /// <summary>
    /// 是否可以安装
    /// </summary>
    public bool CanInstall => !RestartRequired && IsOwned && !IsInstalled;

    /// <summary>
    /// 是否可以运行
    /// </summary>
    public bool CanRun => !RestartRequired && IsOwned && IsInstalled;

    /// <summary>
    /// 是否可以卸载
    /// </summary>
    public bool CanUninstall => !RestartRequired && IsInstalled && !Debugging;

    /// <summary>
    /// 是否可以升级
    /// </summary>
    public bool CanUpgrade => !RestartRequired && IsInstalled && NewVersionAvailable;

    /// <summary>
    /// 是否需要重启动软件生效
    /// </summary>
    public bool RestartRequired { get; set; }

    /// <summary>
    /// 是否有新版本
    /// </summary>
    public bool NewVersionAvailable => Installed is { } installed && Versions.Last().CompareWith(installed) > 0;

    #endregion

    public override NodeType Type => NodeType.Plugin;
}
