<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <RootNamespace>DeviceGuard.Infrastructure</RootNamespace>
    <Platforms>AnyCPU;x64</Platforms>
    <Nullable>enable</Nullable>
  </PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
		<DebugType>none</DebugType>
		<DebugSymbols>false</DebugSymbols>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
	  <DebugType>none</DebugType>
	  <DebugSymbols>false</DebugSymbols>
	</PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Interfaces\DeviceGuard.Interface\DeviceGuard.Interface.csproj" />
    <ProjectReference Include="..\..\Interfaces\DeviceGuard.Windows.Interface\DeviceGuard.Windows.Interface.csproj" />
    <ProjectReference Include="..\Configuration\Configuration.csproj" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Flurl" Version="4.0.0" />
    <PackageReference Include="Flurl.Http" Version="4.0.2" />
    <PackageReference Include="Microsoft.IdentityModel.Protocols" Version="7.7.1" />
    <PackageReference Include="Microsoft.IdentityModel.Protocols.OpenIdConnect" Version="7.7.1" />
    <PackageReference Include="Semver" Version="3.0.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.7.1" />
  </ItemGroup>
</Project>
