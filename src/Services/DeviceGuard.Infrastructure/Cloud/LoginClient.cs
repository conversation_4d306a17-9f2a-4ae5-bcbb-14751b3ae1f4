using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Security.Authentication;
using System.Threading;
using System.Threading.Tasks;
using DeviceGuard.Interface.Cloud;
using Microsoft.IdentityModel.Protocols;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using Newtonsoft.Json;

namespace DeviceGuard.Infrastructure.Cloud;

/// <summary>
/// 登录客户端
/// </summary>
public class LoginClient : ILoginClient
{
    private readonly string _clientId     = "dgc-win";
    private readonly string _clientSecret = "ETgBVg8pDVPvUGVwjqsXzAY0SBsGgqFb";

    // 缓存 OpenId 配置，避免重复获取
    private readonly ConfigurationManager<OpenIdConnectConfiguration> _configManager =
        new("https://auth.chkfun.com/realms/device-guard/.well-known/openid-configuration",
            new OpenIdConnectConfigurationRetriever()
        );

    public async Task<TokenResponse> LoginAsync(string userName, string password)
    {
        if (userName == null) throw new ArgumentNullException(nameof(userName));

        var config = await _configManager.GetConfigurationAsync(CancellationToken.None);

        using HttpClient client = new();
        var form = new Dictionary<string, string>
        {
            { "grant_type", "password" },
            { "client_id", _clientId },
            { "client_secret", _clientSecret },
            { "username", userName },
            { "password", password }
        };

        var response = await client.PostAsync(config.TokenEndpoint, new FormUrlEncodedContent(form));
        var content  = await response.Content.ReadAsStringAsync();

        if (!response.IsSuccessStatusCode)
        {
            var errorObj    = JsonConvert.DeserializeObject<Dictionary<string, string>>(content);
            var error       = errorObj?.GetValueOrDefault("error") ?? "unknown";
            var description = errorObj?.GetValueOrDefault("error_description") ?? "";

            var errorMessage = error switch
            {
                "invalid_grant"          => "用户名或密码错误",
                "invalid_request"        => "请求不合法，缺少参数或格式错误",
                "unauthorized_client"    => "客户端未被授权执行此操作",
                "unsupported_grant_type" => "不支持的认证类型",
                "invalid_client"         => "客户端身份验证失败，请检查配置",
                _                        => $"登录失败：{description}"
            };
            throw new AuthenticationException($"{errorMessage} ({(int)response.StatusCode})");
        }

        return JsonConvert.DeserializeObject<TokenResponse>(content)
               ?? throw new AuthenticationException("登录失败: 无法解析响应内容");
    }

    public async Task<TokenResponse> RefreshTokenAsync(string refreshToken, CancellationToken cancellation)
    {
        var form = new Dictionary<string, string>
        {
            ["grant_type"]    = "refresh_token",
            ["client_id"]     = _clientId,
            ["refresh_token"] = refreshToken
        };

        if (!string.IsNullOrEmpty(_clientSecret))
            form["client_secret"] = _clientSecret;

        var config = await _configManager.GetConfigurationAsync(cancellation);

        using HttpClient client = new();
        using var response = await client.PostAsync(
            config.TokenEndpoint,
            new FormUrlEncodedContent(form),
            cancellation);

        response.EnsureSuccessStatusCode();

        var content = await response.Content.ReadAsStringAsync(cancellation);
        return JsonConvert.DeserializeObject<TokenResponse>(content)
               ?? throw new AuthenticationException("刷新Token失败: 无法解析响应内容");
    }
}
