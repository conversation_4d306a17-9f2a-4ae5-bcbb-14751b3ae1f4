{"openapi": "3.0.1", "info": {"title": "DeviceGuardCloud", "version": "1.0"}, "paths": {"/api/v1/admin-roles": {"get": {"tags": ["admin-roles"], "summary": "获取平台运维管理员的角色列表", "description": "需要权限: Admin:AdminRoleRead", "operationId": "AdminRoles_GetRoles", "parameters": [{"name": "filter.name", "in": "query", "schema": {"type": "string"}}, {"name": "filter.skip", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "filter.take", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "selector", "in": "query", "schema": {"$ref": "#/components/schemas/AdminRoleQuerySelector"}}, {"name": "page.skip", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "page.take", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminRoleViewModelPage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminRoleViewModelPage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminRoleViewModelPage"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "post": {"tags": ["admin-roles"], "summary": "添加平台运维管理员的角色", "description": "需要权限: Admin:AdminRoleCreate", "operationId": "AdminRoles_CreateRole", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/CreateAdminRoleCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreateAdminRoleCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateAdminRoleCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateAdminRoleCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/IdentModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/IdentModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IdentModel"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/admin-roles/{adminRoleId}": {"get": {"tags": ["admin-roles"], "summary": "根据ID获取平台运维管理员的角色", "description": "需要权限: Admin:AdminRoleRead", "operationId": "AdminRoles_GetRole", "parameters": [{"name": "adminRoleId", "in": "path", "description": "", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminRoleViewModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminRoleViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminRoleViewModel"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "put": {"tags": ["admin-roles"], "summary": "更新平台运维管理员的角色", "description": "需要权限: Admin:AdminRoleUpdate", "operationId": "AdminRoles_UpdateRole", "parameters": [{"name": "adminRoleId", "in": "path", "description": "要修改的角色的Id", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"description": "新的管理员用户数据", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UpdateAdminRoleCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdateAdminRoleCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateAdminRoleCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateAdminRoleCommand"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "delete": {"tags": ["admin-roles"], "summary": "删除登录的管理账户", "description": "需要权限: Admin:AdminRoleDelete", "operationId": "AdminRoles_DeleteRole", "parameters": [{"name": "adminRoleId", "in": "path", "description": "", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/admin-users": {"get": {"tags": ["admin-users"], "summary": "获取平台运维管理员列表", "description": "需要权限: Admin:AdminUserRead", "operationId": "AdminUsers_GetAdminUsers", "parameters": [{"name": "filter.name", "in": "query", "schema": {"type": "string"}}, {"name": "filter.phoneNumber", "in": "query", "schema": {"type": "string"}}, {"name": "filter.department", "in": "query", "schema": {"type": "string"}}, {"name": "filter.enabled", "in": "query", "schema": {"type": "boolean"}}, {"name": "sort.name", "in": "query", "schema": {"enum": ["createdAt"], "type": "string"}}, {"name": "sort.direction", "in": "query", "schema": {"enum": ["asc", "desc"], "type": "string"}}, {"name": "page.skip", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "page.take", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminUserViewModelPage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminUserViewModelPage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminUserViewModelPage"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "post": {"tags": ["admin-users"], "summary": "添加平台运维管理员", "description": "需要权限: Admin:AdminUserCreate", "operationId": "AdminUsers_CreateAdminUser", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/CreateAdminUserCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreateAdminUserCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateAdminUserCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateAdminUserCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/IdentModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/IdentModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IdentModel"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/admin-users/{adminUserId}": {"get": {"tags": ["admin-users"], "summary": "根据ID获取平台运维管理员", "description": "需要权限: Admin:AdminUserRead", "operationId": "AdminUsers_GetAdminUser", "parameters": [{"name": "adminUserId", "in": "path", "description": "要查询的管理员的对象Id", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AdminUserViewModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AdminUserViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AdminUserViewModel"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "put": {"tags": ["admin-users"], "summary": "更新平台运维管理员", "description": "需要权限: Admin:UserUpdate", "operationId": "AdminUsers_UpdateAdminUser", "parameters": [{"name": "adminUserId", "in": "path", "description": "要修改的管理员用户的Id", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"description": "管理员更新命令", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UpdateAdminUserCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdateAdminUserCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateAdminUserCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateAdminUserCommand"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "delete": {"tags": ["admin-users"], "summary": "删除登录的管理账户", "description": "需要权限: Admin:AdminUserDelete", "operationId": "AdminUsers_DeleteAdminUser", "parameters": [{"name": "adminUserId", "in": "path", "description": "", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/artifact-releases": {"post": {"tags": ["artifact-releases"], "summary": "上传软件制品包", "description": "需要权限: Admin:ReleaseUpload", "operationId": "ArtifactReleases_Create", "parameters": [{"name": "artifactCode", "in": "query", "description": "软件制品代码", "schema": {"type": "string"}}, {"name": "version", "in": "query", "description": "版本号, 例如 1.0.2-beta.1", "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"zipPackage": {"type": "string", "format": "binary"}}}, "encoding": {"zipPackage": {"style": "form"}}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "get": {"tags": ["artifact-releases"], "summary": "根据条件查询软件制品包列表", "description": "需要权限: Admin:ReleaseRead", "operationId": "ArtifactReleases_Search", "parameters": [{"name": "filter.artifactId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "filter.enabled", "in": "query", "schema": {"type": "boolean"}}, {"name": "page.skip", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "page.take", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "sort.name", "in": "query", "schema": {"enum": ["createdAt", "version"], "type": "string"}}, {"name": "sort.direction", "in": "query", "schema": {"enum": ["asc", "desc"], "type": "string"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/artifact-releases/{releaseId}": {"put": {"tags": ["artifact-releases"], "summary": "更新一个软件制品包", "description": "需要权限: Admin:ReleaseUpdate", "operationId": "ArtifactReleases_Update", "parameters": [{"name": "releaseId", "in": "path", "description": "上传的 ReleaseId", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"description": "要更新的内容", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UpdateReleaseCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdateReleaseCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateReleaseCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateReleaseCommand"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "delete": {"tags": ["artifact-releases"], "summary": "删除一个软件制品包", "description": "需要权限: Admin:ReleaseDelete", "operationId": "ArtifactReleases_Delete", "parameters": [{"name": "releaseId", "in": "path", "description": "上传的 ReleaseId", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "get": {"tags": ["artifact-releases"], "summary": "通过ID查询软件制品包", "description": "需要权限: Admin:ReleaseRead", "operationId": "ArtifactReleases_GetById", "parameters": [{"name": "releaseId", "in": "path", "description": "构建ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/artifacts": {"get": {"tags": ["artifacts"], "summary": "获取软件制品列表", "description": "需要权限: Admin:ArtifactRead", "operationId": "Artifacts_GetArtifacts", "parameters": [{"name": "filter.code", "in": "query", "schema": {"type": "string"}}, {"name": "filter.name", "in": "query", "schema": {"type": "string"}}, {"name": "filter.enabled", "in": "query", "schema": {"type": "boolean"}}, {"name": "filter.type", "in": "query", "schema": {"enum": ["software", "firmware"], "type": "string"}}, {"name": "page.skip", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "page.take", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "sort.name", "in": "query", "schema": {"enum": ["name", "createdAt"], "type": "string"}}, {"name": "sort.direction", "in": "query", "schema": {"enum": ["asc", "desc"], "type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ArtifactViewModelPage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ArtifactViewModelPage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ArtifactViewModelPage"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "post": {"tags": ["artifacts"], "summary": "添加新的软件制品", "description": "需要权限: Admin:ArtifactCreate", "operationId": "Artifacts_CreateArtifact", "requestBody": {"description": "新增的实体对象", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/CreateArtifactCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreateArtifactCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateArtifactCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateArtifactCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/IdentModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/IdentModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IdentModel"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/artifacts/{artifactId}": {"get": {"tags": ["artifacts"], "summary": "根据ID获取软件制品记录", "description": "需要权限: Admin:ArtifactRead", "operationId": "Artifacts_GeArtifact", "parameters": [{"name": "artifactId", "in": "path", "description": "要查询的软件制品的对象Id", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ArtifactViewModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ArtifactViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ArtifactViewModel"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "put": {"tags": ["artifacts"], "summary": "更新指定ID的软件制品记录", "description": "需要权限: Admin:ArtifactUpdate", "operationId": "Artifacts_UpdateArtifact", "parameters": [{"name": "artifactId", "in": "path", "description": "要修改的软件制品的Id", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"description": "要更新的软件制品的内容", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UpdateArtifactCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdateArtifactCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateArtifactCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateArtifactCommand"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "delete": {"tags": ["artifacts"], "summary": "删除软件制品", "description": "需要权限: Admin:ArtifactDelete", "operationId": "Artifacts_DeleteArtifact", "parameters": [{"name": "artifactId", "in": "path", "description": "要删除的软件制品的Id", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/artifacts/{type}/{code}/latest-version": {"get": {"tags": ["artifacts"], "summary": "查询要下载软件的最新版本", "operationId": "Artifacts_GetLatestSoftwareVersion", "parameters": [{"name": "type", "in": "path", "description": "查询的制品包的类型", "required": true, "schema": {"enum": ["software", "firmware"], "type": "string"}}, {"name": "code", "in": "path", "description": "软件代码", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ArtifactLatestVersionModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ArtifactLatestVersionModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ArtifactLatestVersionModel"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}}}, "/api/v1/artifacts/software/{softwareCode}/download": {"get": {"tags": ["artifacts"], "summary": "下载软件, 注意: 这个 API 仅能够下载", "operationId": "Artifacts_DownloadSoftware", "parameters": [{"name": "softwareCode", "in": "path", "description": "软件代码", "required": true, "schema": {"type": "string"}}, {"name": "version", "in": "query", "description": "可选的软件版本, 如果不指定版本, 则下载最新版本", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}}}, "/api/v1/artifacts/firmware/{boardModel}/{serialNumber}/download": {"get": {"tags": ["artifacts"], "summary": "下载固件", "operationId": "Artifacts_DownloadFirmware", "parameters": [{"name": "boardModel", "in": "path", "description": "设备型号", "required": true, "schema": {"type": "string"}}, {"name": "serialNumber", "in": "path", "description": "请正确提供序列号, 因为下载的包是按照该序列号设备加密的", "required": true, "schema": {"type": "string"}}, {"name": "version", "in": "query", "description": "固件/软件版本，可选参数, 如果不指定版本, 则下载最新版本", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}}}, "/api/v1/audits": {"get": {"tags": ["audits"], "summary": "查询用户操作的审计日志", "description": "需要权限: Admin:AuditRead", "operationId": "Audits_GetAudits", "parameters": [{"name": "filter.entityType", "in": "query", "schema": {"type": "string"}}, {"name": "filter.entityId", "in": "query", "schema": {"type": "string"}}, {"name": "filter.userId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "filter.client", "in": "query", "schema": {"type": "string"}}, {"name": "filter.operation", "in": "query", "schema": {"type": "string"}}, {"name": "filter.description", "in": "query", "schema": {"type": "string"}}, {"name": "filter.createdTimeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "filter.createdTimeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "selector.includeUserName", "in": "query", "schema": {"type": "boolean"}}, {"name": "sort.name", "in": "query", "schema": {"enum": ["createdAt"], "type": "string"}}, {"name": "sort.direction", "in": "query", "schema": {"enum": ["asc", "desc"], "type": "string"}}, {"name": "page.skip", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "page.take", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AuditViewModelPage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AuditViewModelPage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AuditViewModelPage"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/boards": {"get": {"tags": ["boards"], "summary": "获取许可证列表", "description": "需要权限: Admin:BoardR", "operationId": "Boards_GetBoards", "parameters": [{"name": "filter.model", "in": "query", "schema": {"type": "string"}}, {"name": "filter.serialNumber", "in": "query", "schema": {"type": "string"}}, {"name": "page.skip", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "page.take", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "sort.name", "in": "query", "schema": {"enum": ["createdAt"], "type": "string"}}, {"name": "sort.direction", "in": "query", "schema": {"enum": ["asc", "desc"], "type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BoardViewModelPage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BoardViewModelPage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BoardViewModelPage"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "post": {"tags": ["boards"], "summary": "激活 Board", "description": "需要权限: Authorize Policy=[], Role=[]", "operationId": "Boards_ActivateBoard", "requestBody": {"description": "Board更新命令", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ActivateBoardCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ActivateBoardCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ActivateBoardCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ActivateBoardCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ActivateResponseModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ActivateResponseModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ActivateResponseModel"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/boards/{boardId}": {"get": {"tags": ["boards"], "summary": "根据ID获取许可证", "description": "需要权限: Admin:BoardR", "operationId": "Boards_GeBoard", "parameters": [{"name": "boardId", "in": "path", "description": "要查询的Board的对象Id", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BoardViewModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BoardViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BoardViewModel"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/dashboards": {"get": {"tags": ["dashboards"], "summary": "获取Dashboard列表", "description": "需要权限: Admin:DashboardRead", "operationId": "Dashboards_GetDashboards", "parameters": [{"name": "filter.name", "in": "query", "schema": {"type": "string"}}, {"name": "filter.type", "in": "query", "schema": {"enum": ["html"], "type": "string"}}, {"name": "filter.enabled", "in": "query", "schema": {"type": "boolean"}}, {"name": "page.skip", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "page.take", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "sort.name", "in": "query", "schema": {"enum": ["createdAt"], "type": "string"}}, {"name": "sort.direction", "in": "query", "schema": {"enum": ["asc", "desc"], "type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DashboardViewModelPage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DashboardViewModelPage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DashboardViewModelPage"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "post": {"tags": ["dashboards"], "summary": "添加Dashboard", "description": "需要权限: Admin:DashboardCreate", "operationId": "Dashboards_CreateDashboard", "requestBody": {"description": "新增Dashboard的实体对象", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/CreateDashboardCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreateDashboardCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateDashboardCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateDashboardCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/IdentModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/IdentModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IdentModel"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/dashboards/{dashboardId}": {"get": {"tags": ["dashboards"], "summary": "根据ID获取Dashboard", "description": "需要权限: Admin:DashboardRead", "operationId": "Dashboards_GeDashboard", "parameters": [{"name": "dashboardId", "in": "path", "description": "要查询的Dashboard的对象Id", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DashboardViewModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DashboardViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DashboardViewModel"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "put": {"tags": ["dashboards"], "summary": "更新Dashboard", "description": "需要权限: Admin:DashboardUpdate", "operationId": "Dashboards_UpdateDashboard", "parameters": [{"name": "dashboardId", "in": "path", "description": "要修改的Dashboard的Id", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"description": "Dashboard更新命令", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UpdateDashboardCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdateDashboardCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateDashboardCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateDashboardCommand"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "delete": {"tags": ["dashboards"], "summary": "删除Dashboard", "description": "需要权限: Admin:DashboardDelete", "operationId": "Dashboards_DeleteDashboard", "parameters": [{"name": "dashboardId", "in": "path", "description": "要删除的Dashboard的Id", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/dashboards/{dashboardId}/package": {"post": {"tags": ["dashboards"], "summary": "上传仪表盘部署文件包", "description": "需要权限: Admin:DashboardUpdate", "operationId": "Dashboards_UploadDashboard", "parameters": [{"name": "dashboardId", "in": "path", "description": "", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "get": {"tags": ["dashboards"], "summary": "下载仪表盘部署文件包", "description": "需要权限: Admin:DashboardRead", "operationId": "Dashboards_DownloadDashboard", "parameters": [{"name": "dashboardId", "in": "path", "description": "", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "delete": {"tags": ["dashboards"], "summary": "删除仪表盘部署文件包", "description": "需要权限: Admin:DashboardUpdate", "operationId": "Dashboards_DeleteDashboardPackage", "parameters": [{"name": "dashboardId", "in": "path", "description": "", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/home-redirection/api/v1/home-redirection": {"post": {"tags": ["home-redirection"], "summary": "更新首页重定向配置", "description": "需要权限: Admin:HomePageRedirection", "operationId": "HomeRedirection_UpdateRedirection", "requestBody": {"description": "更新命令", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UpdateHomePageRedirectionCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdateHomePageRedirectionCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateHomePageRedirectionCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateHomePageRedirectionCommand"}}}, "required": true}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "get": {"tags": ["home-redirection"], "summary": "获取当前首页重定向配置", "operationId": "HomeRedirection_GetRedirection", "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}}}, "/api/v1/module-builds/{buildId}": {"delete": {"tags": ["module-builds"], "summary": "删除一个发布包", "description": "需要权限: Admin:PluginDelete", "operationId": "ModuleBuilds_Delete", "parameters": [{"name": "buildId", "in": "path", "description": "上传的 BuildId", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "get": {"tags": ["module-builds"], "summary": "通过ID查询构建信息", "description": "需要权限: Admin:PluginRead", "operationId": "ModuleBuilds_GetById", "parameters": [{"name": "buildId", "in": "path", "description": "构建ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/module-builds": {"get": {"tags": ["module-builds"], "summary": "根据条件查询构建列表", "description": "需要权限: Admin:PluginRead", "operationId": "ModuleBuilds_Search", "parameters": [{"name": "filter.moduleId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "filter.enabled", "in": "query", "schema": {"type": "boolean"}}, {"name": "page.skip", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "page.take", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "sort.name", "in": "query", "schema": {"enum": ["createdAt", "version"], "type": "string"}}, {"name": "sort.direction", "in": "query", "schema": {"enum": ["asc", "desc"], "type": "string"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/modules": {"get": {"tags": ["modules"], "summary": "查询模块", "description": "需要权限: Admin:ModuleRead", "operationId": "Modules_GetModules", "parameters": [{"name": "filter.code", "in": "query", "schema": {"type": "string"}}, {"name": "filter.name", "in": "query", "schema": {"type": "string"}}, {"name": "filter.host", "in": "query", "schema": {"enum": ["dgWin"], "type": "string"}}, {"name": "filter.enabled", "in": "query", "schema": {"type": "boolean"}}, {"name": "page.skip", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "page.take", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "sort.name", "in": "query", "schema": {"enum": ["createdAt"], "type": "string"}}, {"name": "sort.direction", "in": "query", "schema": {"enum": ["asc", "desc"], "type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ModuleViewModelPage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ModuleViewModelPage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ModuleViewModelPage"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "post": {"tags": ["modules"], "summary": "创建新模块", "description": "需要权限: Admin:ModuleCreate", "operationId": "Modules_Create", "requestBody": {"description": "创建模块的命令", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/CreateModuleCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreateModuleCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateModuleCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateModuleCommand"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/modules/{moduleId}": {"get": {"tags": ["modules"], "summary": "查询单个模块信息", "description": "需要权限: Admin:ModuleRead", "operationId": "Modules_GetModule", "parameters": [{"name": "moduleId", "in": "path", "description": "模块的记录Id", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "delete": {"tags": ["modules"], "summary": "删除一个模块", "description": "注意: 整个模块将被删除", "operationId": "Mo<PERSON>les_Delete", "parameters": [{"name": "moduleId", "in": "path", "description": "模块的记录Id", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/modules/{code}/upload": {"post": {"tags": ["modules"], "summary": "创建（或者更新）发行包", "description": "需要权限: Admin:ModuleUpload", "operationId": "Modules_UploadBuild", "parameters": [{"name": "code", "in": "path", "description": "模块的代码", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"zipPackage": {"type": "string", "format": "binary"}}}, "encoding": {"zipPackage": {"style": "form"}}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/modules/{code}/{version}/download": {"get": {"tags": ["modules"], "summary": "下载模块", "description": "需要权限: Authorize Policy=[], Role=[]", "operationId": "Modules_Download", "parameters": [{"name": "code", "in": "path", "description": "模块代码或者Id", "required": true, "schema": {"type": "string"}}, {"name": "version", "in": "path", "description": "指定版本，如果指定为 latest, 则下载最新版本", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/plugin-bundles": {"get": {"tags": ["plugin-bundles"], "summary": "根据条件查询插件包列表", "description": "需要权限: Admin:PluginRead", "operationId": "PluginBundles_Search", "parameters": [{"name": "filter.pluginId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "filter.enabled", "in": "query", "schema": {"type": "boolean"}}, {"name": "page.skip", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "page.take", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "sort.name", "in": "query", "schema": {"enum": ["createdAt", "version"], "type": "string"}}, {"name": "sort.direction", "in": "query", "schema": {"enum": ["asc", "desc"], "type": "string"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/plugin-bundles/{bundleId}": {"get": {"tags": ["plugin-bundles"], "summary": "通过ID查询插件包", "description": "需要权限: Admin:PluginRead", "operationId": "PluginBundles_GetById", "parameters": [{"name": "bundleId", "in": "path", "description": "构建ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "delete": {"tags": ["plugin-bundles"], "summary": "删除一个插件包", "description": "需要权限: Admin:PluginDelete", "operationId": "PluginBundles_Delete", "parameters": [{"name": "bundleId", "in": "path", "description": "上传的 BundleId", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/plugin-categories": {"get": {"tags": ["plugin-categories"], "summary": "获取插件分类列表", "description": "需要权限: Admin:PluginCategoryRead", "operationId": "PluginCategories_GetCategories", "parameters": [{"name": "filter.name", "in": "query", "schema": {"type": "string"}}, {"name": "filter.parentId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "sort.name", "in": "query", "schema": {"enum": ["name", "position", "createdAt"], "type": "string"}}, {"name": "sort.direction", "in": "query", "schema": {"enum": ["asc", "desc"], "type": "string"}}, {"name": "page.skip", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "page.take", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PluginCategoryViewModelPage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PluginCategoryViewModelPage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PluginCategoryViewModelPage"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "post": {"tags": ["plugin-categories"], "summary": "添加插件分类", "description": "需要权限: Admin:PluginCategoryCreate", "operationId": "PluginCategories_CreateCategory", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/CreatePluginCategoryCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreatePluginCategoryCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreatePluginCategoryCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreatePluginCategoryCommand"}}}}, "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/IdentModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/IdentModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IdentModel"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/plugin-categories/{categoryId}": {"get": {"tags": ["plugin-categories"], "summary": "根据ID获取插件分类", "description": "需要权限: Admin:PluginCategoryRead", "operationId": "PluginCategories_GetCategory", "parameters": [{"name": "categoryId", "in": "path", "description": "要查询的插件分类Id", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PluginCategoryViewModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PluginCategoryViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PluginCategoryViewModel"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "put": {"tags": ["plugin-categories"], "summary": "更新插件分类", "description": "需要权限: Admin:PluginCategoryUpdate", "operationId": "PluginCategories_UpdateCategory", "parameters": [{"name": "categoryId", "in": "path", "description": "要修改的插件分类的Id", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"description": "插件分类更新命令", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UpdatePluginCategoryCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdatePluginCategoryCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdatePluginCategoryCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdatePluginCategoryCommand"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "delete": {"tags": ["plugin-categories"], "summary": "删除插件分类", "description": "需要权限: Admin:PluginCategoryDelete", "operationId": "PluginCategories_DeleteCategory", "parameters": [{"name": "categoryId", "in": "path", "description": "", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/plugin-db/{host}/tree": {"get": {"tags": ["plugin-db"], "summary": "获取插件分类树, 这个函数用来返回提供给 WPF 客户端使用, 包含所有支持的产品", "operationId": "PluginDb_GetPluginTree", "parameters": [{"name": "host", "in": "path", "description": "平台类型", "required": true, "schema": {"enum": ["dgWin"], "type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TreeNode"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TreeNode"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TreeNode"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}}}, "/api/v1/plugin-db/{host}/meta": {"get": {"tags": ["plugin-db"], "summary": "获取插件数据库MD5哈希值", "operationId": "PluginDb_GetPluginDbHash", "parameters": [{"name": "host", "in": "path", "description": "平台类型", "required": true, "schema": {"enum": ["dgWin"], "type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PluginDbMetaModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PluginDbMetaModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PluginDbMetaModel"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}}}, "/api/v1/plugin-db/{host}": {"get": {"tags": ["plugin-db"], "summary": "下载插件数据库", "operationId": "PluginDb_DownloadPluginDb", "parameters": [{"name": "host", "in": "path", "description": "平台类型", "required": true, "schema": {"enum": ["dgWin"], "type": "string"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}}}, "/api/v1/plugins": {"get": {"tags": ["plugins"], "summary": "查询插件", "description": "需要权限: Admin:PluginRead", "operationId": "Plugins_GetPlugins", "parameters": [{"name": "filter.code", "in": "query", "schema": {"type": "string"}}, {"name": "filter.pluginCategoryId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "filter.name", "in": "query", "schema": {"type": "string"}}, {"name": "filter.enabled", "in": "query", "schema": {"type": "boolean"}}, {"name": "filter.host", "in": "query", "schema": {"enum": ["dgWin"], "type": "string"}}, {"name": "page.skip", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "page.take", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "sort.name", "in": "query", "schema": {"enum": ["code", "name", "createdAt"], "type": "string"}}, {"name": "sort.direction", "in": "query", "schema": {"enum": ["asc", "desc"], "type": "string"}}], "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PluginViewModelPage"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PluginViewModelPage"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PluginViewModelPage"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "post": {"tags": ["plugins"], "summary": "创建插件", "description": "需要权限: Admin:PluginCreate", "operationId": "Plugins_Create", "requestBody": {"description": "创建插件命令", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/CreatePluginCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CreatePluginCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreatePluginCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreatePluginCommand"}}}}, "responses": {"200": {"description": "Success"}, "201": {"description": "Created", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/IdentModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/IdentModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IdentModel"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/plugins/{pluginId}": {"get": {"tags": ["plugins"], "summary": "查询单个插件信息", "description": "需要权限: Admin:PluginRead", "operationId": "Plugins_GetPlugin", "parameters": [{"name": "pluginId", "in": "path", "description": "插件的记录Id", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "put": {"tags": ["plugins"], "summary": "更新插件", "description": "需要权限: Admin:PluginUpdate", "operationId": "Plugins_Update", "parameters": [{"name": "pluginId", "in": "path", "description": "", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UpdatePluginCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdatePluginCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdatePluginCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdatePluginCommand"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "delete": {"tags": ["plugins"], "summary": "删除一个插件", "description": "注意: 整个插件将被删除", "operationId": "Plugins_Delete", "parameters": [{"name": "pluginId", "in": "path", "description": "插件的记录Id", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/plugins/{code}/upload": {"post": {"tags": ["plugins"], "summary": "上传插件包", "description": "需要权限: Admin:PluginUpload", "operationId": "Plugins_Upload", "parameters": [{"name": "code", "in": "path", "description": "插接件代码", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"zipPackage": {"type": "string", "format": "binary"}}}, "encoding": {"zipPackage": {"style": "form"}}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/plugins/{code}/{version}/download": {"get": {"tags": ["plugins"], "summary": "使用插件的 code 以及版本号 下载插件", "description": "插件下载的权限由插件不同而不同", "operationId": "Plugins_DownloadPlugin", "parameters": [{"name": "code", "in": "path", "description": "The code representing the plugin to be downloaded.", "required": true, "schema": {"type": "string"}}, {"name": "version", "in": "path", "description": "The specific version of the plugin to download.", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/signature/root-certificate": {"get": {"tags": ["signature"], "summary": "获取根证书公钥", "description": "需要权限: Authorize Policy=[], Role=[]", "operationId": "Signature_GetRootCertificate", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RootCertificateResponseModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RootCertificateResponseModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RootCertificateResponseModel"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/signature/sign-certificate": {"get": {"tags": ["signature"], "summary": "获取当前签名证书信息", "description": "需要权限: Authorize Policy=[], Role=[]", "operationId": "Signature_GetSignCertificate", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/SignCertificateResponseModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/SignCertificateResponseModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SignCertificateResponseModel"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden"}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/signature/regenerate-certificate": {"post": {"tags": ["signature"], "summary": "强制重新生成签名证书（仅管理员）", "description": "需要权限: Authorize Policy=[], Role=[], Admin:CertsUpdate", "operationId": "Signature_RegenerateCertificate", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/OperationResultModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/OperationResultModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OperationResultModel"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "500": {"description": "Server Error", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/system": {"get": {"tags": ["system"], "summary": "获取服务器版本", "operationId": "System_GetServerVersion", "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}}}, "/api/v1/system/changelog": {"get": {"tags": ["system"], "summary": "获取更新日志", "operationId": "System_GetChangelog", "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}}}, "/api/v1/user-plugin-fetch-audits/{id}": {"get": {"tags": ["user-plugin-fetch-audits"], "summary": "根据ID获取插件获取审计记录", "description": "需要权限: Admin:AuditRead", "operationId": "UserPluginFetchAudits_GetUserPluginFetchAudit", "parameters": [{"name": "id", "in": "path", "description": "审计记录ID", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/user-plugin-fetch-audits": {"get": {"tags": ["user-plugin-fetch-audits"], "summary": "查询插件获取审计记录列表", "description": "需要权限: Admin:AuditRead", "operationId": "UserPluginFetchAudits_GetUserPluginFetchAudits", "parameters": [{"name": "filter.userId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "filter.machineSerialNumber", "in": "query", "schema": {"type": "string"}}, {"name": "filter.updatedTimeFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "filter.updatedTimeTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "selector", "in": "query", "schema": {"$ref": "#/components/schemas/UserPluginFetchAuditQuerySelector"}}, {"name": "sort.name", "in": "query", "schema": {"enum": ["createdAt", "updatedAt", "count"], "type": "string"}}, {"name": "sort.direction", "in": "query", "schema": {"enum": ["asc", "desc"], "type": "string"}}, {"name": "page.skip", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "page.take", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/user-plugin-fetch-audits/statistics/user-machine-count": {"get": {"tags": ["user-plugin-fetch-audits"], "summary": "统计用户激活的机器数量，按激活机器数量降序排列", "description": "需要权限: Admin:AuditRead", "operationId": "UserPluginFetchAudits_GetUserMachineCountStatistics", "parameters": [{"name": "topCount", "in": "query", "description": "返回前N个用户，默认10个", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/user-plugin-fetch-audits/statistics/machine-user-count": {"get": {"tags": ["user-plugin-fetch-audits"], "summary": "统计机器激活的用户数量，按激活用户数量降序排列", "description": "需要权限: Admin:AuditRead", "operationId": "UserPluginFetchAudits_GetMachineUserCountStatistics", "parameters": [{"name": "topCount", "in": "query", "description": "返回前N台机器，默认10台", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/user-plugins": {"get": {"tags": ["user-plugins"], "summary": "获取指定查询条件的用户插件数据", "description": "需要权限: Admin:UserPluginRead", "operationId": "UserPlugins_GetUserPlugins", "parameters": [{"name": "filter.userId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "filter.pluginId", "in": "query", "schema": {"type": "string", "format": "uuid"}}, {"name": "filter.valid", "in": "query", "schema": {"type": "boolean"}}, {"name": "filter.host", "in": "query", "schema": {"enum": ["dgWin"], "type": "string"}}, {"name": "page.skip", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "page.take", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "sort.name", "in": "query", "schema": {"enum": ["createdAt"], "type": "string"}}, {"name": "sort.direction", "in": "query", "schema": {"enum": ["asc", "desc"], "type": "string"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "post": {"tags": ["user-plugins"], "summary": "批量创建用户插件", "description": "需要权限: Admin:UserPluginCreate", "operationId": "UserPlugins_BatchCreateUserPlugin", "requestBody": {"description": "包含创建用户插件的详细参数", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/ApproveUserPluginCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApproveUserPluginCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApproveUserPluginCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ApproveUserPluginCommand"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/user-plugins/me": {"get": {"tags": ["user-plugins"], "summary": "获取我的（登录用户）的用户插件信息，包括用户拥有的所有有效的 pluginCode 及其有效期，用于签名返回", "description": "需要权限: Authorize Policy=[], Role=[]", "operationId": "UserPlugins_GetMyUserPlugins", "parameters": [{"name": "host", "in": "query", "description": "主机的类型", "schema": {"enum": ["dgWin"], "type": "string"}}, {"name": "serialNumber", "in": "query", "description": "在请求时, 附加上主机类型对应的序列号, 例如计算机序列号", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/user-plugins/{userPluginId}": {"delete": {"tags": ["user-plugins"], "summary": "删除指定Id的用户插件", "description": "需要权限: Admin:UserPluginDelete", "operationId": "UserPlugins_DeleteUserPlugin", "parameters": [{"name": "userPluginId", "in": "path", "description": "用户插件Id", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/users/login": {"get": {"tags": ["users"], "summary": "获取登录用户的信息", "description": "需要权限: Authorize Policy=[], Role=[]", "operationId": "Users_GetLoginUser", "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/users/rebinding": {"put": {"tags": ["users"], "summary": "用户重新绑定", "description": "需要权限: Authorize Policy=[], Role=[]", "operationId": "Users_UpdateLoginUserBinding", "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/users/screenName": {"put": {"tags": ["users"], "summary": "用户修改昵称", "description": "需要权限: Authorize Policy=[], Role=[]", "operationId": "Users_UpdateUserScreenName", "parameters": [{"name": "screenName", "in": "query", "description": "要修改的用户的昵称", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/users": {"get": {"tags": ["users"], "summary": "获取指定条件的 运维用户", "description": "需要权限: Admin:UserRead", "operationId": "Users_GetUsers", "parameters": [{"name": "filter.screenName", "in": "query", "schema": {"type": "string"}}, {"name": "filter.phoneNumber", "in": "query", "schema": {"type": "string"}}, {"name": "page.skip", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "page.take", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "sort.name", "in": "query", "schema": {"enum": ["createdAt"], "type": "string"}}, {"name": "sort.direction", "in": "query", "schema": {"enum": ["asc", "desc"], "type": "string"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/v1/users/{userId}": {"get": {"tags": ["users"], "description": "需要权限: Admin:UserRead", "operationId": "Users_GetUser", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "delete": {"tags": ["users"], "summary": "删除指定Id的用户", "description": "需要权限: Admin:UserDelete", "operationId": "Users_DeleteUser", "parameters": [{"name": "userId", "in": "path", "description": "用户Id", "required": true, "schema": {"type": "string", "format": "uuid"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}, "put": {"tags": ["users"], "summary": "更新用户启动状态", "description": "需要权限: Admin:UserUpdate", "operationId": "Users_UpdateUser", "parameters": [{"name": "userId", "in": "path", "description": "要修改的用户的Id", "required": true, "schema": {"type": "string", "format": "uuid"}}], "requestBody": {"description": "用户更新命令", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UpdateUserCommand"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserCommand"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserCommand"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserCommand"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/web-api-test": {"get": {"tags": ["web-api-test"], "summary": "Get action test", "operationId": "WebApiTest_GetList", "parameters": [{"name": "name", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "type", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}}, "post": {"tags": ["web-api-test"], "summary": "POST action test, the model of submit in the body", "operationId": "WebApiTest_TestPost", "parameters": [{"name": "x-requestid", "in": "header", "description": "", "schema": {"type": "string"}}], "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/PostModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PostModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PostModel"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PostModel"}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}}, "patch": {"tags": ["web-api-test"], "summary": "PATCH model test, the model of submit in the form", "operationId": "WebApiTest_TestPatch", "requestBody": {"description": "", "content": {"application/json-patch+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Operation"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Operation"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Operation"}}}, "application/*+json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Operation"}}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}}, "delete": {"tags": ["web-api-test"], "summary": "DELETE action test", "operationId": "WebApiTest_TestDelete", "parameters": [{"name": "message", "in": "query", "description": "", "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}}}, "/api/web-api-test/{id}": {"get": {"tags": ["web-api-test"], "summary": "Get action test", "operationId": "WebApiTest_GetId", "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "name", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "type", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}}}, "/api/web-api-test/authorize": {"get": {"tags": ["web-api-test"], "summary": "GET action test, authorize required", "description": "需要权限: Admin:AdminUserCreate", "operationId": "WebApiTest_Authorize", "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "401": {"description": "Unauthorized"}, "403": {"description": "Forbidden", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}, "security": [{"OAuth2": ["read_access"]}]}}, "/api/web-api-test/file": {"post": {"tags": ["web-api-test"], "summary": "Upload file test, by POST action", "operationId": "WebApiTest_TestUploadFile", "parameters": [{"name": "message", "in": "query", "description": "additional message", "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}, "encoding": {"file": {"style": "form"}}}}}, "responses": {"200": {"description": "Success"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}}}, "/api/web-api-test/exception": {"get": {"tags": ["web-api-test"], "summary": "Exception test", "operationId": "WebApiTest_TestException", "responses": {"200": {"description": "Success", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/IAsyncResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/IAsyncResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/IAsyncResult"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorMessageModel"}}}}}}}}, "components": {"schemas": {"ActivateBoardCommand": {"type": "object", "properties": {"model": {"type": "string", "nullable": true}, "serialNumber": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ActivateResponseModel": {"type": "object", "properties": {"productKey": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AdminRoleQuerySelector": {"type": "object", "additionalProperties": false}, "AdminRoleViewModel": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "sortNumber": {"type": "string", "nullable": true}, "permissions": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}, "description": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "AdminRoleViewModelPage": {"type": "object", "properties": {"skip": {"type": "integer", "format": "int32"}, "take": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/AdminRoleViewModel"}, "nullable": true}}, "additionalProperties": false}, "AdminUserRoleViewModel": {"type": "object", "properties": {"roleId": {"type": "string", "format": "uuid"}, "roleName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AdminUserViewModel": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "realName": {"type": "string", "nullable": true}, "avatarUrl": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "department": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "userId": {"type": "string", "format": "uuid", "nullable": true}, "userScreenName": {"type": "string", "nullable": true}, "roles": {"type": "array", "items": {"$ref": "#/components/schemas/AdminUserRoleViewModel"}, "nullable": true}, "enabled": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "AdminUserViewModelPage": {"type": "object", "properties": {"skip": {"type": "integer", "format": "int32"}, "take": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/AdminUserViewModel"}, "nullable": true}}, "additionalProperties": false}, "ApproveUserPluginCommand": {"type": "object", "properties": {"userPluginItems": {"type": "array", "items": {"$ref": "#/components/schemas/UserPluginItem"}, "nullable": true}, "note": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ArtifactLatestVersionModel": {"type": "object", "properties": {"code": {"type": "string", "description": "Gets or sets the code identifier of the artifact.", "nullable": true}, "latestVersion": {"type": "string", "description": "Gets or sets the latest version number of the artifact.", "nullable": true}, "updatedAt": {"type": "string", "description": "Gets or sets the date and time when the artifact was last updated.", "format": "date-time"}}, "additionalProperties": false, "description": "Represents the latest version information of an artifact."}, "ArtifactViewModel": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "type": {"enum": ["software", "firmware"], "type": "string"}, "code": {"type": "string", "nullable": true}, "permission": {"enum": ["anonymous", "user", "admin"], "type": "string"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "latestVersion": {"type": "string", "nullable": true}, "enabled": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "createdBy": {"type": "string", "format": "uuid"}, "createdByName": {"type": "string", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}, "updatedBy": {"type": "string", "format": "uuid", "nullable": true}, "updatedByName": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ArtifactViewModelPage": {"type": "object", "properties": {"skip": {"type": "integer", "format": "int32"}, "take": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/ArtifactViewModel"}, "nullable": true}}, "additionalProperties": false}, "AuditViewModel": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "entityType": {"type": "string", "nullable": true}, "entityId": {"nullable": true}, "entityName": {"type": "string", "nullable": true}, "userId": {"type": "string", "format": "uuid", "nullable": true}, "userName": {"type": "string", "nullable": true}, "screenName": {"type": "string", "nullable": true}, "client": {"type": "string", "nullable": true}, "operation": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "details": {"nullable": true}, "status": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "AuditViewModelPage": {"type": "object", "properties": {"skip": {"type": "integer", "format": "int32"}, "take": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/AuditViewModel"}, "nullable": true}}, "additionalProperties": false}, "BoardViewModel": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "model": {"type": "string", "nullable": true}, "agencyName": {"type": "string", "nullable": true}, "serialNumber": {"type": "string", "nullable": true}, "productKey": {"type": "string", "nullable": true}, "activatedCount": {"type": "integer", "format": "int32"}, "ownedBy": {"type": "string", "format": "uuid", "nullable": true}, "ownedByName": {"type": "string", "nullable": true}, "createdBy": {"type": "string", "format": "uuid"}, "createdByName": {"type": "string", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedBy": {"type": "string", "format": "uuid", "nullable": true}, "updatedByName": {"type": "string", "nullable": true}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "BoardViewModelPage": {"type": "object", "properties": {"skip": {"type": "integer", "format": "int32"}, "take": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/BoardViewModel"}, "nullable": true}}, "additionalProperties": false}, "CreateAdminRoleCommand": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "sortNumber": {"type": "string", "nullable": true}, "permissions": {"type": "array", "items": {"type": "string"}, "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateAdminUserCommand": {"type": "object", "properties": {"phoneNumber": {"type": "string", "nullable": true}, "realName": {"type": "string", "nullable": true}, "department": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "enabled": {"type": "boolean"}, "roleIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}}, "additionalProperties": false}, "CreateArtifactCommand": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "type": {"enum": ["software", "firmware"], "type": "string"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "enabled": {"type": "boolean"}, "permission": {"enum": ["anonymous", "user", "admin"], "type": "string"}}, "additionalProperties": false}, "CreateDashboardCommand": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "type": {"enum": ["html"], "type": "string"}, "description": {"type": "string", "nullable": true}, "enableRootPathAccess": {"enum": ["desktop", "mobile"], "type": "string", "nullable": true}, "path": {"type": "string", "nullable": true}, "indexPage": {"type": "string", "nullable": true}, "enabled": {"type": "boolean"}}, "additionalProperties": false}, "CreateModuleCommand": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "host": {"enum": ["dgWin"], "type": "string"}, "enabled": {"type": "boolean"}}, "additionalProperties": false}, "CreatePluginCategoryCommand": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "parentId": {"type": "string", "format": "uuid", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreatePluginCommand": {"type": "object", "properties": {"code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "pluginCategoryId": {"type": "string", "format": "uuid", "nullable": true}, "docsUrl": {"type": "string", "nullable": true}, "overviewUrl": {"type": "string", "nullable": true}, "host": {"enum": ["dgWin"], "type": "string"}, "enabled": {"type": "boolean"}, "isFree": {"type": "boolean"}}, "additionalProperties": false}, "DashboardViewModel": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "type": {"enum": ["html"], "type": "string"}, "description": {"type": "string", "nullable": true}, "enableRootPathAccess": {"enum": ["desktop", "mobile"], "type": "string", "nullable": true}, "path": {"type": "string", "nullable": true}, "indexPage": {"type": "string", "nullable": true}, "fileSize": {"type": "integer", "format": "int32", "nullable": true}, "fileUploadTime": {"type": "string", "format": "date-time", "nullable": true}, "enabled": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "DashboardViewModelPage": {"type": "object", "properties": {"skip": {"type": "integer", "format": "int32"}, "take": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/DashboardViewModel"}, "nullable": true}}, "additionalProperties": false}, "ErrorMessageModel": {"type": "object", "properties": {"message": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "details": {"nullable": true}, "requestId": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}}, "additionalProperties": false}, "IAsyncResult": {"type": "object", "properties": {"isCompleted": {"type": "boolean", "readOnly": true}, "asyncWaitHandle": {"$ref": "#/components/schemas/WaitHandle"}, "asyncState": {"nullable": true, "readOnly": true}, "completedSynchronously": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "IdentModel": {"type": "object", "properties": {"id": {"description": "创建的对象Id", "nullable": true}}, "additionalProperties": false, "description": "通过 HTTP POST 提交创建成功的返回结果"}, "ModuleViewModel": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "host": {"enum": ["dgWin"], "type": "string"}, "latestVersion": {"type": "string", "nullable": true}, "enabled": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "ModuleViewModelPage": {"type": "object", "properties": {"skip": {"type": "integer", "format": "int32"}, "take": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/ModuleViewModel"}, "nullable": true}}, "additionalProperties": false}, "Operation": {"type": "object", "properties": {"value": {"nullable": true}, "path": {"type": "string", "nullable": true}, "op": {"type": "string", "nullable": true}, "from": {"type": "string", "nullable": true}}, "additionalProperties": false}, "OperationResultModel": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功"}, "message": {"type": "string", "description": "操作结果消息", "nullable": true}, "timestamp": {"type": "string", "description": "操作时间戳", "format": "date-time"}, "operationType": {"type": "string", "description": "操作类型", "nullable": true}}, "additionalProperties": false, "description": "操作结果响应模型"}, "PluginCategoryViewModel": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "parentId": {"type": "string", "format": "uuid", "nullable": true}, "numberOfPlugins": {"type": "integer", "format": "int32"}, "createdAt": {"type": "string", "format": "date-time"}, "position": {"type": "number", "format": "double"}}, "additionalProperties": false}, "PluginCategoryViewModelPage": {"type": "object", "properties": {"skip": {"type": "integer", "format": "int32"}, "take": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/PluginCategoryViewModel"}, "nullable": true}}, "additionalProperties": false}, "PluginDbMetaModel": {"type": "object", "properties": {"fileName": {"type": "string", "nullable": true}, "size": {"type": "integer", "format": "int32"}, "md5": {"type": "string", "nullable": true}, "sha1": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PluginViewModel": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "code": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "pluginCategoryId": {"type": "string", "format": "uuid", "nullable": true}, "pluginCategoryName": {"type": "string", "nullable": true}, "docsUrl": {"type": "string", "nullable": true}, "overviewUrl": {"type": "string", "nullable": true}, "host": {"enum": ["dgWin"], "type": "string"}, "latestVersion": {"type": "string", "nullable": true}, "enabled": {"type": "boolean"}, "isFree": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "PluginViewModelPage": {"type": "object", "properties": {"skip": {"type": "integer", "format": "int32"}, "take": {"type": "integer", "format": "int32"}, "total": {"type": "integer", "format": "int32"}, "items": {"type": "array", "items": {"$ref": "#/components/schemas/PluginViewModel"}, "nullable": true}}, "additionalProperties": false}, "PostModel": {"type": "object", "properties": {"name": {"type": "string", "description": "名称", "nullable": true}, "message": {"type": "string", "description": "消息", "nullable": true}, "createTime": {"type": "string", "description": "创建时间", "format": "date-time"}}, "additionalProperties": false, "description": "测试提交的数据模型"}, "RootCertificateResponseModel": {"type": "object", "properties": {"publicKey": {"type": "string", "nullable": true}, "issuer": {"type": "string", "nullable": true}, "validFrom": {"type": "string", "format": "date-time"}, "validTo": {"type": "string", "format": "date-time"}, "fingerprint": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SafeWaitHandle": {"type": "object", "properties": {"isInvalid": {"type": "boolean", "readOnly": true}, "isClosed": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "SignCertificateResponseModel": {"type": "object", "properties": {"publicKey": {"type": "string", "nullable": true}, "issuer": {"type": "string", "nullable": true}, "subject": {"type": "string", "nullable": true}, "validFrom": {"type": "string", "format": "date-time"}, "validTo": {"type": "string", "format": "date-time"}, "fingerprint": {"type": "string", "nullable": true}, "serialNumber": {"type": "string", "nullable": true}, "certificateType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TreeNode": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "type": {"enum": ["category", "plugin"], "type": "string", "readOnly": true}, "tags": {"type": "array", "items": {"type": "string"}, "nullable": true}, "brief": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateAdminRoleCommand": {"type": "object", "properties": {"id": {"type": "string", "format": "uuid"}, "name": {"type": "string", "nullable": true}, "sortNumber": {"type": "string", "nullable": true}, "permissions": {"type": "array", "items": {"type": "string"}, "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateAdminUserCommand": {"type": "object", "properties": {"phoneNumber": {"type": "string", "nullable": true}, "realName": {"type": "string", "nullable": true}, "department": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "enabled": {"type": "boolean"}, "roleIds": {"type": "array", "items": {"type": "string", "format": "uuid"}, "nullable": true}}, "additionalProperties": false}, "UpdateArtifactCommand": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "type": {"enum": ["software", "firmware"], "type": "string"}, "description": {"type": "string", "nullable": true}, "permission": {"enum": ["anonymous", "user", "admin"], "type": "string"}, "enabled": {"type": "boolean"}}, "additionalProperties": false}, "UpdateDashboardCommand": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "type": {"enum": ["html"], "type": "string"}, "description": {"type": "string", "nullable": true}, "enableRootPathAccess": {"enum": ["desktop", "mobile"], "type": "string", "nullable": true}, "path": {"type": "string", "nullable": true}, "indexPage": {"type": "string", "nullable": true}, "enabled": {"type": "boolean"}}, "additionalProperties": false}, "UpdateHomePageRedirectionCommand": {"type": "object", "properties": {"redirectUrl": {"type": "string", "nullable": true}, "enabled": {"type": "boolean"}}, "additionalProperties": false}, "UpdatePluginCategoryCommand": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "parentId": {"type": "string", "format": "uuid", "nullable": true}, "description": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdatePluginCommand": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "pluginCategoryId": {"type": "string", "format": "uuid", "nullable": true}, "docsUrl": {"type": "string", "nullable": true}, "overviewUrl": {"type": "string", "nullable": true}, "host": {"enum": ["dgWin"], "type": "string", "nullable": true}, "enabled": {"type": "boolean"}, "isFree": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "UpdateReleaseCommand": {"type": "object", "properties": {"enabled": {"type": "boolean"}}, "additionalProperties": false}, "UpdateUserCommand": {"type": "object", "properties": {"screenName": {"type": "string", "nullable": true}, "enabled": {"type": "boolean"}}, "additionalProperties": false}, "UserPluginFetchAuditQuerySelector": {"type": "object", "additionalProperties": false}, "UserPluginItem": {"type": "object", "properties": {"userId": {"type": "string", "format": "uuid"}, "pluginId": {"type": "string", "format": "uuid"}, "validUntil": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "WaitHandle": {"type": "object", "properties": {"handle": {"deprecated": true}, "safeWaitHandle": {"$ref": "#/components/schemas/SafeWaitHandle"}}, "additionalProperties": false}}, "securitySchemes": {"OAuth2": {"type": "oauth2", "flows": {"implicit": {"authorizationUrl": "https://auth.chkfun.com/realms/device-guard/protocol/openid-connect/auth", "scopes": {"read_access": "Device Guard Cloud WebAPI"}}}}}}, "tags": [{"name": "admin-roles", "description": "负责角色管理功能"}, {"name": "admin-users", "description": "平台运维管理员相关的API"}, {"name": "artifact-releases", "description": "软件制品 (Artifact) 包版本管理"}, {"name": "artifacts", "description": "软件制品相关的API"}, {"name": "audits", "description": "用户操作的审计日志"}, {"name": "boards", "description": "激活检测仪设备API"}, {"name": "dashboards", "description": "Dashboard相关的API"}, {"name": "home-redirection", "description": "处理 homepage redirection"}, {"name": "module-builds", "description": "模块 (<PERSON><PERSON><PERSON>) 编译包版本管理"}, {"name": "modules", "description": "软件模块管理相关 API"}, {"name": "plugin-bundles", "description": "插件 (Plugin) 包版本管理"}, {"name": "plugin-categories", "description": "插件分类相关的API"}, {"name": "plugin-db", "description": "插件数据库相关的API"}, {"name": "plugins", "description": "插件管理相关 API"}, {"name": "signature", "description": "RSA数字签名API控制器"}, {"name": "system", "description": "系统相关功能"}, {"name": "user-plugin-fetch-audits", "description": "插件获取审计记录管理"}, {"name": "user-plugins", "description": "用户登录系统相关的用户插件信息管理"}, {"name": "users", "description": "所有登录系统的用户信息管理"}, {"name": "web-api-test", "description": "For communication test with your client"}]}