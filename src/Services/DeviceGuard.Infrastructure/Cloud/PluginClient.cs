using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using DeviceGuard.Interface.Cloud;
using Flurl;
using Flurl.Http;

namespace DeviceGuard.Infrastructure.Cloud;

/// <summary>
/// Represents a client for interacting with the cloud to perform plugin-related operations.
/// </summary>
public class PluginClient : IPluginClient
{
    private readonly string        _baseUrl = CloudOptions.Url;
    private readonly ILoginSession _loginSession;

    public PluginClient(ILoginSession loginSession)
    {
        _loginSession = loginSession;
    }

    /// <summary>
    /// Downloads a plugin zip for the specified plugin ID and version.
    /// </summary>
    /// <param name="pluginId">The unique identifier of the plugin to download.</param>
    /// <param name="version">The version of the plugin to download.</param>
    /// <param name="cancellationToken">A token to monitor for cancellation requests.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the binary data of the plugin.</returns>
    public async Task<byte[]> DownloadPluginAsync(string pluginId, string version, CancellationToken cancellationToken)
    {
        try
        {
            var accessToken = await _loginSession.GetAccessTokenAsync(cancellationToken);

            var request = _baseUrl
                          .AppendPathSegment($"api/v1/plugins/{pluginId}/{version}/download")
                          .WithOAuthBearerToken(accessToken);
            var response = await request.GetBytesAsync(HttpCompletionOption.ResponseContentRead, cancellationToken);
            return response;
        }
        catch (FlurlHttpException ex)
        {
            // 处理HTTP错误
            if (ex.Call.Response != null)
            {
                var error = await ex.GetResponseJsonAsync<ErrorMessageModel>();
                throw new ApiException($"API调用失败: {error?.Message ?? ex.Message}", ex)
                {
                    ErrorCode  = error?.Code ?? "",
                    RequestId  = error?.RequestId ?? "",
                    StatusCode = ex.Call.Response.StatusCode
                };
            }

            throw;
        }
    }
}
