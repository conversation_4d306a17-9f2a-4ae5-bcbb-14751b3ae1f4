using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using DeviceGuard.Interface.Cloud;
using Flurl;
using Flurl.Http;

namespace DeviceGuard.Infrastructure.Cloud;

/// <summary>
/// 插件分类API客户端
/// </summary>
public class PluginDbClient : IPluginDbClient
{
    private readonly string _baseUrl = CloudOptions.Url;

    /// <summary>
    /// 获取插件分类树
    /// </summary>
    /// <param name="host">平台类型（可选）</param>
    /// <param name="cancellationToken"></param>
    /// <returns>返回提供给客户端使用的插件分类树，包含所有支持的产品</returns>
    public async Task<TreeNode> GetPluginTreeAsync(PluginHost host, CancellationToken cancellationToken)
    {
        try
        {
            var request = _baseUrl
                .AppendPathSegment($"api/v1/plugin-db/{host}/tree");

            var result = await request.GetJsonAsync<TreeNode>(cancellationToken: cancellationToken);
            return result;
        }
        catch (FlurlHttpException ex)
        {
            // 处理HTTP错误
            if (ex.Call.Response != null)
            {
                var error = await ex.GetResponseJsonAsync<ErrorMessageModel>();
                throw new ApiException($"API调用失败: {error?.Message ?? ex.Message}", ex)
                {
                    ErrorCode  = error?.Code ?? "",
                    RequestId  = error?.RequestId ?? "",
                    StatusCode = ex.Call.Response.StatusCode
                };
            }

            throw;
        }
    }

    public async Task<PluginDbMetaModel> GetPluginDbMetaAsync(PluginHost host, CancellationToken cancellationToken)
    {
        try
        {
            var request = _baseUrl
                .AppendPathSegment($"api/v1/plugin-db/{host}/meta");
            var result = await request.GetJsonAsync<PluginDbMetaModel>(cancellationToken: cancellationToken);
            return result;
        }
        catch (FlurlHttpException ex)
        {
            // 处理HTTP错误
            if (ex.Call.Response != null)
            {
                var error = await ex.GetResponseJsonAsync<ErrorMessageModel>();
                throw new ApiException($"API调用失败: {error?.Message ?? ex.Message}", ex)
                {
                    ErrorCode  = error?.Code ?? "",
                    RequestId  = error?.RequestId ?? "",
                    StatusCode = ex.Call.Response.StatusCode
                };
            }

            throw;
        }
    }

    /// <summary>
    /// 下载插件数据库
    /// </summary>
    /// <param name="host">平台类型（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>返回插件数据库的字节数组</returns>
    public async Task<byte[]> DownloadPluginDbAsync(PluginHost host, CancellationToken cancellationToken)
    {
        try
        {
            var request = _baseUrl
                .AppendPathSegment($"api/v1/plugin-db/{host}");

            // 将枚举值转换为小写字符串
            var response = await request.GetBytesAsync(HttpCompletionOption.ResponseContentRead, cancellationToken);
            return response;
        }
        catch (FlurlHttpException ex)
        {
            // 处理HTTP错误
            if (ex.Call.Response != null)
            {
                var error = await ex.GetResponseJsonAsync<ErrorMessageModel>();
                throw new ApiException($"API调用失败: {error?.Message ?? ex.Message}", ex)
                {
                    ErrorCode  = error?.Code ?? "",
                    RequestId  = error?.RequestId ?? "",
                    StatusCode = ex.Call.Response.StatusCode
                };
            }

            throw;
        }
    }
}
