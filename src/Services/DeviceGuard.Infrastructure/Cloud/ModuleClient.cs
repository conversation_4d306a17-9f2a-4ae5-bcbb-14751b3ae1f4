using System;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using DeviceGuard.Interface.Cloud;
using Flurl;
using Flurl.Http;

namespace DeviceGuard.Infrastructure.Cloud;

/// <summary>
/// Represents a client for interacting with the cloud to perform module-related operations.
/// </summary>
public class ModuleClient : IModuleClient
{
    private readonly string        _baseUrl = CloudOptions.Url;
    private readonly ILoginSession _loginSession;

    public ModuleClient(ILoginSession loginSession)
    {
        _loginSession = loginSession;
    }

    /// <summary>
    /// 下载模块
    /// </summary>
    /// <param name="moduleCode">模块编码</param>
    /// <param name="version">版本号（最新版本=latest）</param>
    /// <param name="cancellation">取消令牌</param>
    /// <returns>下载的模块二进制数据</returns>
    public async Task<byte[]> DownloadAsync(string moduleCode, string version, CancellationToken cancellation)
    {
        if (string.IsNullOrEmpty(moduleCode))
            throw new ArgumentException("Value cannot be null or empty.", nameof(moduleCode));
        if (string.IsNullOrEmpty(version))
            throw new ArgumentException("Value cannot be null or empty.", nameof(version));

        try
        {
            var accessToken = await _loginSession.GetAccessTokenAsync(cancellation);

            var request = _baseUrl
                          .AppendPathSegment($"api/v1/modules/{moduleCode}/{version}/download")
                          .WithOAuthBearerToken(accessToken);
            var response = await request.GetBytesAsync(HttpCompletionOption.ResponseContentRead, cancellation);
            return response;
        }
        catch (FlurlHttpException ex)
        {
            // 处理HTTP错误
            if (ex.Call.Response != null)
            {
                var error = await ex.GetResponseJsonAsync<ErrorMessageModel>();
                throw new ApiException($"API调用失败: {error?.Message ?? ex.Message}", ex)
                {
                    ErrorCode  = error?.Code ?? "",
                    RequestId  = error?.RequestId ?? "",
                    StatusCode = ex.Call.Response.StatusCode
                };
            }

            throw;
        }
    }
}
