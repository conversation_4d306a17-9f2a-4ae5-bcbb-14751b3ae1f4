using System;
using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using DeviceGuard.Interface.Cloud;
using Flurl;
using Flurl.Http;

namespace DeviceGuard.Infrastructure.Cloud;

/// <summary>
/// Represents a client for interacting with the cloud to perform artifact-related operations.
/// </summary>
public class ArtifactClient : IArtifactClient
{
    private readonly string        _baseUrl = CloudOptions.Url;
    private readonly ILoginSession _loginSession;

    public ArtifactClient(ILoginSession loginSession)
    {
        _loginSession = loginSession ?? throw new ArgumentNullException(nameof(loginSession));
    }

    /// <summary>
    /// Retrieves the latest version string of the specified software.
    /// </summary>
    /// <param name="softwareCode">
    /// A unique identifier for the software whose latest version is being retrieved.
    /// </param>
    /// <param name="cancellationToken">
    /// A token to monitor for cancellation requests.
    /// </param>
    /// <returns>
    /// A task representing the asynchronous operation. The task result contains the latest version string of the specified software.
    /// </returns>
    public async Task<string> GetLatestSoftwareVersionAsync(string softwareCode, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(softwareCode))
            throw new ArgumentException("Software code cannot be null or empty.", nameof(softwareCode));

        try
        {
            var request = _baseUrl
                .AppendPathSegment($"api/v1/artifacts/software/{softwareCode}/latest-version");

            // {
            //     "code" : "dgwin",
            //     "latestVersion" : "1.0.0",
            //     "updatedAt" : "2025-06-18T00:41:26.1113951"
            // }
            var response = await request.GetStringAsync(cancellationToken: cancellationToken);

            // 解析 JSON 响应并提取版本号
            using var jsonDocument = JsonDocument.Parse(response);
            var       root         = jsonDocument.RootElement;

            if (root.TryGetProperty("latestVersion", out var versionElement))
            {
                return versionElement.GetString() ?? throw new InvalidOperationException("Latest version is null in API response");
            }

            throw new InvalidOperationException("API response does not contain 'latestVersion' field");
        }
        catch (FlurlHttpException ex)
        {
            // 处理HTTP错误
            if (ex.Call.Response != null)
            {
                var error = await ex.GetResponseJsonAsync<ErrorMessageModel>();
                throw new ApiException($"API调用失败: {error?.Message ?? ex.Message}", ex)
                {
                    ErrorCode  = error?.Code ?? "",
                    RequestId  = error?.RequestId ?? "",
                    StatusCode = ex.Call.Response.StatusCode
                };
            }

            throw;
        }
    }

    /// <summary>
    /// Downloads the specified version of the software for the given software code asynchronously.
    /// </summary>
    /// <param name="softwareCode">The code representing the software to be downloaded.</param>
    /// <param name="version">The specific version of the software to download.</param>
    /// <param name="cancellationToken">A token to monitor for cancellation requests.</param>
    /// <returns>A byte array containing the downloaded software data.</returns>
    public async Task<byte[]> DownloadSoftwareAsync(string softwareCode, string version, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(softwareCode))
            throw new ArgumentException("Software code cannot be null or empty.", nameof(softwareCode));

        if (string.IsNullOrEmpty(version))
            throw new ArgumentException("Version cannot be null or empty.", nameof(version));

        try
        {
            var request = _baseUrl
                          .WithTimeout(TimeSpan.FromMinutes(30))
                          .AppendPathSegment($"api/v1/artifacts/software/{softwareCode}/download")
                          .AppendQueryParam("version", version);

            var response = await request.GetBytesAsync(HttpCompletionOption.ResponseContentRead, cancellationToken);
            return response;
        }
        catch (FlurlHttpException ex)
        {
            // 处理HTTP错误
            if (ex.Call.Response != null)
            {
                var error = await ex.GetResponseJsonAsync<ErrorMessageModel>();
                throw new ApiException($"API调用失败: {error?.Message ?? ex.Message}", ex)
                {
                    ErrorCode  = error?.Code ?? "",
                    RequestId  = error?.RequestId ?? "",
                    StatusCode = ex.Call.Response.StatusCode
                };
            }

            throw;
        }
    }
}
