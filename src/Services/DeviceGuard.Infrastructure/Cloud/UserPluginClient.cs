using System;
using System.Threading;
using System.Threading.Tasks;
using DeviceGuard.Interface.Cloud;
using Flurl;
using Flurl.Http;
using Serilog;

namespace DeviceGuard.Infrastructure.Cloud;

public class UserPluginClient : IUserPluginClient
{
    private readonly string        _baseUrl = CloudOptions.Url;
    private readonly ILoginSession _loginSession;
    private readonly ILogger       _logger;

    public UserPluginClient(
        ILoginSession loginSession,
        ILogger       logger)
    {
        _loginSession = loginSession;
        _logger       = logger;
    }

    public async Task<string> DownloadMyOwnedPluginsAsync(PluginHost host, string serialNumber, CancellationToken cancellationToken)
    {
        try
        {
            var accessToken = await _loginSession.GetAccessTokenAsync(cancellationToken);

            var request = _baseUrl
                          .AppendPathSegment("api/v1/user-plugins/me")
                          .SetQueryParam("host", host)
                          .SetQueryParam("serialNumber", serialNumber)
                          .WithOAuthBearerToken(accessToken);

            var response = await request.GetStringAsync(cancellationToken: cancellationToken);
            return response;
        }
        catch (FlurlHttpException ex)
        {
            // 处理HTTP错误
            if (ex.Call.Response != null)
            {
                var error = await ex.GetResponseJsonAsync<ErrorMessageModel>();
                throw new ApiException($"API调用失败: {error?.Message ?? ex.Message}", ex)
                {
                    ErrorCode  = error?.Code ?? "",
                    RequestId  = error?.RequestId ?? "",
                    StatusCode = ex.Call.Response.StatusCode
                };
            }

            throw;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Failed to download user owned plugins for machine {MachineSerialNumber}", serialNumber);
            throw;
        }
    }
}
