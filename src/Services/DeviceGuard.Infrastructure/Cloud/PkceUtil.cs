using System.Security.Cryptography;
using System.Text;
using Microsoft.IdentityModel.Tokens;

namespace DeviceGuard.Infrastructure.Cloud;

public static class PkceUtil
{
    public static string GenerateCodeVerifier()
    {
        var bytes = new byte[64]; // 可以是 43-128 字符之间
        RandomNumberGenerator.Fill(bytes);
        return Base64UrlEncoder.Encode(bytes);
    }

    public static string GenerateCodeChallenge(string codeVerifier)
    {
        var sha256 = SHA256.HashData(Encoding.ASCII.GetBytes(codeVerifier));
        return Base64UrlEncoder.Encode(sha256);
    }
}
