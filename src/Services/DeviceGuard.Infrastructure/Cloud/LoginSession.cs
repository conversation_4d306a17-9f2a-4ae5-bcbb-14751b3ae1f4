using System;
using System.Diagnostics;
using System.IdentityModel.Tokens.Jwt;
using System.Linq;
using System.Security.Authentication;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using DeviceGuard.Interface.Cloud;
using DeviceGuard.Windows.Interface.ViewLoader;
using Newtonsoft.Json;

namespace DeviceGuard.Infrastructure.Cloud;

/// <summary>
/// 登录客户端
/// </summary>
public class LoginSession : ILoginSession
{
    private readonly ILoginDialog      _loginDialog;
    private          TokenResponse?    _token;
    private readonly LoginClientConfig _config;
    private readonly ILoginClient      _loginClient;

    public LoginSession(ILoginDialog loginDialog, LoginClientConfig config, ILoginClient loginClient)
    {
        _loginDialog = loginDialog;
        _config      = config;
        _loginClient = loginClient;
    }

    public async Task LoginAsync(CancellationToken cancellation)
    {
        _token ??= await _loginDialog.ShowAsync(cancellation) ?? throw new AuthenticationException("登录失败")
        {
            Data =
            {
                ["Silence"] = true
            }
        };
    }

    public Task LogoutAsync(CancellationToken cancellation)
    {
        _token        = null;
        _config.Token = string.Empty;

        return Task.CompletedTask;
    }

    /// <summary>
    /// 获取 access_token, 如果这个token过期了, 则使用 refresh_token换一个, 如果 refresh_token 过期了, 则抛出未登录异常
    /// </summary>
    /// <param name="cancellation"></param>
    /// <returns></returns>
    public async Task<string> GetAccessTokenAsync(CancellationToken cancellation)
    {
        if (!await UpdateTokenAsync(cancellation))
        {
            await LoginAsync(cancellation);
        }

        return _token?.AccessToken ?? throw new AuthenticationException("用户登录流程错误");
    }

    /// <summary>
    /// 确保已经登录, 如果用户没有登录, 则弹出登录对话框提示用户登录
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task EnsureLoginAsync(CancellationToken cancellationToken)
    {
        if (!await UpdateTokenAsync(cancellationToken))
        {
            await LoginAsync(cancellationToken);
        }
    }

    /// <summary>
    /// 判断是否登录
    /// </summary>
    /// <param name="cancellation"></param>
    /// <returns></returns>
    public async Task<bool> IsLoggedInAsync(CancellationToken cancellation)
    {
        return await UpdateTokenAsync(cancellation);
    }

    public async Task<LoginUserInfo?> GetLoginUserAsync(CancellationToken cancellation)
    {
        if (!await UpdateTokenAsync(cancellation))
        {
            return null;
        }

        Debug.Assert(_token != null);
        Debug.Assert(_token.AccessToken != null);

        var handler  = new JwtSecurityTokenHandler();
        var jwtToken = handler.ReadJwtToken(_token.AccessToken);

        return new LoginUserInfo
        {
            Id          = jwtToken.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier)?.Value,
            Name        = jwtToken.Claims.FirstOrDefault(c => c.Type == "name")?.Value,
            UserName    = jwtToken.Claims.FirstOrDefault(c => c.Type == "preferred_username")?.Value,
            Email       = jwtToken.Claims.FirstOrDefault(c => c.Type == ClaimTypes.Email)?.Value,
            PhoneNumber = jwtToken.Claims.FirstOrDefault(c => c.Type == "phone_number")?.Value,
            Avatar      = jwtToken.Claims.FirstOrDefault(c => c.Type == "picture")?.Value
        };
    }

    private async Task<bool> UpdateTokenAsync(CancellationToken cancellation)
    {
        try
        {
            if (_token == null)
            {
                if (_config.RememberMe)
                {
                    _token = JsonConvert.DeserializeObject<TokenResponse>(_config.Token)
                             ?? throw new AuthenticationException("登录失败: 无法解析响应内容");
                }
                else
                {
                    throw new AuthenticationException("用户未登录");
                }
            }

            if (!_token.AccessTokenExpired())
            {
                return true;
            }

            if (!_token.RefreshTokenExpired())
            {
                _token = await _loginClient.RefreshTokenAsync(_token.RefreshToken, cancellation);

                if (_config.RememberMe)
                {
                    _config.Token = JsonConvert.SerializeObject(_token);
                }

                return true;
            }

            throw new AuthenticationException("用户登录已过期");
        }
        catch (Exception)
        {
            return false;
        }
    }
}
