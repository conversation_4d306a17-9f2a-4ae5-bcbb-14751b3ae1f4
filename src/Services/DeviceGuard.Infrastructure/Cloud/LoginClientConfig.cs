using Oulida.Configuration;

namespace DeviceGuard.Infrastructure.Cloud;

public class LoginClientConfig : ConfigBase
{
    /// <summary>
    /// 构造函数，
    /// </summary>
    /// <param name="config">强命名该分组</param>
    public LoginClientConfig(Config config) : base(config)
    {
    }

    #region Overrides of ConfigBase

    /// <summary>
    /// 定义作用域(包)
    /// 在多个配置文件合并的时候，用来区分不同的作用域
    /// </summary>
    public override string PathName => "LoginClient";

    #endregion

    /// <summary>
    /// 上次登录的用户名
    /// </summary>
    public string LastUserName
    {
        get => GetPropertyValue("");
        set => SetPropertyValue(value);
    }

    /// <summary>
    /// 用户登录的 access token
    /// </summary>
    public string Token
    {
        get => GetPropertyValue("");
        set => SetPropertyValue(value);
    }

    public bool RememberMe
    {
        get => GetPropertyValue(false);
        set => SetPropertyValue(value);
    }
}
