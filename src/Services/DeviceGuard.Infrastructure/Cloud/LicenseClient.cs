using System;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using DeviceGuard.Interface.Cloud;
using MyNamespace;

namespace DeviceGuard.Infrastructure.Cloud;

public class LicenseClient : ILicenseClient
{
    private readonly ILoginSession _loginSession;

    public LicenseClient(ILoginSession loginSession)
    {
        _loginSession = loginSession;
    }

    /// <summary>
    /// 通过 SKU 和 serialNumber 激活设备
    /// </summary>
    /// <param name="sku"></param>
    /// <param name="serialNumber"></param>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentException"></exception>
    public async Task<string> ActivateLicenseAsync(string sku, string serialNumber, CancellationToken cancellationToken)
    {
        if (string.IsNullOrEmpty(sku)) throw new ArgumentException("Value cannot be null or empty.",          nameof(sku));
        if (string.IsNullOrEmpty(serialNumber)) throw new ArgumentException("Value cannot be null or empty.", nameof(serialNumber));

        // 获取登录账号
        var token = await _loginSession.GetAccessTokenAsync(cancellationToken);

        HttpClient httpClient = new();
        httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {token}");

        LicensesClient client = new(CloudOptions.Url, httpClient);
        var response = await client.ActivateLicenseAsync(new ActivateLicenseCommand
            {
                Sku          = sku,
                SerialNumber = serialNumber
            },
            cancellationToken);

        return response.ProductKey;
    }
}
