using System;
using System.IO;
using System.Linq;
using DeviceGuard.Interface.FileSystem;

namespace DeviceGuard.Infrastructure.FileSystem;

/// <summary>
/// 文件测试接口默认实现
/// </summary>
public class FileTester : IFileTester
{
    /// <summary>
    /// 文件可以写
    /// </summary>
    /// <param name="filePath"></param>
    /// <returns></returns>
    public bool CanWrite(string filePath)
    {
        if (filePath == null)
            throw new ArgumentNullException(nameof(filePath));

        var fileExist = File.Exists(filePath);

        try
        {
            if (fileExist)
            {
                using var stream = File.OpenWrite(filePath);
                stream.Close();
            }
            else
            {
                // ReSharper disable once LocalizableElement
                File.WriteAllText(filePath, "Test writable");
            }

            return true;
        }
        catch
        {
            // ignored
        }
        finally
        {
            // remove file if file create by test
            if (!fileExist)
                TryDelete(filePath);
        }

        return false;
    }

    /// <summary>
    /// 尝试删除文件
    /// </summary>
    /// <param name="filePath"></param>
    private void TryDelete(string filePath)
    {
        if (filePath == null)
            throw new ArgumentNullException(nameof(filePath));

        try
        {
            if (File.Exists(filePath))
                File.Delete(filePath);
        }
        catch
        {
            // ignored
        }
    }

    /// <summary>
    /// 文件可以读
    /// </summary>
    /// <param name="filePath"></param>
    /// <returns></returns>
    public bool CanRead(string filePath)
    {
        if (filePath == null)
            throw new ArgumentNullException(nameof(filePath));

        if (!File.Exists(filePath))
            return false;

        try
        {
            using var stream = File.OpenRead(filePath);
            stream.Close();

            return true;
        }
        catch
        {
            // ignored
        }

        return false;
    }

    /// <summary>
    /// 文件名是否合法
    /// </summary>
    /// <param name="filePath"></param>
    /// <returns></returns>
    public bool IsFileNameLegal(string filePath)
    {
        var invalidChars = Path.GetInvalidFileNameChars();
        if (filePath.Any(c => invalidChars.Contains(c)))
            return false;

        return true;
    }
}