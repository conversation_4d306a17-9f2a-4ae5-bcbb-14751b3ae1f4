using System;
using System.IO;
using DeviceGuard.Interface.FileSystem;
using Oulida.Configuration;

namespace DeviceGuard.Infrastructure.FileSystem;

/// <summary>
/// 应用程序目录的管理
/// </summary>
public class FolderService : IFolderService
{
    private readonly Config            _config;
    private readonly IAppFolderService _appFolderCreationService;

    /// <summary>
    /// 配置文件路径
    /// </summary>
    private const string ConfigPathName = "Folder";

    public FolderService(Config config, IAppFolderService appFolderCreationService)
    {
        _config                   = config;
        _appFolderCreationService = appFolderCreationService;
    }


    /// <summary>
    /// 获取文件夹
    /// </summary>
    /// <param name="appFolder">文件夹类型</param>
    /// <returns></returns>
    public string GetFolderOrDefault(AppFolder appFolder)
    {
        if (appFolder == null)
            throw new ArgumentNullException(nameof(appFolder));

        if (appFolder.RememberLatestFolder)
        {
            var configPath = ConfigPath.CombinePath(ConfigPathName, appFolder.Key);
            var folder     = _config.GetValue<string>(configPath);

            // 路径不存在,则忽略, 毕竟记住的 folder 只是一个临时的机制, 用户可能卸载 U 盘等操作导致无法访问
            if (!string.IsNullOrEmpty(folder) && Directory.Exists(folder))
                return folder;
        }

        var defaultFolder = _appFolderCreationService.GetOrCreateFolder(appFolder);
        return defaultFolder;
    }

    /// <summary>
    /// 保存用户最后访问的文件夹
    /// </summary>
    /// <param name="appFolder">文件夹类型</param>
    /// <param name="recentPath">路径</param>
    public void SaveFolder(AppFolder appFolder, string recentPath)
    {
        if (appFolder == null)
            throw new ArgumentNullException(nameof(appFolder));

        if (!appFolder.RememberLatestFolder)
            throw new ArgumentException($"The folder <{appFolder.Key}> cannot be remember", nameof(appFolder));

        var configPath = ConfigPath.CombinePath(ConfigPathName, appFolder.Key);
        _config.SetValue(configPath, recentPath);
    }
}