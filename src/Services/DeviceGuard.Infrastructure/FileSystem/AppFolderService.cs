#nullable enable
using System;
using System.IO;
using DeviceGuard.Interface.FileSystem;

namespace DeviceGuard.Infrastructure.FileSystem;

/// <summary>
/// 应用程序目录的管理, 包含管理系统文件夹和用户文件夹
/// </summary>
public class AppFolderService : IAppFolderService
{
    /// <summary>
    /// 固定的子文件夹 :D
    /// </summary>
    protected const string FixedFolder = @"DeviceGuard";

    /// <summary>
    /// 放在 EXE 目录中么?
    /// 调试模式, 数据放在身边即可
    /// </summary>
    public static string? BaseFolder { get; set; }

    /// <summary>
    /// 获取系统文件夹
    /// </summary>
    /// <returns></returns>
    private string GetBaseFolder()
    {
        if (BaseFolder is { Length: > 0 } baseFolder)
        {
            return baseFolder;
        }

        var path     = Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
        var filePath = Path.Combine(path, FixedFolder);
        filePath = Path.GetFullPath(filePath);

        return filePath;
    }

    /// <summary>
    /// 准备指定的文件夹,如果不存在就创建
    /// </summary>
    /// <param name="folderPath"></param>
    /// <returns></returns>
    private string PrepareFolder(params string[] folderPath)
    {
        var path = Path.Combine(folderPath);
        path = Path.GetFullPath(path);

        if (!Directory.Exists(path))
        {
            Directory.CreateDirectory(path);
        }

        return path;
    }

    /// <summary>
    /// 获取文件夹
    /// </summary>
    /// <param name="folder">文件夹类型</param>
    /// <returns></returns>
    public string GetOrCreateFolder(AppFolder folder)
    {
        if (folder == null)
            throw new ArgumentNullException(nameof(folder));

        // 读取文件夹名称
        return PrepareFolder(GetBaseFolder(), folder.DefaultFolder);
    }


    /// <summary>
    /// 准备默认的目录
    /// </summary>
    public void PrepareDefaultFolders(AppFolder[] appFolders)
    {
        foreach (var item in appFolders)
        {
            var baseFolder = GetBaseFolder();
            PrepareFolder(baseFolder, item.DefaultFolder);
        }
    }
}