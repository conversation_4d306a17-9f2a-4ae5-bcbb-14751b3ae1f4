using System;
using System.IO;
using System.Linq;
using System.Security;
using System.Security.Permissions;
using DeviceGuard.Interface.FileSystem;

namespace DeviceGuard.Infrastructure.FileSystem;

/// <summary>
/// 文件夹测试接口默认实现
/// </summary>
class FolderTester : IFolderTester
{
    /// <summary>
    /// 文件夹是否可写入
    /// </summary>
    /// <param name="folderName"></param>
    /// <returns></returns>
    public bool CanWrite(string folderName)
    {
        if (folderName == null)
            throw new ArgumentNullException(nameof(folderName));

        // 判断文件夹是否存在, 不存在，自然不可写
        if (!Directory.Exists(folderName))
            return false;

        throw new NotImplementedException();
        // 判断文件夹写入权限
        // var permissionSet   = new PermissionSet(PermissionState.None);
        // var writePermission = new FileIOPermission(FileIOPermissionAccess.Write, folderName);
        // permissionSet.AddPermission(writePermission);
        // if (!permissionSet.IsSubsetOf(AppDomain.CurrentDomain.PermissionSet))
        //     return false;
        //
        // return true;
    }

    /// <summary>
    /// 是否拥有足够的空间
    /// </summary>
    /// <param name="folderName">文件夹的名称</param>
    /// <param name="minimumSpaceRequired">最低空间检查</param>
    /// <returns></returns>
    public bool HasEnoughSpace(string folderName, double minimumSpaceRequired)
    {
        if (folderName == null)
            throw new ArgumentNullException(nameof(folderName));
        if (minimumSpaceRequired < 0)
            throw new ArgumentOutOfRangeException(nameof(minimumSpaceRequired));

        // retrieve driver name
        var rootName = Path.GetPathRoot(folderName);
        if (rootName == null)
            throw new ArgumentNullException(nameof(rootName));

        // not enough space in driver
        var drive = new DriveInfo(rootName);
        if (drive.AvailableFreeSpace < minimumSpaceRequired)
            return false;

        return true;
    }

    /// <summary>
    /// 文件名是否合法
    /// </summary>
    /// <param name="folderName">文件夹名</param>
    /// <returns></returns>
    public bool IsFolderNameLegal(string folderName)
    {
        var invalidChars = Path.GetInvalidFileNameChars();
        if (folderName.Any(c => invalidChars.Contains(c)))
            return false;

        return true;
    }
}