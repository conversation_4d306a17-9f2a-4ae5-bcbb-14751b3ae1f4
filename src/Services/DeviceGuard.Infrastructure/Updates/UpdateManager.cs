using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using DeviceGuard.Interface;
using DeviceGuard.Interface.Cloud;
using DeviceGuard.Interface.FileSystem;
using Semver;
using Serilog;

namespace DeviceGuard.Infrastructure.Updates;

/// <summary>
/// 软件更新管理器，负责检查、下载和安装软件更新
/// </summary>
public class UpdateManager : IUpdateManager
{
    private readonly IArtifactClient   _artifactClient;
    private readonly IAppFolderService _appFolderService;
    private readonly ILogger           _logger;

    /// <summary>
    /// 软件代码，用于从云端获取更新
    /// </summary>
    private const string SoftwareCode = "dgWin";

    /// <summary>
    /// 更新文件名
    /// </summary>
    private const string UpdateFileName = "DeviceGuard-Update.exe";

    public UpdateManager(
        IArtifactClient   artifactClient,
        IAppFolderService appFolderService,
        ILogger           logger)
    {
        _artifactClient   = artifactClient ?? throw new ArgumentNullException(nameof(artifactClient));
        _appFolderService = appFolderService ?? throw new ArgumentNullException(nameof(appFolderService));
        _logger           = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    /// <summary>
    /// 下载更新
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task DownloadUpdatesAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.Information("开始检查软件更新...");

            // 1. 获取当前版本
            var currentVersion = GetCurrentVersion();
            if (currentVersion == null)
            {
                _logger.Warning("无法获取当前软件版本信息");
                return;
            }

            _logger.Information("当前软件版本: {CurrentVersion}", currentVersion);

            // 2. 获取最新版本
            var latestVersionString = await _artifactClient.GetLatestSoftwareVersionAsync(SoftwareCode, cancellationToken);
            if (string.IsNullOrEmpty(latestVersionString))
            {
                _logger.Warning("无法获取最新版本信息");
                return;
            }

            if (!SemVersion.TryParse(latestVersionString, out var latestVersion))
            {
                _logger.Warning("最新版本格式无效: {LatestVersion}", latestVersionString);
                return;
            }

            _logger.Information("最新软件版本: {LatestVersion}", latestVersion);

            // 3. 比较版本
            if (currentVersion.ComparePrecedenceTo(latestVersion) >= 0)
            {
                _logger.Information("当前版本已是最新版本，无需更新");
                return;
            }

            _logger.Information("发现新版本，开始下载更新...");

            // 4. 下载更新文件
            var updateData = await _artifactClient.DownloadSoftwareAsync(SoftwareCode, latestVersionString, cancellationToken);
            if (updateData == null || updateData.Length == 0)
            {
                _logger.Warning("下载的更新文件为空");
                return;
            }

            // 5. 保存更新文件
            var updatesFolder  = _appFolderService.GetOrCreateFolder(AppFolder.Updates);
            var updateFilePath = Path.Combine(updatesFolder, UpdateFileName);

            // 如果已存在更新文件，先删除
            if (File.Exists(updateFilePath))
            {
                File.Delete(updateFilePath);
            }

            await File.WriteAllBytesAsync(updateFilePath, updateData, cancellationToken);
            _logger.Information("更新文件已下载到: {UpdateFilePath}", updateFilePath);

            // 6. 保存版本信息文件
            var versionInfoPath = Path.Combine(updatesFolder, "version.txt");
            await File.WriteAllTextAsync(versionInfoPath, latestVersionString, cancellationToken);

            _logger.Information("软件更新下载完成，版本: {LatestVersion}", latestVersion);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "下载软件更新时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 获取等待安装的更新版本号
    /// </summary>
    /// <param name="cancellationToken">取消令牌，允许异步操作被取消</param>
    /// <returns>返回等待安装的版本号，如果没有等待更新的版本则返回 null</returns>
    public async Task<string?> GetUpdatePendingVersionAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var updatesFolder   = _appFolderService.GetOrCreateFolder(AppFolder.Updates);
            var updateFilePath  = Path.Combine(updatesFolder, UpdateFileName);
            var versionInfoPath = Path.Combine(updatesFolder, "version.txt");

            // 检查更新文件是否存在
            if (!File.Exists(updateFilePath) || !File.Exists(versionInfoPath))
            {
                return null;
            }

            // 检查文件是否有效
            var fileInfo = new FileInfo(updateFilePath);
            if (fileInfo.Length == 0)
            {
                return null;
            }

            // 读取版本信息
            var versionString = await File.ReadAllTextAsync(versionInfoPath, cancellationToken);
            if (string.IsNullOrEmpty(versionString) || !SemVersion.TryParse(versionString.Trim(), out var updateVersion))
            {
                return null;
            }

            // 比较版本
            var currentVersion = GetCurrentVersion();
            if (currentVersion == null)
            {
                return null;
            }

            // 只有当更新版本比当前版本新时才返回版本号
            return currentVersion.ComparePrecedenceTo(updateVersion) < 0 ? updateVersion.ToString() : null;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "检查更新状态时发生错误");
            return null;
        }
    }

    /// <summary>
    /// 安装更新
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    public async Task InstallUpdatesAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.Information("开始安装软件更新...");

            // 1. 检查是否有可安装的更新
            var pendingVersion = await GetUpdatePendingVersionAsync(cancellationToken);
            if (pendingVersion == null)
            {
                _logger.Warning("没有可安装的更新");
                return;
            }

            _logger.Information("准备安装更新版本: {PendingVersion}", pendingVersion);

            var updatesFolder  = _appFolderService.GetOrCreateFolder(AppFolder.Updates);
            var updateFilePath = Path.Combine(updatesFolder, UpdateFileName);

            // 2. 验证更新文件存在且可执行
            if (!File.Exists(updateFilePath))
            {
                _logger.Error("更新文件不存在: {UpdateFilePath}", updateFilePath);
                throw new FileNotFoundException("更新文件不存在", updateFilePath);
            }

            _logger.Information("启动更新程序: {UpdateFilePath}", updateFilePath);

            // 3. 启动更新程序
            var startInfo = new ProcessStartInfo
            {
                FileName        = updateFilePath,
                UseShellExecute = true,
                Verb            = "runas" // 以管理员权限运行
            };

            Process.Start(startInfo);

            _logger.Information("更新程序已启动，当前应用程序即将退出");

            // 4. 退出当前应用程序
            Environment.Exit(0);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "安装软件更新时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 获取当前应用程序版本
    /// </summary>
    /// <returns></returns>
    protected virtual SemVersion? GetCurrentVersion()
    {
        try
        {
            var assembly = Assembly.GetEntryAssembly();
            if (assembly == null)
            {
                // 在测试环境中，如果无法获取入口程序集，返回一个默认版本
                return SemVersion.Parse("1.0.0");
            }

            var versionAttribute = assembly.GetCustomAttribute<AssemblyInformationalVersionAttribute>();
            if (versionAttribute?.InformationalVersion == null)
            {
                // 在测试环境中，如果无法获取版本信息，返回一个默认版本
                return SemVersion.Parse("1.0.0");
            }

            // 移除构建元数据（+后面的部分）
            var versionString = versionAttribute.InformationalVersion.Split('+')[0];

            return SemVersion.TryParse(versionString, out var version) ? version : SemVersion.Parse("1.0.0");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "获取当前版本信息时发生错误");
            return SemVersion.Parse("1.0.0");
        }
    }
}
