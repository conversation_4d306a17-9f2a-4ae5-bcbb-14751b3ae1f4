using DeviceGuard.Infrastructure.Cloud;
using DeviceGuard.Infrastructure.Configuration;
using DeviceGuard.Infrastructure.FileSystem;
using DeviceGuard.Infrastructure.Updates;
using DeviceGuard.Interface;
using DeviceGuard.Interface.Cloud;
using DeviceGuard.Interface.FileSystem;
using DeviceGuard.Interface.Prism;
using Prism.Ioc;

namespace DeviceGuard.Infrastructure.DependencyInjection;

/// <summary>
/// 基础设施部件
/// </summary>
public class InfrastructureComponent : IPrismComponent
{
    public void RegisterTypes(IContainerRegistry containerRegistry)
    {
        // 登录和会话
        containerRegistry.RegisterSingleton<ILoginSession, LoginSession>();

        // 文件夹准备
        containerRegistry.Register<IAppFolderService, AppFolderService>();
        containerRegistry.Register<IFolderService, FolderService>();

        // 文件系统测试
        containerRegistry.Register<IFileTester, FileTester>();
        containerRegistry.Register<IFolderTester, FolderTester>();

        // 配置信息
        containerRegistry.RegisterInstance(ConfigFactory.Config);

        // 云端
        containerRegistry.RegisterSingleton<ILoginClient, LoginClient>();
        containerRegistry.RegisterSingleton<ILicenseClient, LicenseClient>();
        containerRegistry.RegisterSingleton<IPluginDbClient, PluginDbClient>();
        containerRegistry.RegisterSingleton<IPluginClient, PluginClient>();
        containerRegistry.RegisterSingleton<IModuleClient, ModuleClient>();
        containerRegistry.RegisterSingleton<IUserPluginClient, UserPluginClient>();
        containerRegistry.RegisterSingleton<IArtifactClient, ArtifactClient>();

        // 更新管理
        containerRegistry.Register<IUpdateManager, UpdateManager>();
    }
}
