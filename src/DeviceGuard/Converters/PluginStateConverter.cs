using System.Globalization;
using System.Windows;
using System.Windows.Data;
using DeviceGuard.ModuleCore.Plugins;

namespace DeviceGuard.Shell.Converters;

public class PluginStateConverter : IValueConverter
{
    public object? Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is not PluginState pluginState)
        {
            return null;
        }

        const string foreground = "Foreground";
        const string background = "Background";
        const string text       = "Text";

        switch (pluginState)
        {
            case PluginState.Owned:
                return parameter switch
                {
                    foreground => "White",
                    background => "Green",
                    text       => "已拥有",
                    _          => throw new ArgumentException("Invalid parameter")
                };
            case PluginState.Installed:
                return parameter switch
                {
                    foreground => Application.Current.TryFindResource("MaterialDesign.Brush.Primary.Foreground"),
                    background => Application.Current.TryFindResource("MaterialDesign.Brush.Primary"),
                    text       => "已安装",
                    _          => throw new ArgumentException("Invalid parameter")
                };
            case PluginState.NewVersionAvailable:
                return parameter switch
                {
                    foreground => Application.Current.TryFindResource("MaterialDesign.Brush.Secondary.Foreground"),
                    background => Application.Current.TryFindResource("MaterialDesign.Brush.Secondary"),
                    text       => "有更新",
                    _          => throw new ArgumentException("Invalid parameter")
                };
            case PluginState.None:
            default:
                return parameter switch
                {
                    foreground => "Transparent",
                    background => "Transparent",
                    text       => "",
                    _          => throw new ArgumentException("Invalid parameter")
                };
        }
    }

    public object? ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
