namespace DeviceGuard.Shell.Licenses
{
    public static class ArrayExtensions
    {
        /// <summary>
        /// Return the subarray of a source
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="source"></param>
        /// <param name="index"></param>
        /// <param name="length"></param>
        /// <returns></returns>
        public static T[] SubArray<T>(this T[] source, int index, int length)
        {
            if (source == null)
                throw new ArgumentNullException(nameof(source));
            if (index < 0)
                throw new ArgumentOutOfRangeException(nameof(index));
            if (length <= 0)
                throw new ArgumentOutOfRangeException(nameof(length));

            if (source.Length < index + length)
                throw new ArgumentOutOfRangeException();

            var dest = new T[length];
            Array.Copy(source, index, dest, 0, length);

            return dest;
        }
    }
}
