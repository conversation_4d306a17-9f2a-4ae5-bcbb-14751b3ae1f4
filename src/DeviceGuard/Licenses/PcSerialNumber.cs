using System.Management;
using System.Security.Cryptography;
using System.Text;
using System.Text.RegularExpressions;
using DeviceGuard.Interface.Licenses;

namespace DeviceGuard.Shell.Licenses
{
    /// <summary>
    /// 生成PC序列号
    /// </summary>
    public class PcSerialNumber : IPcSerialNumber
    {
        /// <summary>
        /// PC 序列号获取有点点慢, 但是运行后永远不变, 因此使用了全局变量缓存
        /// </summary>
        private static string? _cache = null;

        /// <summary>
        /// 生成PC序列号
        /// </summary>
        /// <returns></returns>
        public string GetSerialNumber()
        {
            return _cache ??= GenerateSerialNumber();
        }

        private string GenerateSerialNumber()
        {
            // 首先获取主板的序列号
            var biosSerialNumber   = GetBiosSerialNumber();
            var motherSerialNumber = GetMotherboardSerialNumber();
            var sn                 = $"{motherSerialNumber}-{biosSerialNumber}";

            // 如果失败，获取第一块硬盘的序列号
            if (string.IsNullOrEmpty(sn))
                sn = GetPrimaryHddSerialNumber();

            // 如果失败，出错
            if (string.IsNullOrEmpty(sn))
                throw new Exception("Can not find fingerprint in your pc");

            // 添加签名, 生成Hash
            sn += "DeviceGuard ^_^";
            var hash = MD5.Create().ComputeHash(Encoding.Default.GetBytes(sn));

            // 1122-3344-5566-7788
            var bytes = hash.SubArray(0, 8);

            var byteText = string.Join("", bytes.Select(b => $"{b:x2}"));
            byteText = Regex.Replace(byteText, @"(\w{4})", "$1-").TrimEnd('-');

            return byteText.ToUpper();
        }


        /// <summary>
        /// Retrieving Motherboard Serial number
        /// </summary>
        /// <returns></returns>
        private string? GetBiosSerialNumber()
        {
            try
            {
                var searcher    = new ManagementObjectSearcher("root\\CIMV2", "SELECT * FROM Win32_BIOS");
                var motherboard = searcher.Get().OfType<ManagementObject>().FirstOrDefault();
                return motherboard?.GetPropertyValue("SerialNumber").ToString();
            }
            catch
            {
                // ignored
            }

            return null;
        }


        /// <summary>
        /// Retrieving Motherboard Serial number
        /// </summary>
        /// <returns></returns>
        private string? GetMotherboardSerialNumber()
        {
            try
            {
                var searcher    = new ManagementObjectSearcher("root\\CIMV2", "SELECT * FROM Win32_BaseBoard");
                var motherboard = searcher.Get().OfType<ManagementObject>().FirstOrDefault();
                return motherboard?.GetPropertyValue("SerialNumber").ToString();
            }
            catch
            {
                // ignored
            }

            return null;
        }


        /// <summary>
        /// 主硬盘序列号
        /// </summary>
        /// <returns></returns>
        private static string? GetPrimaryHddSerialNumber()
        {
            try
            {
                var management = new ManagementClass("Win32_DiskDrive");
                var instances  = management.GetInstances();
                var q = (from c in instances.OfType<ManagementObject>()
                         orderby c["Index"]
                         select c).FirstOrDefault();
                return q?["SerialNumber"]?.ToString();
            }
            catch
            {
                // ignored
            }

            return null;
        }
    }
}
