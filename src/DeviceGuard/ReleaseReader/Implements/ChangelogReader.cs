using System.IO;
using System.Reflection;
using DeviceGuardCloud.WebApi.ReleaseReader;
using Doulex;
using Newtonsoft.Json;
using Semver;

namespace DeviceGuard.Shell.ReleaseReader.Implements;

/// <summary>
/// 查看更新日志
/// </summary>
public class ChangelogReader : IChangelogReader
{
    /// <summary>
    /// 更新日志缓存，避免每次都读取
    /// </summary>
    private static Changelog[]? _cache;

    private static AssemblyBuildInfo? _assemblyInfo;

    /// <summary>
    /// 获取更新日志
    /// </summary>
    /// <returns></returns>
    public Changelog[] GetChangelogs()
    {
        return _cache ??= ReadChangelogs();
    }

    /// <summary>
    /// 获取最新的更新日志
    /// </summary>
    /// <returns></returns>
    public Changelog? GetLatestChangelog()
    {
        var notes = GetChangelogs();
        return notes.MaxBy(x => x.Version);
    }

    /// <inheritdoc />
    public AssemblyBuildInfo GetAssemblyBuildInfo()
    {
        _assemblyInfo ??= GetBuildInfoCore();
        return _assemblyInfo;
    }

    private AssemblyBuildInfo GetBuildInfoCore()
    {
        var assembly = Assembly.GetEntryAssembly() ?? throw new InvalidDataException("Cannot get entry assembly info");
        var metadata = assembly.GetCustomAttributes<AssemblyMetadataAttribute>().ToArray();

        var version = SemVersion.TryParse(assembly.GetCustomAttribute<AssemblyInformationalVersionAttribute>()?.InformationalVersion, out var semver) ? semver : null;

        return new AssemblyBuildInfo
        {
            Name        = assembly.GetName().Name,
            Version     = version?.ToString(),
            Type        = version?.Prerelease,
            BuildTime   = DateTime.TryParse(metadata.FirstOrDefault(x => x.Key == "time")?.Value, out var time) ? time : null,
            BuildNumber = metadata.FirstOrDefault(x => x.Key == "build")?.Value,
            IsRelease   = version?.IsRelease ?? true,
        };
    }

    private Changelog[] ReadChangelogs()
    {
        var asm   = Assembly.GetExecutingAssembly();
        var names = asm.GetManifestResourceNames();
        foreach (var name in names)
        {
            if (!name.EndsWith("changelog.json"))
                continue;

            using var stream = asm.GetManifestResourceStream(name) ??
                               throw new InvalidOperationException("Cannot get resource stream of changelog.json");
            var reader = new StreamReader(stream);
            var json   = reader.ReadToEnd();
            var notes  = JsonConvert.DeserializeObject<Changelog[]>(json) ?? [];
            notes = notes.Where(x => !x.Version.IsNullOrEmpty()).ToArray();
            return notes;
        }

        throw new InvalidOperationException("Cannot find changelog.json");
    }
}
