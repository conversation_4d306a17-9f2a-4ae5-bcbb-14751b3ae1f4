using Newtonsoft.Json;

namespace DeviceGuardCloud.WebApi.ReleaseReader;

/// <summary>
/// 版本更新日志信息
/// </summary>
public class Changelog
{
    /// <summary>
    /// 版本号
    /// </summary>
    public string? Version { get; set; }

    /// <summary>
    /// 发布日期
    /// </summary>
    public string? Date { get; set; }

    /// <summary>
    /// 版本概述
    /// </summary>
    public string? Summary { get; set; }

    /// <summary>
    /// 新增功能列表
    /// </summary>
    [JsonProperty("features")]
    public string[]? Features { get; set; }

    /// <summary>
    /// 问题修复列表
    /// </summary>
    [JsonProperty("fixed")]
    public string[]? Fixed { get; set; }

    /// <summary>
    /// 破坏性变更列表
    /// </summary>
    [JsonProperty("breakingChanges")]
    public string[]? BreakingChanges { get; set; }

    /// <summary>
    /// 已弃用功能列表
    /// </summary>
    [JsonProperty("deprecation")]
    public string[]? Deprecation { get; set; }
}

public class AssemblyBuildInfo
{
    public string? Name { get; set; }

    public string? Version { get; set; }

    public string? Type { get; set; }

    public bool IsRelease { get; set; }

    public DateTime? BuildTime { get; set; }

    public string? BuildNumber { get; set; }
}
