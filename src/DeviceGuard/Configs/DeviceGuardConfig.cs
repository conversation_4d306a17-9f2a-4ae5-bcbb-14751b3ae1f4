using Newtonsoft.Json;
using Oulida.Configuration;

namespace DeviceGuard.Shell.Configs;

public class DeviceGuardConfig : ConfigBase
{
    /// <summary>
    /// 构造函数，
    /// </summary>
    /// <param name="config">强命名该分组</param>
    public DeviceGuardConfig(Config config) : base(config)
    {
    }

    #region Overrides of ConfigBase

    /// <summary>
    /// 定义作用域(包)
    /// 在多个配置文件合并的时候，用来区分不同的作用域
    /// </summary>
    public override string PathName => "UI";

    #endregion

    // /// <summary>
    // /// 最后打开的模块名
    // /// </summary>
    // public string LatestPageName
    // {
    //     get => GetPropertyValue("");
    //     set => SetPropertyValue(value);
    // }

    /// <summary>
    /// 最近打开的插件
    /// </summary>
    public string[] RecentPlugins
    {
        get
        {
            try
            {
                var value = GetPropertyValue("[]");
                return JsonConvert.DeserializeObject<string[]>(value) ?? [];
            }
            catch (Exception)
            {
                return [];
            }
        }
        set
        {
            var text = JsonConvert.SerializeObject(value);
            SetPropertyValue(text);
        }
    }
}
