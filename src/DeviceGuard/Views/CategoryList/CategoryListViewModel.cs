using DeviceGuard.ModuleCore.Plugins;
using DeviceGuard.Shell.Interface.Breadcrumbs;
using DeviceGuard.Shell.Interface.Pages;
using DeviceGuard.Shell.Views.Pages.CategoryPage;
using DeviceGuard.Shell.Views.Pages.PluginPage;
using DeviceGuard.Windows.Utility.Dialogs;
using DeviceGuard.Windows.Utility.Mvvm;
using PropertyChanged;

namespace DeviceGuard.Shell.Views.CategoryList;

public class CategoryListViewModel : RegionViewModelBase
{
    private readonly IMessageBox                  _messageBox;
    private readonly CategoryPageViewLoader       _categoryPageViewLoader;
    private readonly IPluginPageViewLoaderFactory _pluginPageViewLoaderFactory;
    private readonly IPluginManager               _pluginManager;
    private readonly IBreadcrumb                  _breadcrumb;

    public CategoryListViewModel(
        IMessageBox                  messageBox,
        CategoryPageViewLoader       categoryPageViewLoader,
        IPluginPageViewLoaderFactory pluginPageViewLoaderFactory,
        IPluginManager               pluginManager,
        IBreadcrumb                  breadcrumb)
    {
        _messageBox                  = messageBox;
        _categoryPageViewLoader      = categoryPageViewLoader;
        _pluginPageViewLoaderFactory = pluginPageViewLoaderFactory;
        _pluginManager               = pluginManager;
        _breadcrumb                  = breadcrumb;
    }

    /// <summary>
    /// 父节点
    /// </summary>
    public CategoryNode CurrentCategory { get; set; } = CategoryNode.Empty;

    /// <summary>
    /// 用户选中的列表项
    /// </summary>
    [OnChangedMethod(nameof(OnSelectedNodeChanged))]
    public TreeNode? SelectedNode { get; set; }

    /// <summary>
    /// 用户选中的列表项, 更新右侧详细描述信息
    /// </summary>
    public void OnSelectedNodeChanged()
    {
        if (SelectedNode is PluginNode pluginNode)
        {
            return;
        }

        if (SelectedNode is CategoryNode categoryNode)
        {
            return;
        }
    }

    /// <summary>
    /// 用户双击, 加载命令
    /// </summary>
    public AsyncDelegateCommand<TreeNode> ItemDoubleClickCommand => GetPropertyCached(() => new AsyncDelegateCommand<TreeNode>(async (doubleClickNode, cancellationToken) =>
    {
        // 如果配置了该节点是一个插件, 我木目前会忽略后续的所有子项
        if (doubleClickNode is PluginNode plugin)
        {
            if (!plugin.IsInstalled)
            {
                await _pluginManager.InstallAsync(plugin.Id, plugin.Latest.SemVersion, cancellationToken);
            }

            var viewLoader = _pluginPageViewLoaderFactory.Create(plugin);

            // 这个是跳级点击, 因此要在面包屑中补一个节点
            _breadcrumb.Add(new BreadcrumbNode(CurrentCategory.Name,
                    new NavigationModel(_categoryPageViewLoader, new CategoryPageLoaderParameter() { Current = CurrentCategory, Selected = plugin })),
                true);

            await viewLoader.ShowAsync(RegionNames.PrimaryViewRegion, null, cancellationToken);
            return;
        }

        // 加载子目录
        await _categoryPageViewLoader.ShowAsync(RegionNames.PrimaryViewRegion,
            new CategoryPageLoaderParameter
            {
                Current  = CurrentCategory,
                Selected = SelectedNode,
            },
            cancellationToken);
    }));

    /// <summary>
    /// 获取插件
    /// </summary>
    public AsyncDelegateCommand<TreeNode> PluginShowDetailCommand => new(async (node, cancellationToken) =>
    {
        // 如果配置了该节点是一个插件, 我木目前会忽略后续的所有子项
        if (node is not PluginNode plugin)
        {
            await _messageBox.ShowErrorAsync("请选择一个插件");
            return;
        }

        // 加载子目录
        await _categoryPageViewLoader.ShowAsync(RegionNames.PrimaryViewRegion,
            new CategoryPageLoaderParameter
            {
                Current  = CurrentCategory,
                Selected = node,
            },
            cancellationToken);
    });

    public override Task OnNavigatedToAsync(NavigationContext navigationContext, CancellationToken cancellation)
    {
        if (!navigationContext.Parameters.TryGetValue("Category", out CategoryNode? category) || category == null)
        {
            throw new ArgumentException("没有分类对象");
        }

        CurrentCategory = category;

        return base.OnNavigatedToAsync(navigationContext, cancellation);
    }
}
