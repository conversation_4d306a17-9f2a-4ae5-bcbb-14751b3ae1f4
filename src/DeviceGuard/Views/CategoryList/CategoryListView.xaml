<UserControl x:Class="DeviceGuard.Shell.Views.CategoryList.CategoryListView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:categoryList="clr-namespace:DeviceGuard.Shell.Views.CategoryList"
             xmlns:converters="clr-namespace:DeviceGuard.Shell.Converters"
             xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
             mc:Ignorable="d"
             d:DataContext="{d:DesignInstance Type=categoryList:CategoryListViewModel}"
             d:DesignHeight="450"
             d:DesignWidth="800">
    <UserControl.Resources>
        <converters:PluginStateConverter x:Key="PluginStateConverter" />
    </UserControl.Resources>
    <!--插件显示-->
    <Grid Margin="10"
          Visibility="{Binding CurrentCategory, Converter={StaticResource DisplayNotNullElseCollapsedConverter}}">
        <ListBox
            ItemsSource="{Binding CurrentCategory.Children}"
            SelectedItem="{Binding SelectedNode}">
            <b:Interaction.Triggers>
                <b:EventTrigger EventName="MouseDoubleClick">
                    <b:InvokeCommandAction Command="{Binding ItemDoubleClickCommand}"
                                           CommandParameter="{Binding SelectedItem, RelativeSource={RelativeSource AncestorType=ListBox}}" />
                </b:EventTrigger>
            </b:Interaction.Triggers>
            <ListBox.ItemsPanel>
                <ItemsPanelTemplate>
                    <WrapPanel Orientation="Horizontal" />
                </ItemsPanelTemplate>
            </ListBox.ItemsPanel>
            <ListBox.ItemTemplate>
                <DataTemplate>
                    <Grid Margin="6" Width="250">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"></ColumnDefinition>
                            <ColumnDefinition Width="*"></ColumnDefinition>
                        </Grid.ColumnDefinitions>

                        <!--左侧图标-->
                        <materialDesign:PackIcon Kind="FolderOutline"
                                                 Visibility="{Binding IsCategoryNode, Converter={StaticResource DisplayTrueElseCollapsedConverter}}"
                                                 Grid.Column="0"
                                                 VerticalAlignment="Center" Width="40" Height="40"
                                                 Foreground="DimGray" />
                        <Image Grid.Column="0" Width="40" Height="40"
                               Visibility="{Binding IsCategoryNode, Converter={StaticResource DisplayFalseElseCollapsedConverter}}"
                               Source="{Binding Icon, TargetNullValue='pack://application:,,,/Resources/Undefined.ico', FallbackValue='pack://application:,,,/Resources/Undefined.ico', Mode=OneWay}">
                        </Image>

                        <!-- 右侧文本内容 -->
                        <StackPanel Grid.Column="1" Margin="15 0 0 0">
                            <TextBlock FontWeight="Bold" FontSize="15" Text="{Binding Name}" />
                            <Grid Margin="0 3 0 0"
                                  TextBlock.FontSize="13"
                                  TextBlock.Foreground="DimGray">
                                <StackPanel Orientation="Horizontal"
                                            Visibility="{Binding IsCategoryNode, Converter={StaticResource DisplayFalseElseCollapsedConverter}}">
                                    <TextBlock
                                        VerticalAlignment="Center"
                                        Text="{Binding Badge,Converter={StaticResource PluginStateConverter}, ConverterParameter='Text' }" />
                                    <TextBlock Margin="10 0 0 0">
                                        <Hyperlink NavigateUri="https://www.qq.com"
                                                   Foreground="#0000E6"
                                                   Command="{Binding DataContext.PluginShowDetailCommand, RelativeSource={RelativeSource AncestorType=ListBox}}"
                                                   CommandParameter="{Binding}">
                                            详情
                                        </Hyperlink>
                                    </TextBlock>
                                </StackPanel>
                                <StackPanel
                                    Visibility="{Binding IsCategoryNode, Converter={StaticResource DisplayTrueElseCollapsedConverter}}">
                                    <TextBlock Text="{Binding Brief}"
                                               TextWrapping="NoWrap"
                                               TextTrimming="CharacterEllipsis" />
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Grid>
                </DataTemplate>
            </ListBox.ItemTemplate>
        </ListBox>
    </Grid>
</UserControl>