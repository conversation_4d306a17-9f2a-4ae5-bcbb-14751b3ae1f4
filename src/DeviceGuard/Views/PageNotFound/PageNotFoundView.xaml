<UserControl x:Class="DeviceGuard.Shell.Views.PageNotFound.BlankView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d"
             d:DesignHeight="450"
             d:DesignWidth="800">
    <Grid>
        <StackPanel VerticalAlignment="Center"
                    HorizontalAlignment="Center">
            <TextBlock HorizontalAlignment="Center"
                       FontSize="18"
                       Margin="0 0 0 10"
                       Text="抱歉, 页面没找到" />
            <TextBlock HorizontalAlignment="Center"
                       Text="Sorry, page not found" />
        </StackPanel>
        <Border HorizontalAlignment="Left"
                VerticalAlignment="Bottom"
                Margin="20 0 0 20"
                Padding="10 7"
                BorderThickness="1">
            <Border.BorderBrush>
                <VisualBrush>
                    <VisualBrush.Visual>
                        <Rectangle StrokeThickness="1"
                                   StrokeDashArray="3 3"
                                   Width="{Binding RelativeSource={RelativeSource AncestorType={x:Type Border}}, Path=ActualWidth}"
                                   Height="{Binding RelativeSource={RelativeSource AncestorType={x:Type Border}}, Path=ActualHeight}" />
                    </VisualBrush.Visual>
                </VisualBrush>
            </Border.BorderBrush>
            <StackPanel Orientation="Horizontal">
                <materialDesign:PackIcon Margin="0 0 10 0"
                                         Width="24"
                                         Height="24"
                                         Kind="ArrowDownBoldHexagonOutline" />
                <TextBlock FontSize="18"
                           Text="选择功能并开始" />
            </StackPanel>
        </Border>
    </Grid>
</UserControl>