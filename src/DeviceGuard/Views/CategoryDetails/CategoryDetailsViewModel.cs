using DeviceGuard.ModuleCore.Plugins;
using DeviceGuard.Shell.Views.Pages.CategoryPage;
using DeviceGuard.Windows.Utility.Dialogs;
using DeviceGuard.Windows.Utility.Mvvm;

namespace DeviceGuard.Shell.Views.CategoryDetails;

public class CategoryDetailsViewModel : RegionViewModelBase
{
    private readonly IMessageBox            _messageBox;
    private readonly CategoryPageViewLoader _categoryPageViewLoader;

    public CategoryDetailsViewModel(
        IMessageBox            messageBox,
        CategoryPageViewLoader categoryPageViewLoader
    )
    {
        _messageBox             = messageBox;
        _categoryPageViewLoader = categoryPageViewLoader;
    }

    public CategoryNode Category { get; set; } = CategoryNode.Empty;

    /// <summary>
    /// 获取插件
    /// </summary>
    public AsyncDelegateCommand OpenCommand => new(async (cancellationToken) =>
    {
        if (Category.Id == CategoryNode.Empty.Id)
        {
            await _messageBox.ShowErrorAsync("没有分类对象");
            return;
        }

        await _categoryPageViewLoader.ShowAsync(RegionNames.PrimaryViewRegion, Category, cancellationToken);
    });

    public override Task OnNavigatedToAsync(NavigationContext navigationContext, CancellationToken cancellation)
    {
        if (!navigationContext.Parameters.TryGetValue("Category", out CategoryNode? category) || category == null)
        {
            throw new ArgumentException("没有分类对象");
        }

        Category = category;

        return base.OnNavigatedToAsync(navigationContext, cancellation);
    }
}
