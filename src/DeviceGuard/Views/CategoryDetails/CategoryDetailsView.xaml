<UserControl x:Class="DeviceGuard.Shell.Views.CategoryDetails.CategoryDetailsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:wpf="clr-namespace:CefSharp.Wpf;assembly=CefSharp.Wpf"
             xmlns:categoryDetails="clr-namespace:DeviceGuard.Shell.Views.CategoryDetails"
             mc:Ignorable="d"
             d:DataContext="{d:DesignInstance Type=categoryDetails:CategoryDetailsViewModel}"
             d:DesignHeight="450"
             d:DesignWidth="800">
    <!--插件显示-->
    <Grid Margin="20"
          Visibility="{Binding Category, Converter={StaticResource DisplayNotNullElseCollapsedConverter}}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
        </Grid.RowDefinitions>

        <StackPanel>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                    <ColumnDefinition Width="*"></ColumnDefinition>
                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                </Grid.ColumnDefinitions>

                <!--左侧图标-->
                <Border Background="#E5EAF2" CornerRadius="10" Padding="10">
                    <Image Width="64" Height="64"
                           Source="{Binding Category.Icon, TargetNullValue='pack://application:,,,/Resources/Undefined.ico',FallbackValue='pack://application:,,,/Resources/Undefined.ico', Mode=OneWay}">
                    </Image>
                </Border>

                <!--主要内容-->
                <StackPanel Grid.Column="1" Margin="30 0 0 0">
                    <StackPanel Orientation="Horizontal" Margin="0 0 0 10">
                        <TextBlock FontSize="20" Text="{Binding Category.Name, FallbackValue=Unknown, Mode=OneWay}">
                        </TextBlock>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0 0 0 10">
                        <TextBlock
                            Visibility="{Binding Category.Url, Converter={StaticResource DisplayNotNullElseCollapsedConverter}}">
                            <Hyperlink
                                NavigateUri="{Binding Category.Url, FallbackValue=about:blank, TargetNullValue=about:blank}">
                                <Run Text="{Binding Category.Url,  Mode=OneWay}"></Run>
                            </Hyperlink>
                        </TextBlock>
                        <TextBlock Foreground="DimGray"
                                   Visibility="{Binding Category.Url, Converter={StaticResource DisplayNullElseCollapsedConverter}}"
                                   Text="文档正在加紧制作中...">
                        </TextBlock>
                    </StackPanel>
                </StackPanel>

                <!--右侧按钮-->
                <StackPanel Grid.Column="2" Orientation="Horizontal" Margin="0 0 0 0" VerticalAlignment="Top">
                    <Button
                        Content="打开目录"
                        Margin="10 0 0 0"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        Command="{Binding OpenCommand}"
                        ToolTip="打开分类" />
                </StackPanel>

            </Grid>
            <TextBlock Margin="0 20 0 0" Text="{Binding Category.Brief, FallbackValue=描述正在加紧制作中..., Mode=OneWay}" />
            <Separator Margin="0 20"></Separator>
        </StackPanel>
        <wpf:ChromiumWebBrowser Grid.Row="1"
                                Address="{Binding Category.Url, FallbackValue=about:blank, TargetNullValue=about:blank}" />
    </Grid>
</UserControl>