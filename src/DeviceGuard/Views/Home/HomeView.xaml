<UserControl x:Class="DeviceGuard.Shell.Views.Home.HomeView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:regions="http://prismlibrary.com/"
             xmlns:home="clr-namespace:DeviceGuard.Shell.Views.Home"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:prism="clr-namespace:DeviceGuard.Windows.Utility.Prism;assembly=DeviceGuard.Windows.Utility"
             xmlns:core="clr-namespace:DeviceGuard.Windows.Utility.Mvvm;assembly=DeviceGuard.Windows.Utility"
             xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
             regions:ViewModelLocator.AutoWireViewModel="True"
             prism:ViewModelBinder.BindingView="True"
             mc:Ignorable="d"
             Background="#F7f8fa"
             d:DesignHeight="450"
             d:DesignWidth="700"
             d:DataContext="{d:DesignInstance home:HomeViewModel }">
    <Grid>

        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>

        <!-- 左侧菜单 -->
        <Grid Grid.Column="0" Margin="0">
            <Grid.RowDefinitions>
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <ListBox
                Grid.Row="0"
                materialDesign:ListBoxAssist.IsToggle="False"
                ItemsSource="{Binding TopButtons}"
                SelectedItem="{Binding TopSelectionButton}"
                VerticalAlignment="Stretch">
                <b:Interaction.Triggers>
                    <b:EventTrigger EventName="SelectionChanged">
                        <b:InvokeCommandAction Command="{Binding ButtonClickCommand}"
                                               CommandParameter="{Binding TopSelectionButton}" />
                    </b:EventTrigger>
                </b:Interaction.Triggers>
                <ListBox.Resources>
                    <Style TargetType="materialDesign:Ripple">
                        <Setter Property="IsEnabled" Value="False" />
                    </Style>
                </ListBox.Resources>
                <ListBox.ItemTemplate>
                    <DataTemplate>
                        <StackPanel Margin="10"
                                    ToolTip="{Binding Name}"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center">
                            <materialDesign:PackIcon Kind="{Binding Icon}"
                                                     Width="32"
                                                     Height="32" />
                        </StackPanel>
                    </DataTemplate>
                </ListBox.ItemTemplate>
            </ListBox>

            <!-- 底部菜单 -->
            <ListBox
                Grid.Row="1"
                materialDesign:ListBoxAssist.IsToggle="False"
                ItemsSource="{Binding BottomButtons}"
                SelectedItem="{Binding BottomSelectionButton}"
                VerticalAlignment="Stretch">
                <b:Interaction.Triggers>
                    <b:EventTrigger EventName="SelectionChanged">
                        <b:InvokeCommandAction Command="{Binding ButtonClickCommand}"
                                               CommandParameter="{Binding BottomSelectionButton}" />
                    </b:EventTrigger>
                </b:Interaction.Triggers>
                <ListBox.Resources>
                    <Style TargetType="materialDesign:Ripple">
                        <Setter Property="IsEnabled" Value="False" />
                    </Style>
                </ListBox.Resources>
                <ListBox.ItemTemplate>
                    <DataTemplate>
                        <StackPanel Margin="10"
                                    ToolTip="{Binding Name}"
                                    HorizontalAlignment="Center"
                                    VerticalAlignment="Center">
                            <materialDesign:PackIcon Kind="{Binding Icon}"
                                                     Width="32"
                                                     Height="32" />
                        </StackPanel>
                    </DataTemplate>
                </ListBox.ItemTemplate>
            </ListBox>
        </Grid>

        <!-- 右侧 -->
        <Grid Grid.Column="1" >
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <Grid Grid.Row="0" Margin="0 15 20 15">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                    <ColumnDefinition Width="*"></ColumnDefinition>
                </Grid.ColumnDefinitions>

                <!-- 面包屑导航 -->
                <ItemsControl Grid.Column="1" ItemsSource="{Binding Breadcrumb.Nodes}"
                              Margin="15 0 0 0">
                    <ItemsControl.Style>
                        <Style TargetType="ItemsControl">
                            <Style.Triggers>
                                <DataTrigger Binding="{Binding Breadcrumb.Nodes.Count}" Value="0">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate>
                                                <TextBlock Text="暂无导航" Foreground="LightGray" />
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </DataTrigger>
                            </Style.Triggers>
                        </Style>
                    </ItemsControl.Style>
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <StackPanel Orientation="Horizontal" />
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>
                    <ItemsControl.ItemTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text=">"
                                           VerticalAlignment="Center"
                                           Margin="10 0"
                                           Visibility="{Binding IsRoot, Converter={StaticResource DisplayFalseElseCollapsedConverter}}">
                                </TextBlock>
                                <TextBlock
                                    Style="{StaticResource MaterialDesignSubtitle1TextBlock}">
                                    <Hyperlink
                                        Command="{Binding DataContext.BreadcrumbNavigateCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                        CommandParameter="{Binding}">
                                        <Run Text="{Binding Name, Mode=OneWay}"></Run>
                                    </Hyperlink>
                                </TextBlock>
                            </StackPanel>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                </ItemsControl>
            </Grid>

            <!--内容呈现-->
            <Border Grid.Row="1" Background="White" CornerRadius="10 0 0 0">
                <ContentControl Margin="15"
                                regions:RegionManager.RegionName="{x:Static core:RegionNames.PrimaryViewRegion}" />
            </Border>
        </Grid>
    </Grid>
</UserControl>