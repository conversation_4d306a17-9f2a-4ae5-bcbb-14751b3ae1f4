using DeviceGuard.Interface;
using DeviceGuard.ModuleCore.Plugins;
using DeviceGuard.Shell.Interface.Breadcrumbs;
using DeviceGuard.Shell.Interface.Pages;
using DeviceGuard.Windows.Utility;
using DeviceGuard.Windows.Utility.Dialogs;
using DeviceGuard.Windows.Utility.Mvvm;
using PropertyChanged;
using Serilog;

namespace DeviceGuard.Shell.Views.Home;

/// <summary>
/// 首页视图
/// </summary>
public class HomeViewModel : RegionViewModelBase
{
    private readonly IMainWindowTitle _windowTitle;
    private readonly ILogger          _logger;
    private readonly IMessageBox      _messageBox;
    private readonly IPluginManager   _pluginManager;

    private readonly IUpdateManager _updateManager;

    public HomeViewModel(
        IEnumerable<IPrimaryButton> buttons,
        IMainWindowTitle            windowTitle,
        ILogger                     logger,
        IMessageBox                 messageBox,
        IBreadcrumb                 breadcrumb,
        IPluginManager              pluginManager,
        IUpdateManager              updateManager)
    {
        Breadcrumb     = breadcrumb;
        _pluginManager = pluginManager;
        _updateManager = updateManager;
        _windowTitle   = windowTitle;
        _logger        = logger;
        _messageBox    = messageBox;

        Buttons = buttons.OrderBy(x => x.Index).ToArray();
        SetSelectionButton(Buttons.First(x => x.ButtonPosition == ButtonPosition.Top));
    }

    /// <summary>
    /// 面包屑
    /// </summary>
    public IBreadcrumb Breadcrumb { get; }

    public override async Task OnLoadedAsync(CancellationToken cancellation)
    {
        try
        {
            // 初始化插件数据库
            await _pluginManager.InitializeAsync(cancellation);

            // 启动后打开首页
            await NavigateAsync(new NavigationModel(SelectionButton), cancellation);

            // 执行应用程序更新逻辑
            var pendingVersion = await _updateManager.GetUpdatePendingVersionAsync(cancellation);
            if (!string.IsNullOrEmpty(pendingVersion))
            {
                if (await _messageBox.ShowQuestionAsync($"新版本 {pendingVersion} 已下载至本地，是否安装？") == true)
                {
                    await _updateManager.InstallUpdatesAsync(cancellation);
                    return;
                }
            }

            // 下载最新程序, 为下次启动更新做准备
            await _updateManager.DownloadUpdatesAsync(cancellation);
        }
        catch (Exception e)
        {
            await _messageBox.ShowExceptionAsync(e, "初始化插件管理器错误");
        }
    }

    public AsyncDelegateCommand<BreadcrumbNode> BreadcrumbNavigateCommand => GetPropertyCached(() =>
        new AsyncDelegateCommand<BreadcrumbNode>(async (model, cancellationToken) =>
        {
            Breadcrumb.RemoveAfter(model);
            await NavigateAsync(model.Navigation, cancellationToken);
        }));

    #region 功能菜单

    public IPrimaryButton[] Buttons { get; set; }

    /// <summary>
    /// 当前选择的按钮
    /// </summary>
    /// <exception cref="InvalidOperationException"></exception>
    public void SetSelectionButton(IPrimaryButton value)
    {
        if (value == null) throw new ArgumentNullException(nameof(value));
        if (value.ButtonPosition == ButtonPosition.Top)
        {
            TopSelectionButton = value;
        }
        else
        {
            BottomSelectionButton = value;
        }
    }

    public IPrimaryButton SelectionButton => TopSelectionButton ?? BottomSelectionButton ?? throw new InvalidOperationException("没有选择按钮");

    #region 上下菜单内部实现

    public IPrimaryButton[] TopButtons => Buttons.Where(x => x.ButtonPosition == ButtonPosition.Top).ToArray();

    public IPrimaryButton[] BottomButtons => Buttons.Where(x => x.ButtonPosition == ButtonPosition.Bottom).ToArray();

    /// <summary>
    /// 当前选择的页
    /// </summary>
    [OnChangedMethod(nameof(OnTopSelectionButtonChanged))]
    public IPrimaryButton? TopSelectionButton { get; set; }

    [OnChangedMethod(nameof(OnBottomSelectionButtonChanged))]
    public IPrimaryButton? BottomSelectionButton { get; set; }

    /// <summary>
    /// 切换显示页
    /// </summary>
    public void OnTopSelectionButtonChanged()
    {
        if (TopSelectionButton == null)
            return;

        BottomSelectionButton = null;
    }

    public void OnBottomSelectionButtonChanged()
    {
        if (BottomSelectionButton == null)
            return;

        TopSelectionButton = null;
    }

    #endregion

    public AsyncDelegateCommand<IPrimaryButton> ButtonClickCommand => GetPropertyCached(() =>
        new AsyncDelegateCommand<IPrimaryButton>(async (model, cancellationToken) =>
        {
            await NavigateAsync(new NavigationModel(model), cancellationToken);
        }));

    #endregion

    #region 页面导航

    /// <summary>
    /// 导航到指定的页面
    /// </summary>
    /// <param name="navigation"></param>
    /// <param name="cancellationToken"></param>
    /// <exception cref="ArgumentNullException"></exception>
    public async Task NavigateAsync(NavigationModel navigation, CancellationToken cancellationToken)
    {
        if (navigation == null) throw new ArgumentNullException(nameof(navigation));
        using var currentCursor = DisposableWaitCursor.WaitCursor();

        await navigation.Loader.ShowAsync(RegionNames.PrimaryViewRegion, navigation.Parameter, cancellationToken);

        _windowTitle.ChangeTitle(navigation.Loader.Name, "");

        // _config.LatestPageName = current.View.Name;
        _logger.Information("Page {PageName} has been activated", navigation.Loader.Name);
    }

    #endregion
}
