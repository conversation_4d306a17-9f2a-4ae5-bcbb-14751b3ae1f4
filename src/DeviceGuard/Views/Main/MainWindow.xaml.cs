using System.Windows.Controls;
using Hotwheels.Wpf;

namespace DeviceGuard.Shell.Views.Main;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow
{
    public MainWindow()
    {
        InitializeComponent();

        DispatcherHelper.Initialize();
    }


    public MainWindowViewModel ViewModel => (MainWindowViewModel)DataContext;

    public override void OnApplyTemplate()
    {
        // 调整 MetroWindow 的默认的遮盖, 默认遮盖弹出对话框会遮盖了标题条, 导致无法拖动, 以下代码调整解决这个问题
        var parts = new[] { "PART_MetroInactiveDialogsContainer", "PART_OverlayBox", "PART_MetroActiveDialogContainer" };
        foreach (var part in parts)
        {
            if (GetTemplateChild(part) is { } obj)
            {
                obj.SetCurrentValue(Grid.RowProperty,     2);
                obj.SetCurrentValue(Grid.RowSpanProperty, 1);
            }
        }

        base.OnApplyTemplate();
    }
}
