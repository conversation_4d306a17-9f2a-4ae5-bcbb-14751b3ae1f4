using System.Reflection;
using DeviceGuard.Interface.Cloud;
using DeviceGuard.Shell.Views.Home;
using DeviceGuard.Windows.Utility;
using DeviceGuard.Windows.Utility.Mvvm;
using MaterialDesignThemes.Wpf;

namespace DeviceGuard.Shell.Views.Main;

public class MainWindowViewModel : ViewModelBase, IMainWindowTitle
{
    public ISnackbarMessageQueue MessageQueue { get; }

    private readonly IRegionManager _regionManager;

    public MainWindowViewModel(
        IRegionManager        regionManager,
        ISnackbarMessageQueue messageQueue)
    {
        MessageQueue   = messageQueue;
        _regionManager = regionManager;
    }

    public override Task OnLoadedAsync(CancellationToken cancellation)
    {
        ChangeTitle("", "");
        _regionManager.RequestNavigate(RegionNames.MainWindowRegion, nameof(HomeView));
        
        return Task.CompletedTask;
    }

    #region Implementation of IMainWindowTitle

    /// <summary>
    /// 标题
    /// </summary>
    public string Title { get; set; } = "DEVICE GUARD";

    private string _lastSubTitle = "";

    /// <summary>
    /// 更改窗口标题
    /// </summary>
    /// <param name="title"></param>
    /// <param name="description"></param>
    public void ChangeTitle(string title, string description)
    {
        _lastSubTitle = title;

        if (!string.IsNullOrEmpty(description))
            description = $"{description} - ";

        if (!string.IsNullOrEmpty(title))
            title = $"{title} - ";

        // 版本
        var ver     = Assembly.GetExecutingAssembly().GetName().Version;
        var verText = ver != null ? $" v{ver.Major}.{ver.Minor}.{ver.Build}" : string.Empty;

        Title = $"{description}{title}DEVICE GUARD {verText}";
    }

    /// <summary>
    /// 更改窗口标题的描述部分
    /// </summary>
    /// <param name="description"></param>
    public void ChangeDescription(string description)
    {
        ChangeTitle(_lastSubTitle, description);
    }

    #endregion
}
