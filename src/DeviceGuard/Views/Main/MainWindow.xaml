<mah:MetroWindow x:Class="DeviceGuard.Shell.Views.Main.MainWindow"
                 xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                 xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
                 xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
                 xmlns:styles="clr-namespace:DeviceGuard.Styles;assembly=DeviceGuard.Styles"
                 xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                 xmlns:main="clr-namespace:DeviceGuard.Shell.Views.Main"
                 xmlns:mvvm="http://prismlibrary.com/"
                 xmlns:prism="clr-namespace:DeviceGuard.Windows.Utility.Prism;assembly=DeviceGuard.Windows.Utility"
                 xmlns:core="clr-namespace:DeviceGuard.Windows.Utility.Mvvm;assembly=DeviceGuard.Windows.Utility"
                 mc:Ignorable="d"
                 mvvm:ViewModelLocator.AutoWireViewModel="True"
                 prism:ViewModelBinder.BindingView="True"
                 SnapsToDevicePixels="True"
                 Title="{Binding Title}"
                 TitleCharacterCasing="Normal"
                 GlowBrush="{DynamicResource MahApps.Brushes.Accent}"
                 BorderThickness="1"
                 TitleBarHeight="36"
                 WindowStartupLocation="CenterScreen"
                 FontFamily="Microsoft YaHei"
                 FontSize="14"
                 Height="800"
                 Width="1400"
                 d:DataContext="{d:DesignInstance main:MainWindowViewModel}">
    <mah:MetroWindow.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!--Design time style resources-->
                <styles:DesignTimeResourceDictionary
                    Source="pack://application:,,,/DeviceGuard.Styles;component/AppStyle.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </mah:MetroWindow.Resources>
    <Grid>
        <ContentControl mvvm:RegionManager.RegionName="{x:Static core:RegionNames.MainWindowRegion}" />
        <materialDesign:Snackbar MessageQueue="{Binding MessageQueue}"
                                 d:Message="Hello World"
                                 d:IsActive="True" />
    </Grid>
</mah:MetroWindow>