<UserControl x:Class="DeviceGuard.Shell.Views.Pages.RecentPage.RecentPageView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
             xmlns:recentPage="clr-namespace:DeviceGuard.Shell.Views.Pages.RecentPage"
             xmlns:prism="clr-namespace:DeviceGuard.Windows.Utility.Prism;assembly=DeviceGuard.Windows.Utility"
             xmlns:regions="http://prismlibrary.com/"
             xmlns:converters="clr-namespace:DeviceGuard.Shell.Converters"
             prism:ViewModelBinder.BindingView="True"
             mc:Ignorable="d"
             d:DesignHeight="450"
             d:DesignWidth="800"
             d:DataContext="{d:DesignInstance recentPage:RecentPageViewModel}">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="350" />
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>

        <!--左侧分类列表-->
        <Border Grid.Column="0"
                BorderBrush="LightGray"
                BorderThickness="1">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                </Grid.RowDefinitions>
                <ListBox ItemsSource="{Binding Recents}"
                         HorizontalContentAlignment="Stretch"
                         SelectedItem="{Binding SelectedNode}">
                    <b:Interaction.Triggers>
                        <b:EventTrigger EventName="MouseDoubleClick">
                            <b:InvokeCommandAction Command="{Binding ItemDoubleClickCommand}"
                                                   CommandParameter="{Binding SelectedItem, RelativeSource={RelativeSource AncestorType=ListBox}}" />
                        </b:EventTrigger>
                    </b:Interaction.Triggers>

                    <ListBox.Resources>
                        <Style TargetType="materialDesign:Ripple">
                            <Setter Property="IsEnabled" Value="False" />
                        </Style>
                    </ListBox.Resources>
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <Grid Margin="6">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    <ColumnDefinition Width="*"></ColumnDefinition>
                                </Grid.ColumnDefinitions>

                                <!--左侧图标-->
                                <materialDesign:PackIcon Kind="FolderOutline"
                                                         Visibility="{Binding IsCategoryNode, Converter={StaticResource DisplayTrueElseCollapsedConverter}}"
                                                         Grid.Column="0"
                                                         VerticalAlignment="Center" Width="40" Height="40"
                                                         Foreground="DimGray" />
                                <Image Grid.Column="0" Width="40" Height="40"
                                       Visibility="{Binding IsCategoryNode, Converter={StaticResource DisplayFalseElseCollapsedConverter}}"
                                       Source="{Binding Icon, TargetNullValue='pack://application:,,,/Resources/Undefined.ico', FallbackValue='pack://application:,,,/Resources/Undefined.ico', Mode=OneWay}">
                                </Image>

                                <!-- 右侧文本内容 -->
                                <StackPanel Grid.Column="1" Margin="15 0 0 0">
                                    <Grid>
                                        <Grid.Resources>
                                            <converters:PluginStateConverter x:Key="PluginStateConverter" />
                                        </Grid.Resources>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"></ColumnDefinition>
                                            <ColumnDefinition Width="Auto"></ColumnDefinition>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" FontWeight="Bold" FontSize="15"
                                                   Text="{Binding Name}" />
                                        <TextBlock Grid.Column="1"
                                                   Padding="3 2"
                                                   VerticalAlignment="Center"
                                                   FontSize="10"
                                                   Background="{Binding Badge, Converter={StaticResource PluginStateConverter}, ConverterParameter='Background' }"
                                                   Foreground="{Binding Badge, Converter={StaticResource PluginStateConverter}, ConverterParameter='Foreground' }"
                                                   Text="{Binding Badge,Converter={StaticResource PluginStateConverter}, ConverterParameter='Text' }" />
                                    </Grid>
                                    <TextBlock Foreground="Gray" FontSize="13" Text="{Binding Brief}" Margin="0 5" />
                                </StackPanel>
                            </Grid>

                        </DataTemplate>
                    </ListBox.ItemTemplate>
                </ListBox>
                <StackPanel Grid.Row="1">
                    <Separator Background="LightGray"></Separator>
                    <StackPanel Orientation="Horizontal">
                        <Button Content="清除"
                                Style="{StaticResource MaterialDesignOutlinedDarkButton}"
                                Command="{Binding RemoveSelectionCommand}"
                                Margin="10 10 0 10" />
                        <Button Content="全部清除"
                                Padding="5 0"
                                Style="{StaticResource MaterialDesignFlatDarkButton}"
                                Command="{Binding RemoveAllCommand}"
                                Margin="10 10 0 10" />
                    </StackPanel>
                </StackPanel>
            </Grid>
        </Border>

        <GridSplitter Grid.Column="1" Width="10" Background="White" HorizontalAlignment="Stretch"></GridSplitter>

        <Border Grid.Column="2" BorderThickness="1" BorderBrush="LightGray">
            <ContentControl regions:RegionManager.RegionName="{Binding DetailsRegionName}"></ContentControl>
        </Border>
    </Grid>
</UserControl>