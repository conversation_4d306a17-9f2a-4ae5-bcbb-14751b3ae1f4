using System.Collections.ObjectModel;
using System.ComponentModel;
using DeviceGuard.ModuleCore.Plugins;
using DeviceGuard.Shell.Configs;
using DeviceGuard.Shell.Views.CategoryDetails;
using DeviceGuard.Shell.Views.Pages.PluginPage;
using DeviceGuard.Shell.Views.PluginDetails;
using DeviceGuard.Windows.Utility.Mvvm;
using PropertyChanged;

namespace DeviceGuard.Shell.Views.Pages.RecentPage;

public class RecentPageViewModel : RegionViewModelBase
{
    private readonly DeviceGuardConfig            _config;
    private readonly IPluginManager               _pluginManager;
    private readonly IRegionManager               _regionManager;
    private readonly IPluginPageViewLoaderFactory _pluginPageViewLoaderFactory;

    public RecentPageViewModel(
        DeviceGuardConfig            config,
        IPluginManager               pluginManager,
        IRegionManager               regionManager,
        IPluginPageViewLoaderFactory pluginPageViewLoaderFactory)
    {
        _config                      = config;
        _pluginManager               = pluginManager;
        _regionManager               = regionManager;
        _pluginPageViewLoaderFactory = pluginPageViewLoaderFactory;

        Recents.CollectionChanged += (s, e) =>
        {
            OnPropertyChanged(new PropertyChangedEventArgs(nameof(CanRemoveAllCommand)));
        };
    }

    public string DetailsRegionName { get; } = Guid.NewGuid().ToString();


    public ObservableCollection<TreeNode> Recents { get; set; } = new();

    /// <summary>
    /// 用户选中的列表项
    /// </summary>
    [OnChangedMethod(nameof(OnSelectedNodeChanged))]
    public TreeNode? SelectedNode { get; set; }

    public override Task OnLoadedAsync(CancellationToken cancellation)
    {
        var recentIds = _config.RecentPlugins;
        var recents   = new List<TreeNode>();
        foreach (var recentId in recentIds)
        {
            if (string.IsNullOrEmpty(recentId))
                continue;

            var plugin = _pluginManager.GetPluginAsync(recentId, CancellationToken.None).Result;
            if (plugin is null)
                continue;

            recents.Add(plugin);
        }

        Recents.Clear();
        Recents.AddRange(recents);

        return Task.CompletedTask;
    }


    /// <summary>
    /// 用户选中的列表项, 更新右侧详细描述信息
    /// </summary>
    public void OnSelectedNodeChanged()
    {
        if (SelectedNode is PluginNode pluginNode)
        {
            var p = new NavigationParameters { { "Plugin", pluginNode } };
            _regionManager.RequestNavigate(DetailsRegionName, nameof(PluginDetailsView), p);
            return;
        }

        // 清除 
        _regionManager.Regions[DetailsRegionName].RemoveAll();
    }

    /// <summary>
    /// 用户双击, 加载命令
    /// </summary>
    public AsyncDelegateCommand<TreeNode> ItemDoubleClickCommand => GetPropertyCached(() =>
        new AsyncDelegateCommand<TreeNode>(async (doubleClickNode, cancellationToken) =>
        {
            // 如果配置了该节点是一个插件, 我木目前会忽略后续的所有子项
            if (doubleClickNode is PluginNode plugin)
            {
                if (!plugin.IsInstalled)
                {
                    await _pluginManager.InstallAsync(plugin.Id,
                        plugin.Latest.SemVersion,
                        cancellationToken);
                }

                var viewLoader = _pluginPageViewLoaderFactory.Create(plugin);
                await viewLoader.ShowAsync(RegionNames.PrimaryViewRegion, null, cancellationToken);
                return;
            }
        }));

    public DelegateCommand RemoveAllCommand => GetPropertyCached(() => new DelegateCommand(() =>
    {
        Recents.Clear();
        _config.RecentPlugins = [];
    }).ObservesCanExecute(() => CanRemoveAllCommand));

    public bool CanRemoveAllCommand => Recents.Any();


    public DelegateCommand RemoveSelectionCommand => GetPropertyCached(() => new DelegateCommand(() =>
    {
        if (SelectedNode is PluginNode pluginNode)
        {
            Recents.Remove(pluginNode);
            _config.RecentPlugins = Recents.Select(x => x.Id).ToArray();
        }
    }).ObservesCanExecute(() => CanRemoveSelectionCommand));

    public bool CanRemoveSelectionCommand => SelectedNode != null;
}
