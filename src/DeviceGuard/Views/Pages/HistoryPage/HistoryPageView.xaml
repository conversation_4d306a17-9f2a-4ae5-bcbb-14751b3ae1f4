<UserControl x:Class="DeviceGuard.Shell.Views.Pages.HistoryPage.HistoryPageView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
             xmlns:historyPage="clr-namespace:DeviceGuard.Shell.Views.Pages.HistoryPage"
             xmlns:prism="clr-namespace:DeviceGuard.Windows.Utility.Prism;assembly=DeviceGuard.Windows.Utility"
             prism:ViewModelBinder.BindingView="True"
             mc:Ignorable="d"
             d:DesignHeight="450"
             d:DesignWidth="800"
             d:DataContext="{d:DesignInstance historyPage:HistoryPageViewModel}">
    <Grid>
        <Viewbox HorizontalAlignment="Center" VerticalAlignment="Center" MaxWidth="300">
            <TextBlock>Operation Log</TextBlock>
        </Viewbox>
    </Grid>
</UserControl>