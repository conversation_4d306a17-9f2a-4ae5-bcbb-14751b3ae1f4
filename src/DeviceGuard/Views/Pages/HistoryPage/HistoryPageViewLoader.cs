using DeviceGuard.Shell.Interface.Breadcrumbs;
using DeviceGuard.Shell.Interface.Pages;
using MaterialDesignThemes.Wpf;

namespace DeviceGuard.Shell.Views.Pages.HistoryPage;

/// <summary>
/// 最近访问
/// </summary>
public class HistoryPageViewLoader : IPrimaryButton
{
    private readonly IRegionManager _regionManager;
    private readonly IBreadcrumb    _breadcrumb;

    public HistoryPageViewLoader(IRegionManager regionManager, IBreadcrumb breadcrumb)
    {
        _regionManager = regionManager;
        _breadcrumb    = breadcrumb;
    }


    public Task ShowAsync(string regionName, object? parameters, CancellationToken cancellationToken)
    {
        var p = new NavigationParameters();
        if (parameters != null)
        {
            p.Add("Parameters", parameters);
        }

        _regionManager.RequestNavigate(regionName, nameof(HistoryPageView), p);
        _breadcrumb.Clear();
        _breadcrumb.Add(new BreadcrumbNode(Name, new NavigationModel(this)), true);
        return Task.CompletedTask;
    }

    public string Id => GetType().Name;

    public string         Name           => "历史记录";
    public string         Tooltip        => "查看测量历史";
    public string         Icon           => PackIconKind.FileDocumentOutline.ToString();
    public int            Index          => 3;
    public ButtonPosition ButtonPosition => ButtonPosition.Top;

    #region 比较器

    protected bool Equals(HistoryPageViewLoader other)
    {
        return Id.Equals(other.Id);
    }

    public override bool Equals(object? obj)
    {
        if (obj is null) return false;
        if (ReferenceEquals(this, obj)) return true;
        if (obj.GetType() != GetType()) return false;
        return Equals((HistoryPageViewLoader)obj);
    }

    public override int GetHashCode()
    {
        return Id.GetHashCode();
    }

    #endregion
}
