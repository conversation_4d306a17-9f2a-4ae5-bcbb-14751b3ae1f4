using DeviceGuard.Windows.Utility.Dialogs;
using DeviceGuard.Windows.Utility.Mvvm;

namespace DeviceGuard.Shell.Views.Pages.SearchPage;

public class SearchPageViewModel : RegionViewModelBase
{
    private readonly SearchPageViewLoader _pageViewPageViewLoader;
    private readonly IMessageBox          _messageBox;

    public SearchPageViewModel(SearchPageViewLoader pageViewPageViewLoader, IMessageBox messageBox)
    {
        _pageViewPageViewLoader = pageViewPageViewLoader;
        _messageBox             = messageBox;
    }

    public string InputText { get; set; } = "";

    public AsyncDelegateCommand SearchCommand => new(async (cancellationToken) =>
    {
        if (string.IsNullOrWhiteSpace(InputText))
        {
            await _messageBox.ShowWarningAsync("请输入搜索内容");
            return;
        }

        await _pageViewPageViewLoader.ShowAsync(RegionNames.PrimaryViewRegion, InputText, cancellationToken);
    });
}
