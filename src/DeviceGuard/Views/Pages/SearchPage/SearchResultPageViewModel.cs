using System.Collections.ObjectModel;
using DeviceGuard.ModuleCore.Plugins;
using DeviceGuard.Shell.Views.Pages.PluginPage;
using DeviceGuard.Shell.Views.PluginDetails;
using DeviceGuard.Windows.Utility.Mvvm;
using PropertyChanged;

namespace DeviceGuard.Shell.Views.Pages.SearchPage;

public class SearchResultPageViewModel : RegionViewModelBase
{
    private readonly IPluginManager               _pluginManager;
    private readonly IRegionManager               _regionManager;
    private readonly IPluginPageViewLoaderFactory _pluginPageViewLoaderFactory;

    public SearchResultPageViewModel(
        IPluginManager               pluginManager,
        IRegionManager               regionManager,
        IPluginPageViewLoaderFactory pluginPageViewLoaderFactory)
    {
        _pluginManager               = pluginManager;
        _regionManager               = regionManager;
        _pluginPageViewLoaderFactory = pluginPageViewLoaderFactory;
    }

    public string DetailsRegionName { get; } = Guid.NewGuid().ToString();


    public PluginNode[] Results { get; set; } = [];

    public override Task OnNavigatedToAsync(NavigationContext navigationContext, CancellationToken cancellation)
    {
        if (navigationContext.Parameters.TryGetValue("InputText", out string? inputText) && !string.IsNullOrEmpty(inputText))
        {
            InputText = inputText;

            // TODO 多页显示
            Results = _pluginManager.SearchAsync(new PluginSearchPredicate { Text = inputText }, CancellationToken.None).Result.Items;
        }
        else
        {
            //  TODO 显示所有记录
            // var task = Task.Run(() => _pluginManager.GetPluginTreeAsync(CancellationToken.None));
            // CurrentNode = task.Result;
        }

        return base.OnNavigatedToAsync(navigationContext, cancellation);
    }

    public string InputText { get; set; } = "";

    /// <summary>
    /// 用户选中的列表项
    /// </summary>
    [OnChangedMethod(nameof(OnSelectedNodeChanged))]
    public TreeNode? SelectedNode { get; set; }

    /// <summary>
    /// 用户选中的列表项, 更新右侧详细描述信息
    /// </summary>
    public void OnSelectedNodeChanged()
    {
        if (SelectedNode is PluginNode pluginNode)
        {
            var p = new NavigationParameters { { "Plugin", pluginNode } };
            _regionManager.RequestNavigate(DetailsRegionName, nameof(PluginDetailsView), p);
            return;
        }

        // 清除 
        _regionManager.Regions[DetailsRegionName].RemoveAll();
    }

    /// <summary>
    /// 用户双击, 加载命令
    /// </summary>
    public AsyncDelegateCommand<TreeNode> ItemDoubleClickCommand => GetPropertyCached(() =>
        new AsyncDelegateCommand<TreeNode>(async (doubleClickNode, cancellationToken) =>
        {
            // 如果配置了该节点是一个插件, 我木目前会忽略后续的所有子项
            if (doubleClickNode is PluginNode plugin)
            {
                if (!plugin.IsInstalled)
                {
                    await _pluginManager.InstallAsync(plugin.Id,
                        plugin.Latest.SemVersion,
                        cancellationToken);
                }

                var viewLoader = _pluginPageViewLoaderFactory.Create(plugin);
                await viewLoader.ShowAsync(RegionNames.PrimaryViewRegion, null, cancellationToken);
                return;
            }
        }));
}
