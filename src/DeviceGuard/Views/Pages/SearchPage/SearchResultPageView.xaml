<UserControl x:Class="DeviceGuard.Shell.Views.Pages.SearchPage.SearchResultPageView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:searchPage="clr-namespace:DeviceGuard.Shell.Views.Pages.SearchPage"
             xmlns:prism="clr-namespace:DeviceGuard.Windows.Utility.Prism;assembly=DeviceGuard.Windows.Utility"
             xmlns:b="http://schemas.microsoft.com/xaml/behaviors"
             xmlns:converters="clr-namespace:DeviceGuard.Shell.Converters"
             xmlns:regions="http://prismlibrary.com/"
             prism:ViewModelBinder.BindingView="True"
             mc:Ignorable="d"
             d:DesignHeight="450"
             d:DesignWidth="800"
             d:DataContext="{d:DesignInstance searchPage:SearchResultPageViewModel}">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="350" />
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>

        <!--左侧分类列表-->
        <Grid Grid.Column="0">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"></RowDefinition>
                <RowDefinition Height="*"></RowDefinition>
            </Grid.RowDefinitions>
            <Grid Grid.Column="0">
                <TextBox
                    FontSize="16"
                    Text="{Binding InputText}"
                    BorderThickness="1"
                    BorderBrush="LightGray"
                    materialDesign:HintAssist.Hint="请输入设备型号"
                    materialDesign:HintAssist.IsFloating="False"
                    Style="{StaticResource MaterialDesignOutlinedTextBox}" />

                <Button HorizontalAlignment="Right"
                        Margin="-80 10 10 10"
                        materialDesign:ButtonAssist.CornerRadius="5"
                        Style="{StaticResource MaterialDesignFlatDarkButton}"
                        Command="{Binding SearchCommand}">
                    <TextBlock Text="查找" />
                </Button>
            </Grid>
            <ListBox Grid.Row="1" ItemsSource="{Binding Results}"
                     BorderBrush="LightGray"
                     BorderThickness="1"
                     Margin="0 10 0 0"
                     HorizontalContentAlignment="Stretch"
                     SelectedItem="{Binding SelectedNode}">
                <b:Interaction.Triggers>
                    <b:EventTrigger EventName="MouseDoubleClick">
                        <b:InvokeCommandAction Command="{Binding ItemDoubleClickCommand}"
                                               CommandParameter="{Binding SelectedItem, RelativeSource={RelativeSource AncestorType=ListBox}}" />
                    </b:EventTrigger>
                </b:Interaction.Triggers>

                <ListBox.Resources>
                    <Style TargetType="materialDesign:Ripple">
                        <Setter Property="IsEnabled" Value="False" />
                    </Style>
                </ListBox.Resources>
                <ListBox.ItemTemplate>
                    <DataTemplate>
                        <Grid Margin="6">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"></ColumnDefinition>
                                <ColumnDefinition Width="*"></ColumnDefinition>
                            </Grid.ColumnDefinitions>

                            <!--左侧图标-->
                            <materialDesign:PackIcon Kind="FolderOutline"
                                                     Visibility="{Binding IsCategoryNode, Converter={StaticResource DisplayTrueElseCollapsedConverter}}"
                                                     Grid.Column="0"
                                                     VerticalAlignment="Center" Width="40" Height="40"
                                                     Foreground="DimGray" />
                            <Image Grid.Column="0" Width="40" Height="40"
                                   Visibility="{Binding IsCategoryNode, Converter={StaticResource DisplayFalseElseCollapsedConverter}}"
                                   Source="{Binding Icon, TargetNullValue='pack://application:,,,/Resources/Undefined.ico', FallbackValue='pack://application:,,,/Resources/Undefined.ico', Mode=OneWay}">
                            </Image>

                            <!-- 右侧文本内容 -->
                            <StackPanel Grid.Column="1" Margin="15 0 0 0">
                                <Grid>
                                    <Grid.Resources>
                                        <converters:PluginStateConverter x:Key="PluginStateConverter" />
                                    </Grid.Resources>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"></ColumnDefinition>
                                        <ColumnDefinition Width="Auto"></ColumnDefinition>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" FontWeight="Bold" FontSize="15"
                                               Text="{Binding Name}" />
                                    <TextBlock Grid.Column="1"
                                               Padding="3 2"
                                               VerticalAlignment="Center"
                                               FontSize="10"
                                               Background="{Binding Badge, Converter={StaticResource PluginStateConverter}, ConverterParameter='Background' }"
                                               Foreground="{Binding Badge, Converter={StaticResource PluginStateConverter}, ConverterParameter='Foreground' }"
                                               Text="{Binding Badge,Converter={StaticResource PluginStateConverter}, ConverterParameter='Text' }" />
                                </Grid>
                                <TextBlock Foreground="Gray" FontSize="13" Text="{Binding Brief}" Margin="0 5" />
                            </StackPanel>
                        </Grid>

                    </DataTemplate>
                </ListBox.ItemTemplate>
            </ListBox>
        </Grid>

        <GridSplitter Grid.Column="1" Width="10" Background="White" HorizontalAlignment="Stretch"></GridSplitter>

        <Border Grid.Column="2" BorderThickness="1" BorderBrush="LightGray">
            <ContentControl regions:RegionManager.RegionName="{Binding DetailsRegionName}"></ContentControl>
        </Border>
    </Grid>
</UserControl>