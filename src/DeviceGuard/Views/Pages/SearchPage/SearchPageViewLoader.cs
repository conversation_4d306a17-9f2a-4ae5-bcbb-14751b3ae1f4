using DeviceGuard.Shell.Interface.Breadcrumbs;
using DeviceGuard.Shell.Interface.Pages;
using MaterialDesignThemes.Wpf;

namespace DeviceGuard.Shell.Views.Pages.SearchPage;

public class SearchPageViewLoader : IPrimaryButton
{
    private readonly IRegionManager _regionManager;
    private readonly IBreadcrumb    _breadcrumb;

    public SearchPageViewLoader(IRegionManager regionManager, IBreadcrumb breadcrumb)
    {
        _regionManager = regionManager;
        _breadcrumb    = breadcrumb;
    }

    public Task ShowAsync(string regionName, object? parameters, CancellationToken cancellationToken)
    {
        var p = new NavigationParameters();
        if (parameters != null)
        {
            p.Add("InputText", parameters);
            _regionManager.RequestNavigate(regionName, nameof(SearchResultPageView), p);
            _breadcrumb.Add(new BreadcrumbNode($"{Name}结果", new NavigationModel(this, p)), true);
            return Task.CompletedTask;
        }

        _regionManager.RequestNavigate(regionName, nameof(SearchPageView), p);

        _breadcrumb.Clear();
        _breadcrumb.Add(new BreadcrumbNode(Name, new NavigationModel(this)), true);

        return Task.CompletedTask;
    }

    public string Id => GetType().Name;


    public string         Name           => "查找";
    public string         Tooltip        => "查找";
    public string         Icon           => PackIconKind.Magnify.ToString();
    public int            Index          => 2;
    public ButtonPosition ButtonPosition => ButtonPosition.Top;


    #region 比较器

    protected bool Equals(SearchPageViewLoader other)
    {
        return Id.Equals(other.Id);
    }

    public override bool Equals(object? obj)
    {
        if (obj is null) return false;
        if (ReferenceEquals(this, obj)) return true;
        if (obj.GetType() != GetType()) return false;
        return Equals((SearchPageViewLoader)obj);
    }

    public override int GetHashCode()
    {
        return Id.GetHashCode();
    }

    #endregion
}
