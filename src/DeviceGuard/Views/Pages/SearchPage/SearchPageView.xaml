<UserControl x:Class="DeviceGuard.Shell.Views.Pages.SearchPage.SearchPageView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:searchPage="clr-namespace:DeviceGuard.Shell.Views.Pages.SearchPage"
             xmlns:prism="clr-namespace:DeviceGuard.Windows.Utility.Prism;assembly=DeviceGuard.Windows.Utility"
             prism:ViewModelBinder.BindingView="True"
             mc:Ignorable="d"
             d:DesignHeight="450"
             d:DesignWidth="800"
             d:DataContext="{d:DesignInstance searchPage:SearchPageViewModel}">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="2*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="3*"></RowDefinition>
        </Grid.RowDefinitions>
        <StackPanel Grid.Row="1" VerticalAlignment="Center"
                    HorizontalAlignment="Center" Margin="0 0 0 0">
            <TextBlock HorizontalAlignment="Center"
                       FontSize="36"
                       Margin="0 0 0 50"
                       Text="查找设备" />
            <StackPanel Orientation="Horizontal">
                <TextBox
                    Width="700"
                    FontSize="16"
                    Text="{Binding InputText}"
                    materialDesign:HintAssist.Hint="请输入设备型号"
                    materialDesign:HintAssist.IsFloating="False"
                    materialDesign:TextFieldAssist.CharacterCounterVisibility="{Binding Path=IsChecked, ElementName=MaterialDesignOutlinedTextBoxTextCountComboBox, Converter={x:Static materialDesign:BooleanToVisibilityConverter.CollapsedInstance}}"
                    Style="{StaticResource MaterialDesignOutlinedTextBox}" />

                <Button
                    Margin="-80 10 10 10"
                    materialDesign:ButtonAssist.CornerRadius="5"
                    Style="{StaticResource MaterialDesignFlatDarkButton}"
                    Command="{Binding SearchCommand}"
                    >
                    <TextBlock Text="查找" />
                </Button>
            </StackPanel>
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0 50 0 0">
                <!-- <Button -->
                <!--     Style="{StaticResource MaterialDesignPaperLightButton}" -->
                <!--     Margin="0 0 50 0" -->
                <!--     ToolTip="Resource name: MaterialDesignPaperLightButton"> -->
                <!--     <StackPanel Orientation="Horizontal"> -->
                <!--         <materialDesign:PackIcon Kind="History" -->
                <!--                                  VerticalAlignment="Center" -->
                <!--                                  Margin="0 0 5 0" /> -->
                <!--         <TextBlock Text="最近访问"></TextBlock> -->
                <!--     </StackPanel> -->
                <!-- </Button> -->
                <!-- <Button -->
                <!--     Style="{StaticResource MaterialDesignPaperLightButton}" -->
                <!--     ToolTip="Resource name: MaterialDesignPaperLightButton"> -->
                <!--     <StackPanel Orientation="Horizontal"> -->
                <!--         <materialDesign:PackIcon Kind="Apps" -->
                <!--                                  VerticalAlignment="Center" -->
                <!--                                  Margin="0 0 5 0" /> -->
                <!--         <TextBlock Text="分类浏览"></TextBlock> -->
                <!--     </StackPanel> -->
                <!-- </Button> -->
            </StackPanel>
        </StackPanel>
    </Grid>
</UserControl>