using DeviceGuard.Shell.Interface.Breadcrumbs;
using DeviceGuard.Shell.Interface.Pages;
using MaterialDesignThemes.Wpf;

namespace DeviceGuard.Shell.Views.Pages.SettingsPage;

/// <summary>
/// 设置页加载
/// </summary>
public class SettingsPageViewLoader : IPrimaryButton
{
    private readonly IRegionManager _regionManager;
    private readonly IBreadcrumb    _breadcrumb;

    public SettingsPageViewLoader(IRegionManager regionManager, IBreadcrumb breadcrumb)
    {
        _regionManager = regionManager;
        _breadcrumb    = breadcrumb;
    }

    public Task ShowAsync(string regionName, object? parameters, CancellationToken cancellationToken)
    {
        var p = new NavigationParameters();
        if (parameters != null)
        {
            p.Add("Parameters", parameters);
        }

        _regionManager.RequestNavigate(regionName, nameof(SettingsPageView), p);

        _breadcrumb.Clear();
        _breadcrumb.Add(new BreadcrumbNode(Name, new NavigationModel(this)), true);

        return Task.CompletedTask;
    }

    public string Id => GetType().Name;

    public string         Name           => "设置";
    public string         Tooltip        => "设置系统参数";
    public string         Icon           => PackIconKind.CogOutline.ToString();
    public int            Index          => 1;
    public ButtonPosition ButtonPosition => ButtonPosition.Bottom;


    #region 比较器

    protected bool Equals(SettingsPageViewLoader other)
    {
        return Id.Equals(other.Id);
    }

    public override bool Equals(object? obj)
    {
        if (obj is null) return false;
        if (ReferenceEquals(this, obj)) return true;
        if (obj.GetType() != GetType()) return false;
        return Equals((SettingsPageViewLoader)obj);
    }

    public override int GetHashCode()
    {
        return Id.GetHashCode();
    }

    #endregion
}
