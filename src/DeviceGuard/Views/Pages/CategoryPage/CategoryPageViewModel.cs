using DeviceGuard.ModuleCore.Plugins;
using DeviceGuard.Shell.Views.CategoryDetails;
using DeviceGuard.Shell.Views.CategoryList;
using DeviceGuard.Shell.Views.Pages.PluginPage;
using DeviceGuard.Shell.Views.PluginDetails;
using DeviceGuard.Windows.Utility.Mvvm;
using PropertyChanged;

namespace DeviceGuard.Shell.Views.Pages.CategoryPage;

public class CategoryPageViewModel : RegionViewModelBase
{
    private readonly CategoryPageViewLoader       _pageViewLoader;
    private readonly IRegionManager               _regionManager;
    private readonly IPluginPageViewLoaderFactory _pluginPageViewLoaderFactory;
    private readonly IPluginManager               _pluginManager;

    public CategoryPageViewModel(IPluginManager pluginManager,
        CategoryPageViewLoader                  pageViewLoader,
        IRegionManager                          regionManager,
        IPluginPageViewLoaderFactory            pluginPageViewLoaderFactory)
    {
        _pluginManager               = pluginManager;
        _pageViewLoader              = pageViewLoader;
        _regionManager               = regionManager;
        _pluginPageViewLoaderFactory = pluginPageViewLoaderFactory;
    }

    public string DetailsRegionName { get; } = Guid.NewGuid().ToString();

    public override Task OnNavigatedToAsync(NavigationContext navigationContext, CancellationToken cancellation)
    {
        if (navigationContext.Parameters.TryGetValue("Parameters", out CategoryPageLoaderParameter? p) && p != null)
        {
            CurrentNode  = p.Current;
            SelectedNode = p.Selected;
        }
        else
        {
            var task = Task.Run(() => _pluginManager.GetPluginTreeAsync(cancellation), cancellation);
            CurrentNode = task.Result;
        }

        return base.OnNavigatedToAsync(navigationContext, cancellation);
    }

    /// <summary>
    /// 当前提供选择的列表
    /// </summary>
    public TreeNode? CurrentNode { get; set; }

    public TreeNode[] Children => CurrentNode is CategoryNode cn ? cn.Children.ToArray() : [];

    /// <summary>
    /// 用户选中的列表项
    /// </summary>
    [OnChangedMethod(nameof(OnSelectedNodeChanged))]
    public TreeNode? SelectedNode { get; set; }

    /// <summary>
    /// 用户选中的列表项, 更新右侧详细描述信息
    /// </summary>
    public void OnSelectedNodeChanged()
    {
        if (SelectedNode is PluginNode pluginNode)
        {
            var p = new NavigationParameters { { "Plugin", pluginNode } };
            _regionManager.RequestNavigate(DetailsRegionName, nameof(PluginDetailsView), p);
            return;
        }

        if (SelectedNode is CategoryNode categoryNode)
        {
            var p = new NavigationParameters { { "Category", categoryNode } };
            _regionManager.RequestNavigate(DetailsRegionName, nameof(CategoryListView), p);
            return;
        }

        // 清除 
        _regionManager.Regions[DetailsRegionName].RemoveAll();
    }

    /// <summary>
    /// 用户双击, 加载命令
    /// </summary>
    public AsyncDelegateCommand<TreeNode> ItemDoubleClickCommand => GetPropertyCached(() => new AsyncDelegateCommand<TreeNode>(async (doubleClickNode, cancellationToken) =>
    {
        // 如果配置了该节点是一个插件, 我木目前会忽略后续的所有子项
        if (doubleClickNode is PluginNode plugin)
        {
            if (!plugin.IsInstalled)
            {
                await _pluginManager.InstallAsync(plugin.Id, plugin.Latest.SemVersion, cancellationToken);
            }

            var viewLoader = _pluginPageViewLoaderFactory.Create(plugin);
            await viewLoader.ShowAsync(RegionNames.PrimaryViewRegion, null, cancellationToken);
            return;
        }

        // 加载子功能
        await _pageViewLoader.ShowAsync(RegionNames.PrimaryViewRegion, doubleClickNode, cancellationToken);
    }));
}
