using DeviceGuard.ModuleCore.Plugins;
using DeviceGuard.Shell.Interface.Breadcrumbs;
using DeviceGuard.Shell.Interface.Pages;
using MaterialDesignThemes.Wpf;

namespace DeviceGuard.Shell.Views.Pages.CategoryPage;

/// <summary>
/// 首页加载
/// </summary>
public class CategoryPageViewLoader : IPrimaryButton
{
    private readonly IRegionManager _regionManager;
    private readonly IBreadcrumb    _breadcrumb;

    /// <summary>
    /// 首页加载
    /// </summary>
    public CategoryPageViewLoader(IRegionManager regionManager, IBreadcrumb breadcrumb)
    {
        _regionManager = regionManager;
        _breadcrumb    = breadcrumb;
    }


    public Task ShowAsync(string regionName, object? parameters, CancellationToken cancellationToken)
    {
        if (parameters is TreeNode treeNode)
        {
            parameters = new CategoryPageLoaderParameter
            {
                Current = treeNode,
            };
        }

        var name = Name;
        if (parameters != null)
        {
            if (parameters is not CategoryPageLoaderParameter cp)
            {
                throw new ArgumentException("参数类型错误");
            }

            name = cp.Current.Name;
        }
        else
        {
            // 从根开始
            _breadcrumb.Clear();
        }

        var p = new NavigationParameters { };
        if (parameters is CategoryPageLoaderParameter cp2)
        {
            p.Add("Parameters", cp2);
        }

        _regionManager.RequestNavigate(regionName, nameof(CategoryPageView), p);
        _breadcrumb.Add(new BreadcrumbNode(name, new NavigationModel(this, parameters)), true);

        return Task.CompletedTask;
    }

    public string         Id             => GetType().Name;
    public string         Name           => "所有功能";
    public string         Tooltip        => "支持的所有插件";
    public string         Icon           => nameof(PackIconKind.HomeOutline);
    public int            Index          => 0;
    public ButtonPosition ButtonPosition => ButtonPosition.Top;

    #region 比较器

    protected bool Equals(CategoryPageViewLoader other)
    {
        return Id.Equals(other.Id);
    }

    public override bool Equals(object? obj)
    {
        if (obj is null) return false;
        if (ReferenceEquals(this, obj)) return true;
        if (obj.GetType() != GetType()) return false;
        return Equals((CategoryPageViewLoader)obj);
    }

    public override int GetHashCode()
    {
        return Id.GetHashCode();
    }

    #endregion
}