using DeviceGuard.Shell.Interface.Breadcrumbs;
using DeviceGuard.Shell.Interface.Pages;
using MaterialDesignThemes.Wpf;

namespace DeviceGuard.Shell.Views.Pages.MyAccountPage;

/// <summary>
/// 最近访问
/// </summary>
public class MyAccountPageViewLoader : IPrimaryButton
{
    private readonly IRegionManager _regionManager;
    private readonly IBreadcrumb    _breadcrumb;

    public MyAccountPageViewLoader(IRegionManager regionManager, IBreadcrumb breadcrumb)
    {
        _regionManager = regionManager;
        _breadcrumb    = breadcrumb;
    }

    public Task ShowAsync(string regionName, object? parameters, CancellationToken cancellationToken)
    {
        var p = new NavigationParameters();
        if (parameters != null)
        {
            p.Add("Parameters", parameters);
        }

        _regionManager.RequestNavigate(regionName, nameof(MyAccountPageView), p);
        _breadcrumb.Clear();
        _breadcrumb.Add(new BreadcrumbNode(Name, new NavigationModel(this)), true);
        return Task.CompletedTask;
    }


    public string         Id             => GetType().Name;
    public string         Name           => "我的";
    public string         Tooltip        => "查看我的账户信息";
    public string         Icon           => PackIconKind.AccountCircleOutline.ToString();
    public int            Index          => 0;
    public ButtonPosition ButtonPosition => ButtonPosition.Bottom;


    #region 比较器

    protected bool Equals(MyAccountPageViewLoader other)
    {
        return Id.Equals(other.Id);
    }

    public override bool Equals(object? obj)
    {
        if (obj is null) return false;
        if (ReferenceEquals(this, obj)) return true;
        if (obj.GetType() != GetType()) return false;
        return Equals((MyAccountPageViewLoader)obj);
    }

    public override int GetHashCode()
    {
        return Id.GetHashCode();
    }

    #endregion
}
