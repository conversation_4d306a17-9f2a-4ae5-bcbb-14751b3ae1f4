<UserControl x:Class="DeviceGuard.Shell.Views.Pages.MyAccountPage.MyAccountPageView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:myAccountPage="clr-namespace:DeviceGuard.Shell.Views.Pages.MyAccountPage"
             xmlns:prism="clr-namespace:DeviceGuard.Windows.Utility.Prism;assembly=DeviceGuard.Windows.Utility"
             xmlns:conv="clr-namespace:DeviceGuard.Windows.Controls.Conv;assembly=DeviceGuard.Windows.Controls"
             prism:ViewModelBinder.BindingView="True"
             mc:Ignorable="d"
             d:DesignHeight="450"
             d:DesignWidth="800"
             d:DataContext="{d:DesignInstance myAccountPage:MyAccountPageViewModel}">
    <Grid Margin="40">
        <StackPanel
            Visibility="{Binding IsLoggedIn, Converter={StaticResource BooleanToVisibilityConverter}, Mode=OneWay}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                    <ColumnDefinition Width="*"></ColumnDefinition>
                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                </Grid.ColumnDefinitions>

                <!--左侧图标-->
                <Border Background="#E5EAF2" CornerRadius="128" Padding="10">
                    <Image Width="128" Height="128"
                           Source="{Binding Plugin.Icon, TargetNullValue='pack://application:,,,/Resources/Undefined.ico', FallbackValue='pack://application:,,,/Resources/Undefined.ico', Mode=OneWay}">
                    </Image>
                </Border>

                <!--主要内容-->
                <StackPanel Grid.Column="1" Margin="30 10 0 0">
                    <StackPanel Orientation="Horizontal" Margin="0 0 0 15">
                        <TextBlock FontSize="24" Text="{Binding LoginUser.Name, FallbackValue=Unknown, Mode=OneWay}">
                        </TextBlock>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0 0 0 15">
                        <TextBlock FontSize="16" Foreground="Gray" Text="{Binding LoginUser.Email, Mode=OneWay}">
                        </TextBlock>
                    </StackPanel>
                    <!--<StackPanel Margin="0 0 0 15">
                        <TextBlock FontSize="16" VerticalAlignment="Center" Foreground="Gray">
                            <Run Text="{Binding LoginUser.UserName, Mode=OneWay}"></Run>
                        </TextBlock>
                    </StackPanel>-->
                    <StackPanel Orientation="Horizontal" Margin="0 0 0 15">
                        <TextBlock>
                            <Hyperlink Command="{Binding SignOutCommand}">
                                <Run Text="退出"></Run>
                            </Hyperlink>
                        </TextBlock>
                        <TextBlock Margin="10 0 0 0">
                            <TextBlock.Resources>
                                <conv:BooleanToTextConverter x:Key="LoginActionText" True="更换账户" False="登录" />
                            </TextBlock.Resources>
                            <Hyperlink Command="{Binding LoginCommand}">
                                <Run
                                    Text="{Binding IsLoggedIn, Converter={StaticResource LoginActionText}, Mode=OneWay}">
                                </Run>
                            </Hyperlink>
                        </TextBlock>
                    </StackPanel>
                </StackPanel>
            </Grid>
            <TextBlock Margin="60 20 0 0" Text="以后此处提供用户购买信息" />
        </StackPanel>
        <StackPanel
            HorizontalAlignment="Center"
            VerticalAlignment="Center"
            Margin="0 0 0 150"
            Visibility="{Binding IsLoggedIn, Converter={StaticResource DisplayFalseElseCollapsedConverter}, Mode=OneWay}">
            <TextBlock FontSize="16"
                       Margin="0 0 0 20">
                使用你的账户登录, 获取完整功能
            </TextBlock>
            <Button Width="90" Command="{Binding LoginCommand}">登录</Button>
        </StackPanel>
    </Grid>
</UserControl>