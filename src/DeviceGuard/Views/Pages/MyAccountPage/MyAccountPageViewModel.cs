using DeviceGuard.Interface.Cloud;
using DeviceGuard.Shell.Views.LoginDialog;
using DeviceGuard.Windows.Utility.Dialogs;
using DeviceGuard.Windows.Utility.Mvvm;

namespace DeviceGuard.Shell.Views.Pages.MyAccountPage;

public class MyAccountPageViewModel : RegionViewModelBase
{
    private readonly ILoginSession   _session;
    private readonly LoginDialogView _loginDialogView;
    private readonly IDialog         _dialog;

    public MyAccountPageViewModel(ILoginSession session,
        LoginDialogView                         loginDialogView,
        IDialog                                 dialog
    )
    {
        _session         = session;
        _loginDialogView = loginDialogView;
        _dialog          = dialog;
    }


    public override async Task OnLoadedAsync(CancellationToken cancellation)
    {
        IsLoggedIn = await _session.IsLoggedInAsync(cancellation);
        LoginUser  = await _session.GetLoginUserAsync(cancellation) ?? new LoginUserInfo();
    }

    public bool IsLoggedIn { get; private set; }

    public LoginUserInfo? LoginUser { get; private set; } = new();

    public AsyncDelegateCommand LoginCommand => GetPropertyCached(() => new AsyncDelegateCommand(async (cancellation) =>
        {
            await _dialog.ShowDialogAsync(_loginDialogView);

            IsLoggedIn = await _session.IsLoggedInAsync(cancellation);
            LoginUser  = await _session.GetLoginUserAsync(cancellation) ?? throw new InvalidOperationException("登录失败: 未知错误");
        }
    ));

    public AsyncDelegateCommand SignOutCommand => GetPropertyCached(() => new AsyncDelegateCommand(async (cancellation) =>
        {
            await _session.LogoutAsync(cancellation);

            IsLoggedIn = await _session.IsLoggedInAsync(cancellation);
            LoginUser  = await _session.GetLoginUserAsync(cancellation);
        }
    ));
}
