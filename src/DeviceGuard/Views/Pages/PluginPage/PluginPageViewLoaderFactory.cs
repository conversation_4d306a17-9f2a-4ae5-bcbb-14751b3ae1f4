using DeviceGuard.ModuleCore.Plugins;
using DeviceGuard.Shell.Interface.Pages;
using Unity.Resolution;

namespace DeviceGuard.Shell.Views.Pages.PluginPage;

public class PluginPageViewLoaderFactory : IPluginPageViewLoaderFactory
{
    private readonly IUnityContainer _container;

    public PluginPageViewLoaderFactory(IUnityContainer container)
    {
        _container = container;
    }

    public IPrimaryPageViewLoader Create(PluginNode plugin)
    {
        if (plugin == null) throw new ArgumentNullException(nameof(plugin));

        return _container.Resolve<PluginPageViewLoader>(new ParameterOverride("plugin", plugin));
    }
}
