using DeviceGuard.ModuleCore.Plugins;
using DeviceGuard.Modules.Interface;
using DeviceGuard.Shell.Configs;
using DeviceGuard.Shell.Interface.Breadcrumbs;
using DeviceGuard.Shell.Interface.Pages;
using DeviceGuard.Windows.Utility;

namespace DeviceGuard.Shell.Views.Pages.PluginPage;

/// <summary>
/// 最近访问
/// </summary>
public class PluginPageViewLoader : IPrimaryPageViewLoader
{
    private readonly IMainWindowTitle  _windowTitle;
    private readonly IPluginManager    _pluginManager;
    private readonly IBreadcrumb       _breadcrumb;
    private readonly PluginNode        _plugin;
    private readonly DeviceGuardConfig _config;

    public PluginPageViewLoader(IPluginManager pluginManager,
        IBreadcrumb                            breadcrumb,
        PluginNode                             plugin,
        IMainWindowTitle                       windowTitle,
        DeviceGuardConfig                      config)
    {
        _pluginManager = pluginManager;
        _breadcrumb    = breadcrumb;
        _plugin        = plugin;
        _windowTitle   = windowTitle;
        _config        = config;
    }

    public string Name => _plugin.Name;

    public async Task ShowAsync(string regionName, object? parameters, CancellationToken cancellationToken)
    {
        var plugin = _plugin;

        if (plugin == null) throw new ArgumentNullException(nameof(plugin));

        if (plugin.Installed == null)
        {
            throw new InvalidOperationException("插件未安装");
        }

        // 加载并显示
        var bootstrap  = await _pluginManager.LoadPluginAsync(plugin, cancellationToken);
        var viewLoader = bootstrap.GetViewLoaders(ModuleViewCategory.Main).FirstOrDefault();
        if (viewLoader is null)
        {
            throw new InvalidOperationException("插件未找到视图加载器");
        }

        viewLoader.Show(regionName);

        // 记录加载的 plugin
        var list = _config.RecentPlugins.ToList();
        list.Insert(0, plugin.Id);
        _config.RecentPlugins = list.Distinct().ToArray();

        _breadcrumb.Add(new BreadcrumbNode(Name, new NavigationModel(this)), true);
        _windowTitle.ChangeTitle(Name, "");
    }

    protected bool Equals(PluginPageViewLoader other)
    {
        return _plugin.Equals(other._plugin);
    }

    public override bool Equals(object? obj)
    {
        if (obj is null) return false;
        if (ReferenceEquals(this, obj)) return true;
        if (obj.GetType() != GetType()) return false;
        return Equals((PluginPageViewLoader)obj);
    }

    public override int GetHashCode()
    {
        return _plugin.GetHashCode();
    }
}
