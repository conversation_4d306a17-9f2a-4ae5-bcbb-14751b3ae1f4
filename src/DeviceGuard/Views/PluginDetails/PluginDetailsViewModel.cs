using System.Windows;
using DeviceGuard.ModuleCore.Plugins;
using DeviceGuard.Shell.Views.Pages.PluginPage;
using DeviceGuard.Windows.Interface.Services;
using DeviceGuard.Windows.Utility.Dialogs;
using DeviceGuard.Windows.Utility.Mvvm;

namespace DeviceGuard.Shell.Views.PluginDetails;

public class PluginDetailsViewModel : RegionViewModelBase
{
    private readonly IPluginManager               _pluginManager;
    private readonly IMessageBox                  _messageBox;
    private readonly IPluginPageViewLoaderFactory _pluginPageViewLoaderFactory;
    private readonly IAppRestartService           _appRestartService;

    public PluginDetailsViewModel(IPluginManager pluginManager,
        IMessageBox                              messageBox,
        IPluginPageViewLoaderFactory             pluginPageViewLoaderFactory,
        IAppRestartService                       appRestartService)
    {
        _pluginManager               = pluginManager;
        _messageBox                  = messageBox;
        _pluginPageViewLoaderFactory = pluginPageViewLoaderFactory;
        _appRestartService           = appRestartService;
    }

    public PluginNode Plugin { get; set; } = PluginNode.Empty;

    /// <summary>
    /// 获取插件
    /// </summary>
    public AsyncDelegateCommand AcquirePluginCommand => new(async (cancellationToken) =>
    {
        if (Plugin.Id == PluginNode.Empty.Id)
        {
            await _messageBox.ShowErrorAsync("没有插件对象");
            return;
        }

        if (Plugin.IsOwned)
        {
            await _messageBox.ShowWarningAsync("您已经拥有该插件");
            return;
        }

        // todo 目前仅支持通过管理员获取插件
        await _messageBox.ShowInfoAsync("联系系统管理员获取该插件, 插件代码: " + Plugin.Id);

        // await _pluginManager.OwnAsync(Plugin);
        // await _messageBox.ShowInfoAsync("已获取插件");

        // TODO 检查 IsOwned 应该
    });

    public AsyncDelegateCommand InstallPluginCommand => new(async (cancellationToken) =>
    {
        if (Plugin.Id == PluginNode.Empty.Id)
        {
            await _messageBox.ShowErrorAsync("没有插件对象");
            return;
        }

        if (Plugin.IsInstalled)
        {
            await _messageBox.ShowWarningAsync("您已经安装过该插件");
            return;
        }

        await _pluginManager.InstallAsync(Plugin.Id, Plugin.Latest.SemVersion, cancellationToken);
    });

    public AsyncDelegateCommand RunPluginCommand => new(async (cancellationToken) =>
    {
        if (Plugin.Id == PluginNode.Empty.Id)

        {
            await _messageBox.ShowErrorAsync("没有插件对象");
            return;
        }

        if (!Plugin.IsInstalled)
        {
            await _messageBox.ShowWarningAsync("您还没有安装该插件");
            return;
        }

        var loader = _pluginPageViewLoaderFactory.Create(Plugin);
        await loader.ShowAsync(RegionNames.PrimaryViewRegion, Plugin, cancellationToken);
    });

    public AsyncDelegateCommand UninstallPluginCommand => new(async (cancellationToken) =>
    {
        if (Plugin.Id == PluginNode.Empty.Id)
        {
            await _messageBox.ShowErrorAsync("没有插件对象");
            return;
        }

        if (!Plugin.IsInstalled)
        {
            await _messageBox.ShowWarningAsync("您还没有安装该插件");
            return;
        }

        await _pluginManager.UninstallAsync(Plugin.Id, cancellationToken);
    });

    public AsyncDelegateCommand RestartAppCommand => new((_) =>
        {
            _appRestartService.Restart();
            return Task.CompletedTask;
        }
    );

    /// <summary>
    /// 更新升级插件
    /// </summary>
    public AsyncDelegateCommand UpgradePluginCommand => new(async (cancellationToken) =>
    {
        if (Plugin.Id == PluginNode.Empty.Id)
        {
            await _messageBox.ShowErrorAsync("没有插件对象");
            return;
        }

        if (!Plugin.IsInstalled)
        {
            await _messageBox.ShowWarningAsync("您还没有安装该插件");
            return;
        }

        await _pluginManager.InstallAsync(Plugin.Id, Plugin.Latest.SemVersion, cancellationToken);
        await _messageBox.ShowInfoAsync("插件更新成功");

        RaisePropertyChanged(nameof(Plugin));
    });

    public override Task OnNavigatedToAsync(NavigationContext navigationContext, CancellationToken cancellation)
    {
        if (!navigationContext.Parameters.TryGetValue("Plugin", out PluginNode? pluginInfo) || pluginInfo == null)
        {
            throw new ArgumentException("没有插件对象");
        }

        Plugin = pluginInfo;

        return base.OnNavigatedToAsync(navigationContext, cancellation);
    }
}
