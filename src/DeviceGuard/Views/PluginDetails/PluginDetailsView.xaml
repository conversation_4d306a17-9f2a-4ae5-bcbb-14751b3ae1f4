<UserControl x:Class="DeviceGuard.Shell.Views.PluginDetails.PluginDetailsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:wpf="clr-namespace:CefSharp.Wpf;assembly=CefSharp.Wpf"
             xmlns:pluginDetails="clr-namespace:DeviceGuard.Shell.Views.PluginDetails"
             mc:Ignorable="d"
             d:DataContext="{d:DesignInstance Type=pluginDetails:PluginDetailsViewModel}"
             d:DesignHeight="450"
             d:DesignWidth="800">
    <!--插件显示-->
    <Grid Margin="20"
          Visibility="{Binding Plugin, Converter={StaticResource DisplayNotNullElseCollapsedConverter}}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
        </Grid.RowDefinitions>

        <StackPanel>
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                    <ColumnDefinition Width="*"></ColumnDefinition>
                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                </Grid.ColumnDefinitions>

                <!--左侧图标-->
                <Border Background="#E5EAF2" CornerRadius="10" Padding="10">
                    <Image Width="64" Height="64"
                           Source="{Binding Plugin.Icon, TargetNullValue='pack://application:,,,/Resources/Undefined.ico', FallbackValue='pack://application:,,,/Resources/Undefined.ico', Mode=OneWay}">
                    </Image>

                </Border>

                <!--主要内容-->
                <StackPanel Grid.Column="1" Margin="30 0 0 0">
                    <StackPanel Orientation="Horizontal" Margin="0 0 0 10">
                        <TextBlock FontSize="20" Text="{Binding Plugin.Name, FallbackValue=Unknown, Mode=OneWay}">
                        </TextBlock>
                        <TextBlock Margin="15 0 0 0">
                            <Run FontSize="20" Text=""></Run>
                            <Run FontSize="13" Text="v"></Run>
                            <Run FontSize="13"
                                 Text="{Binding Plugin.Latest.Version, FallbackValue=--, Mode=OneWay}">
                            </Run>
                        </TextBlock>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0 0 0 10">
                        <TextBlock
                            Visibility="{Binding Plugin.Url, Converter={StaticResource DisplayNotNullElseCollapsedConverter}}">
                            <Hyperlink NavigateUri="{Binding Plugin.Url, FallbackValue=about:blank, TargetNullValue=about:blank}">
                                <Run Text="{Binding Plugin.Url,  Mode=OneWay}"></Run>
                            </Hyperlink>
                        </TextBlock>
                        <TextBlock Foreground="DimGray"
                                   Visibility="{Binding Plugin.Url, Converter={StaticResource DisplayNullElseCollapsedConverter}}"
                                   Text="文档正在加紧制作中...">
                        </TextBlock>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="2 0 0 0">
                        <!-- <TextBlock Text="免费"></TextBlock> -->
                        <!-- <TextBlock Text="|" Margin="10 0"></TextBlock> -->
                        <TextBlock Text="4.9 ★"></TextBlock>
                        <TextBlock Text="|" Margin="10 0"></TextBlock>
                        <TextBlock Text="1 人评价"></TextBlock>
                    </StackPanel>
                </StackPanel>

                <!--右侧按钮-->
                <StackPanel Grid.Column="2" Orientation="Horizontal" Margin="0 0 0 0" VerticalAlignment="Top">
                    <Button
                        Visibility="{Binding Plugin.CanOwn, FallbackValue=False, Converter={StaticResource DisplayTrueElseCollapsedConverter}}"
                        Content="获取"
                        Margin="10 0 0 0"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        Command="{Binding AcquirePluginCommand}"
                        ToolTip="立刻获取插件, 获取后您可以在您的所有设备安装并运行该插件" />
                    <Button
                        Visibility="{Binding Plugin.CanInstall, FallbackValue=False, Converter={StaticResource DisplayTrueElseCollapsedConverter}}"
                        Content="安装插件"
                        Margin="10 0 0 0"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        Command="{Binding InstallPluginCommand}"
                        ToolTip="在此设备安装插件" />
                    <Button
                        Visibility="{Binding Plugin.CanRun, FallbackValue=False, Converter={StaticResource DisplayTrueElseCollapsedConverter}}"
                        Content="立即运行"
                        Margin="10 0 0 0"
                        Style="{StaticResource MaterialDesignRaisedButton}"
                        Command="{Binding RunPluginCommand}"
                        ToolTip="立即运行插件" />
                    <Button
                        Visibility="{Binding Plugin.CanUpgrade, FallbackValue=False, Converter={StaticResource DisplayTrueElseCollapsedConverter}}"
                        Content="更新"
                        Margin="10 0 0 0"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{Binding UpgradePluginCommand}"
                        ToolTip="把插件更新至最新版本" />
                    <Button
                        Visibility="{Binding Plugin.CanUninstall, FallbackValue=False, Converter={StaticResource DisplayTrueElseCollapsedConverter}}"
                        Foreground="Red"
                        BorderBrush="Red"
                        Content="卸载"
                        Margin="10 0 0 0"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{Binding UninstallPluginCommand}"
                        ToolTip="从该设备卸载插件, 卸载后您可以随时重新安装" />
                    <Button
                        Visibility="{Binding Plugin.RestartRequired, FallbackValue=False, Converter={StaticResource DisplayTrueElseCollapsedConverter}}"
                        Foreground="Red"
                        BorderBrush="Red"
                        Content="重启软件"
                        Margin="10 0 0 0"
                        Style="{StaticResource MaterialDesignOutlinedButton}"
                        Command="{Binding RestartAppCommand}"
                        ToolTip="完成卸载插件后, 需要重启动软件才能生效" />
                </StackPanel>

            </Grid>
            <TextBlock Margin="0 20 0 0" Text="{Binding Plugin.Brief, FallbackValue=描述正在加紧制作中..., Mode=OneWay}" />
            <Separator Margin="0 20"></Separator>
        </StackPanel>
        <wpf:ChromiumWebBrowser Grid.Row="1" Address="{Binding Plugin.Url, FallbackValue=about:blank, TargetNullValue=about:blank}" />
    </Grid>
</UserControl>