using System.Diagnostics;
using DeviceGuard.Infrastructure.Cloud;
using DeviceGuard.Interface.Cloud;
using DeviceGuard.Windows.Utility.Dialogs;
using DeviceGuard.Windows.Utility.Mvvm;
using Newtonsoft.Json;

namespace DeviceGuard.Shell.Views.LoginDialog;

public class LoginDialogViewModel : RegionViewModelBase
{
    private readonly ILoginClient      _loginClient;
    private readonly IMessageBox       _messageBox;
    private readonly LoginClientConfig _config;

    public LoginDialogViewModel(ILoginClient loginClient, IMessageBox messageBox, LoginClientConfig config)
    {
        _loginClient = loginClient;
        _messageBox  = messageBox;
        _config      = config;

        UserName     = _config.LastUserName;
        Password     = "";
        ErrorMessage = "";
        RememberMe   = _config.RememberMe;
    }

    public string UserName { get; set; }

    public string Password { get; set; }

    public string ErrorMessage { get; set; }

    public bool RememberMe { get; set; }

    public AsyncDelegateCommand LoginCommand => GetPropertyCached(() => new AsyncDelegateCommand(async () =>
    {
        if (string.IsNullOrEmpty(UserName))
        {
            ErrorMessage = "请输入用户名";
        }

        // TODO 测试用
        if (UserName == "test" && Password == "")
        {
            Password = "123";
        }

        try
        {
            ErrorMessage = "";

            // 执行登录
            Token = await _loginClient.LoginAsync(UserName, Password);

            _config.LastUserName = UserName;
            _config.RememberMe   = RememberMe;
            _config.Token        = RememberMe ? JsonConvert.SerializeObject(Token) : "";

            CloseDialog?.Invoke(true);
        }
        catch (Exception e)
        {
            ErrorMessage = e.Message;
        }
    }).ObservesCanExecute(() => CanExecuteLoginCommand));


    public DelegateCommand RegisterCommand => GetPropertyCached(() => new DelegateCommand(() =>
    {
        try
        {
            ErrorMessage = "";

            // TODO 
            // 浏览器打开页面 https://auth.chkfun.com/realms/device-guard/account
            string url = "https://auth.chkfun.com/realms/device-guard/account";
            ProcessStartInfo psi = new ProcessStartInfo
            {
                FileName        = url,
                UseShellExecute = true // 必须为 true 才能用默认浏览器打开网址
            };
            Process.Start(psi);

            // await _loginClient.LoginViaWebAsync(RememberMe);
            // CloseDialog?.Invoke(true);
        }
        catch (Exception e)
        {
            ErrorMessage = e.Message;
        }
    }).ObservesCanExecute(() => CanExecuteLoginCommand));


    public bool CanExecuteLoginCommand => !string.IsNullOrWhiteSpace(UserName);

    public AsyncDelegateCommand CancelCommand => GetPropertyCached(() => new AsyncDelegateCommand(() =>
    {
        // 关闭对话框
        CloseDialog?.Invoke(false);
        return Task.CompletedTask;
    }));

    /// <summary>
    /// 关闭对话框
    /// </summary>
    public Action<bool>? CloseDialog { get; set; }

    public AsyncDelegateCommand SwitchToQrLoginCommand => GetPropertyCached(() => new AsyncDelegateCommand(async () =>
    {
        await _messageBox.ShowInfoAsync("二维码登录正在开发中, 敬请期待");
    }));

    /// <summary>
    /// 登录成功后的 Token
    /// </summary>
    public TokenResponse Token { get; private set; } = new();
}
