<dialog:UserDialog x:Class="DeviceGuard.Shell.Views.LoginDialog.LoginDialogView"
                   xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                   xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                   xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                   xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                   xmlns:dialog="clr-namespace:DeviceGuard.Windows.Controls.Dialog;assembly=DeviceGuard.Windows.Controls"
                   xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
                   xmlns:loginDialog="clr-namespace:DeviceGuard.Shell.Views.LoginDialog"
                   xmlns:mvvm="http://prismlibrary.com/"
                   xmlns:prism="clr-namespace:DeviceGuard.Windows.Utility.Prism;assembly=DeviceGuard.Windows.Utility"
                   mvvm:ViewModelLocator.AutoWireViewModel="True"
                   prism:ViewModelBinder.BindingView="True"
                   Loaded="LoginDialogView_OnLoaded"
                   mc:Ignorable="d" d:DataContext="{d:DesignInstance loginDialog:LoginDialogViewModel}">
    <Grid>
        <Grid Margin="50 40">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <!-- 标题 -->
            <TextBlock Text="设备卫士"
                       FontSize="36"
                       FontWeight="Bold"
                       Foreground="{DynamicResource PrimaryHueLightBrush}"
                       HorizontalAlignment="Center"
                       Margin="0,30,0,10" />

            <TextBlock
                Grid.Row="1" Text="{Binding ErrorMessage}"
                FontSize="12"
                Foreground="Red"
                HorizontalAlignment="Center"
                MaxWidth="250"
                Margin="0,10"
                VerticalAlignment="Center" />

            <!-- 用户名 -->
            <TextBox Grid.Row="2"
                     x:Name="UserNameBox"
                     materialDesign:HintAssist.Hint="用户名"
                     materialDesign:TextFieldAssist.DecorationVisibility="Hidden"
                     Style="{StaticResource MaterialDesignOutlinedTextBox}"
                     Text="{Binding UserName, UpdateSourceTrigger=PropertyChanged}"
                     materialDesign:HintAssist.IsFloating="False"
                     FontSize="16"
                     Padding="16 10"
                     VerticalAlignment="Center"
                     Margin="0,10" />

            <!-- 密码 -->
            <PasswordBox Grid.Row="3"
                         x:Name="PasswordBox"
                         materialDesign:HintAssist.Hint="密码"
                         materialDesign:TextFieldAssist.DecorationVisibility="Hidden"
                         Style="{StaticResource MaterialDesignOutlinedPasswordBox}"
                         materialDesign:HintAssist.IsFloating="False"
                         Focusable="True"
                         FontSize="16"
                         Padding="16 10"
                         VerticalAlignment="Center"
                         PasswordChanged="PasswordBox_PasswordChanged"
                         Margin="0,10" />

            <!-- 记住我 -->
            <CheckBox Grid.Row="4"
                      Content="记住我(_R)"
                      FontSize="16"
                      Margin="0 10 0 20"
                      VerticalAlignment="Center"
                      IsChecked="{Binding RememberMe}"
                      Foreground="{DynamicResource MaterialDesignBody}"
                      Style="{StaticResource MaterialDesignCheckBox}" />

            <!-- 按钮 -->
            <StackPanel Grid.Row="5" HorizontalAlignment="Center" Margin="0 30 0 100">
                <StackPanel Orientation="Horizontal">
                    <Button Content="登录 (_L)"
                            Command="{Binding LoginCommand}"
                            Width="120"
                            Padding="10 10"
                            IsDefault="True"
                            Height="Auto"
                            Style="{StaticResource MaterialDesignRaisedDarkButton}" />
                    <Button Content="取消 (_C)"
                            Command="{Binding CancelCommand}"
                            Width="120"
                            Padding="10 10"
                            IsCancel="True"
                            Height="Auto"
                            Margin="20 0 0 0"
                            Style="{StaticResource MaterialDesignOutlinedButton}" />
                </StackPanel>
                <TextBlock Margin="0 20 0 0">
                    <Hyperlink Command="{Binding RegisterCommand}">注册(_R)</Hyperlink>
                </TextBlock> 
            </StackPanel>
        </Grid>
        <Grid VerticalAlignment="Bottom">
            <Button Width="70" Height="70" HorizontalAlignment="Left" Background="White"
                    Padding="0"
                    Style="{StaticResource MaterialDesignFlatButton}"
                    Command="{Binding SwitchToQrLoginCommand}">
                <materialDesign:PackIcon Kind="Qrcode" Width="70" Height="70" Foreground="DimGray" />
            </Button>
            <Polygon Points="0,0 70,0 70,70"
                     Fill="White"
                     StrokeThickness="0" />

        </Grid>
    </Grid>
</dialog:UserDialog>