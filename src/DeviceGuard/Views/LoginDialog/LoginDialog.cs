using DeviceGuard.Interface.Cloud;
using DeviceGuard.Windows.Interface.ViewLoader;
using DeviceGuard.Windows.Utility.Dialogs;

namespace DeviceGuard.Shell.Views.LoginDialog;

public class LoginDialog(
    IDialog         _dialog,
    LoginDialogView _loginDialogView)
    : ILoginDialog
{
    public async Task<TokenResponse> ShowAsync(CancellationToken cancellation)
    {
        await _dialog.ShowDialogAsync(_loginDialogView);
        return _loginDialogView.Token;
    }
}
