using System.Security;
using System.Windows;
using DeviceGuard.Interface.Cloud;
using DeviceGuard.Windows.Controls.Dialog;

namespace DeviceGuard.Shell.Views.LoginDialog;

public partial class LoginDialogView : UserDialog
{
    public LoginDialogView()
    {
        InitializeComponent();

        ViewModel.CloseDialog = (bool result) =>
        {
            DialogResult = result;
            Close();
        };
    }

    public LoginDialogViewModel ViewModel => (LoginDialogViewModel)DataContext;

    /// <summary>
    /// 登录成功后, 返回的 token
    /// </summary>
    public TokenResponse Token => ViewModel.Token;

    private void PasswordBox_PasswordChanged(object sender, RoutedEventArgs e)
    {
        ViewModel.Password = PasswordBox.Password;
    }

    private void LoginDialogView_OnLoaded(object sender, RoutedEventArgs e)
    {
        if (string.IsNullOrEmpty(ViewModel.UserName))
            UserNameBox.Focus();
        else
            PasswordBox.Focus();

        PasswordBox.Clear();
    }
}
