<prism:PrismApplication x:Class="DeviceGuard.Shell.App"
                        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                        xmlns:prism="http://prismlibrary.com/"
                        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!--Default style-->
                <ResourceDictionary Source="pack://application:,,,/DeviceGuard.Styles;component/AppStyle.xaml" />
            </ResourceDictionary.MergedDictionaries>
            <Style TargetType="materialDesign:Ripple">
                <Setter Property="IsEnabled" Value="False" />
            </Style>

        </ResourceDictionary>
    </Application.Resources>
</prism:PrismApplication>