using System.Security.Authentication;
using System.Windows;
using System.Windows.Threading;
using DeviceGuard.Windows.Utility.Dialogs;
using Serilog;

namespace DeviceGuard.Shell.Startup;

/// <summary>
/// WPF 应用程序全局异常处理
/// </summary>
public class ApplicationUnhandledException
{
    private IMessageBox? _messageBox;

    /// <summary>
    /// 防止 OnDispatcherUnhandledException 重入
    /// </summary>
    private readonly ManualResetEventSlim _slimOnDispatcherUnhandledException = new ManualResetEventSlim(false);

    public void SetMessageBox(IMessageBox messageBox)
    {
        _messageBox = messageBox;
    }

    /// <summary>
    /// 订阅异常事件
    /// </summary>
    public void SubscribeUnhandledExceptions()
    {
        Application.Current.DispatcherUnhandledException += OnDispatcherUnhandledException;
        AppDomain.CurrentDomain.UnhandledException       += OnUnhandledDomainException;
        TaskScheduler.UnobservedTaskException            += OnUnhandledTaskSchedulerException;
    }

    /// <summary>
    /// 处理 WPF Dispatcher 捕获的异常，通常这些异常来自 WPF UI 收到的未处理异常
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void OnDispatcherUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
    {
        Log.Error(e.Exception, "OnDispatcherUnhandledException");

        // MessageBox 如果出错可能导致重入, 小心 多线程重入 递归重入 单线程无限循环重入
        if (_slimOnDispatcherUnhandledException.IsSet)
            return;
        _slimOnDispatcherUnhandledException.Set();

        try
        {
            // 任务停止无需执行
            if (e.Exception is TaskCanceledException)
            {
                // todo 建议右上角提示一个气泡
                e.Handled = true;
                return;
            }

            // 这个异常来自插件加载了过程中需要登录, 但是用户点击了取消按钮, 我们就不要再提示用户登录了, 因为用户主动放弃
            if (e.Exception is AuthenticationException ae && ae.Data["Silence"] is true)
            {
                e.Handled = true;
                return;
            }

            var message = e.Exception.ToString();
            _messageBox?.ShowErrorAsync(message);

            // 如果当前主窗体还没有显示出来，直接可以退出了
            if (Application.Current?.MainWindow?.IsVisible == true)
                e.Handled = true;
        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "We got exception in OnDispatcherUnhandledException");
        }
        finally
        {
            _slimOnDispatcherUnhandledException.Reset();
        }
    }

    /// <summary>
    /// 这些异常为应用程序域收到的未处理异常，通常这里收到的异常，意味着程序遇到了无法恢复的错误
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private static void OnUnhandledDomainException(object sender, UnhandledExceptionEventArgs e)
    {
        var exception = e.ExceptionObject as Exception;
        Log.Fatal(exception, $"OnUnhandledDomainException, IsTerminating={e.IsTerminating}");
    }

    /// <summary>
    /// 任务模块收到的异常
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private static void OnUnhandledTaskSchedulerException(object? sender, UnobservedTaskExceptionEventArgs e)
    {
        Log.Fatal(e.Exception, "OnUnhandledTaskSchedulerException");
    }
}
