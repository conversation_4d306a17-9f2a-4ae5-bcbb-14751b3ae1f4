using System.Security.Cryptography;
using System.Text;
using Serilog;

namespace DeviceGuard.Shell.Startup;

/// <summary>
/// 防止程序多开处理类，
/// </summary>
public class MultipleInstancePrevention : IDisposable
{
    /// <summary>
    /// 跨进程事件，检测程序已重启
    /// </summary>
    private EventWaitHandle? _eventHandler;

    /// <summary>
    /// 取消操作
    /// </summary>
    private readonly CancellationTokenSource _tokenSource = new();

    /// <summary>
    /// 收到进程防多开消息
    /// </summary>
    public event Action? WakeUpEventReceived;

    protected virtual void OnWakeUpEventReceived()
    {
        WakeUpEventReceived?.Invoke();
    }

    /// <summary>
    /// 启动单实例守护
    /// </summary>
    /// <returns>启动失败, 已经有一个进程, 返回 False</returns>
    public bool Initialize(string eventName)
    {
        if (string.IsNullOrEmpty(eventName))
            throw new ArgumentException("Event name cannot be null or empty.", nameof(eventName));

        // 为防止非法字符进行 name 封装
        var name = new Guid(MD5.Create().ComputeHash(Encoding.UTF8.GetBytes(eventName))).ToString().ToUpper();

        _eventHandler = new EventWaitHandle(false, EventResetMode.AutoReset, name, out var createdNew);
        if (!createdNew)
        {
            // 已经有一个实例运行了, 就给他发送一个信号, 然后默默离开
            _eventHandler.Set();
            return false;
        }

        // 启动监听
        Task.Run(ListenAnotherProcessWakeUpEvent);
        return true;
    }

    /// <summary>
    /// 监听其他进程的唤醒
    /// </summary>
    /// <returns></returns>
    private async Task ListenAnotherProcessWakeUpEvent()
    {
        if (_eventHandler == null)
            throw new ArgumentNullException(nameof(_eventHandler));

        var cancel = _tokenSource.Token;

        while (!cancel.IsCancellationRequested)
        {
            try
            {
                var result = WaitHandle.WaitAny([
                    _eventHandler,
                    cancel.WaitHandle
                ]);

                // 如果第一个元素 _eventHandler 激发了信号
                if (result == 0)
                    OnWakeUpEventReceived();
            }
            catch (Exception ex)
            {
                Log.Warning(ex, nameof(ListenAnotherProcessWakeUpEvent));
            }
            finally
            {
                await Task.Delay(50, cancel);
            }
        }
    }

    /// <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary>
    public void Dispose()
    {
        _tokenSource.Cancel();
        _tokenSource.Dispose();

        _eventHandler?.Dispose();
    }
}