using System.Collections.ObjectModel;
using System.ComponentModel;
using DeviceGuard.Windows.Utility.Mvvm;

namespace DeviceGuard.Shell.Interface.Breadcrumbs;

/// <summary>
/// 面包屑路径服务
/// </summary>
public interface IBreadcrumb : INotifyPropertyChanged
{
    /// <summary>
    /// 向面包屑路径尾部添加一个节点
    /// </summary>
    /// <param name="node">要添加的节点</param>
    /// <param name="mergeToLastIfMatch">如果节点与当前最后一个节点相同，则合并</param>
    void Add(BreadcrumbNode node, bool mergeToLastIfMatch);

    /// <summary>
    /// 把指定的节点之后的所有节点都移除
    /// </summary>
    /// <param name="node">用来判断的节点, 该节点后面的节点都会被移除</param>
    void RemoveAfter(BreadcrumbNode node);

    /// <summary>
    /// 清空所有节点
    /// </summary>
    void Clear();

    /// <summary>
    /// 返回可供跟踪的节点集合
    /// </summary>
    ObservableCollection<BreadcrumbNode> Nodes { get; }
}