using System.Collections.ObjectModel;

namespace DeviceGuard.Shell.Interface.Breadcrumbs.Impl;

public class Breadcrumb : BindableBase, IBreadcrumb
{
    /// <summary>
    /// 向面包屑路径尾部添加一个节点
    /// </summary>
    /// <param name="node">要添加的节点</param>
    /// <param name="mergeToLastIfMatch">如果节点与当前最后一个节点相同，则合并</param>
    public void Add(BreadcrumbNode node, bool mergeToLastIfMatch)
    {
        if (node == null) throw new ArgumentNullException(nameof(node));

        // 合并最后一个节点
        if (mergeToLastIfMatch && Nodes.LastOrDefault() is { } lastNode && Equals(lastNode, node))
        {
            Nodes[^1] = node;
        }
        else
        {
            // 直接添加
            Nodes.Add(node);
        }

        // 标记该节点为根节点
        Nodes[0].IsRoot = true;
    }

    /// <summary>
    /// 把指定的节点之后的所有节点都移除
    /// </summary>
    /// <param name="node">用来判断的节点, 该节点后面的节点都会被移除</param>
    public void RemoveAfter(BreadcrumbNode node)
    {
        if (node == null) throw new ArgumentNullException(nameof(node));

        var index = Nodes.IndexOf(node);
        if (index == -1)
            return;

        while(index < Nodes.Count)
        {
            Nodes.RemoveAt(index);
        }
    }

    /// <summary>
    /// 清空所有节点
    /// </summary>
    public void Clear()
    {
        Nodes.Clear();
    }

    /// <summary>
    /// 返回可供跟踪的节点集合
    /// </summary>
    public ObservableCollection<BreadcrumbNode> Nodes { get; } = new();
}
