using DeviceGuard.Shell.Interface.Pages;

namespace DeviceGuard.Shell.Interface.Breadcrumbs;

/// <summary>
/// 面包屑节点
/// </summary>
public class BreadcrumbNode
{
    public BreadcrumbNode(string name, NavigationModel navigation)
    {
        Name       = name;
        Navigation = navigation;
    }

    public string Name { get; }
    
    /// <summary>
    /// 是否为第一个节点(根节点)
    /// </summary>
    public bool IsRoot { get; set; }

    public NavigationModel Navigation { get; }

    protected bool Equals(BreadcrumbNode other)
    {
        return Name == other.Name && Navigation.Equals(other.Navigation);
    }

    public override bool Equals(object? obj)
    {
        if (obj is null) return false;
        if (ReferenceEquals(this, obj)) return true;
        if (obj.GetType() != GetType()) return false;
        return Equals((BreadcrumbNode)obj);
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(Name, Navigation);
    }
}
