namespace DeviceGuard.Shell.Interface.Pages;

/// <summary>
/// 任务栏按钮
/// </summary>
public interface IPrimaryButton : IPrimaryPageViewLoader
{
    string Id { get; }

    /// <summary>
    /// 提示
    /// </summary>
    string Tooltip { get; }

    /// <summary>
    /// 图标
    /// </summary>
    string Icon { get; }

    /// <summary>
    /// 默认显示顺序
    /// </summary>
    int Index { get; }

    /// <summary>
    /// 按钮位置
    /// </summary>
    ButtonPosition ButtonPosition { get; }
}
