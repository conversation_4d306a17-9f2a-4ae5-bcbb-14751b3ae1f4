using DeviceGuard.Windows.Interface.ViewLoader;

namespace DeviceGuard.Shell.Interface.Pages;

/// <summary>
/// 主要显示区域页加载器
/// </summary>
public interface IPrimaryPageViewLoader : IViewLoader
{
    /// <summary>
    /// 视图名称
    /// </summary>
    string Name { get; }

    /// <summary>
    /// 显示视图
    /// </summary>
    /// <param name="regionName"></param>
    /// <param name="parameters"></param>
    /// <param name="cancellationToken"></param>
    Task ShowAsync(string regionName, object? parameters, CancellationToken cancellationToken);
}
