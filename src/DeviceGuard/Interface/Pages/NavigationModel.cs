namespace DeviceGuard.Shell.Interface.Pages;

/// <summary>
/// 导航模型对象, 面包屑和菜单都会导航
/// 导航需要加载器和导航参数
/// 导航参数包含 Plugin 的参数以及 Page 导航的参数
/// </summary>
public class NavigationModel
{
    public NavigationModel(IPrimaryPageViewLoader viewLoader)
    {
        Loader = viewLoader;
    }

    public NavigationModel(IPrimaryPageViewLoader viewLoader, object? parameter)
    {
        Loader    = viewLoader;
        Parameter = parameter;
    }

    public IPrimaryPageViewLoader Loader { get; }

    public object? Parameter { get; }

    protected bool Equals(NavigationModel other)
    {
        return Loader.Equals(other.Loader) && Equals(Parameter, other.Parameter);
    }

    public override bool Equals(object? obj)
    {
        if (obj is null) return false;
        if (ReferenceEquals(this, obj)) return true;
        if (obj.GetType() != GetType()) return false;
        return Equals((NavigationModel)obj);
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(Loader, Parameter);
    }
}
