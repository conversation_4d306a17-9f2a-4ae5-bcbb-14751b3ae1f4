namespace DeviceGuard.Shell.Security;

/// <summary>
/// CFTech根证书静态数据类
/// 将根证书数据硬编码在程序中，确保无法被外部篡改
/// </summary>
public static class CFTechRootCertificate
{
    /// <summary>
    /// CFTech根证书的PEM格式公钥
    /// </summary>
    public const string PublicKeyPem = @"-----BEGIN CERTIFICATE-----
MIIDojCCAoqgAwIBAgIJAKl3MFX/vNbhMA0GCSqGSIb3DQEBCwUAMH4xCzAJBgNV
BAYTAkNOMQ8wDQYDVQQIEwZZdW5uYW4xEDAOBgNVBAcTB0t1bm1pbmcxDzANBgNV
BAoTBkNGVGVjaDERMA8GA1UECxMIU2VjdXJpdHkxKDAmBgNVBAMTH0NGVGVjaCBT
aWduYXR1cmUgQXV0aG9yaXR5IFJvb3QwHhcNMjUwNjI1MTUyNTIyWhcNMzUwNjI1
MTUyNTIyWjB+MQswCQYDVQQGEwJDTjEPMA0GA1UECBMGWXVubmFuMRAwDgYDVQQH
EwdLdW5taW5nMQ8wDQYDVQQKEwZDRlRlY2gxETAPBgNVBAsTCFNlY3VyaXR5MSgw
JgYDVQQDEx9DRlRlY2ggU2lnbmF0dXJlIEF1dGhvcml0eSBSb290MIIBIjANBgkq
hkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmPNlbjDdC4tUQ85KcVUTBn7fkW67Ni9l
PjdBEHQxehnjqWA7zrQKZIikhnemx225mKgq5seepSUuJX9B6UT2Qr0B1yfgeHoi
7GJlYYx1hhRw56bP/HuOeWetXj0pNjYoI0aHZVJn5TJBaARS9xWiUq1lGhZWKtEl
rQykn4jHqKjLM6b14r9yg6gQfYLVWrY1Nv9JlRtQVWKbXYRf653mMLGT6tv33+iz
KcwtiQrZJOf7t5If8zhchZqN0wEJlzTEYjIK6f06ldhQhhbyU+92l5h+c2ehaWn2
C9JDb8aE0DKmizQAfsCrH1vEx1lGEL1MtsipkgaUgC03H+yL0q2HsQIDAQABoyMw
ITAPBgNVHRMBAf8EBTADAQH/MA4GA1UdDwEB/wQEAwIBhjANBgkqhkiG9w0BAQsF
AAOCAQEAHVm8jNTSR04d4s/f3BZ2H6bkBCouZSnlZBS6qbPS0zkM0fCIOdTCuGSe
YPGcIa5kEJnY2DryIBYTU9xrPRMNCHF4JjBy/Afsvsijp3JMu49hydBS84+amVfQ
qXlnoQD9ZpaYF4cgf3g9G/KtwTneJ/pF7nX5GS6AcrAgcKAf9faZrrw84Yf3E9Mp
0v6Rb4mEAJECB3nyPkFmbY+eq6DPB11Tc9Bt3d8E2ZViFgwef9+W3d65J3NC1ikU
yYlEXuBtNgTOng5HkiULvV4O25j8DoWw5H+ON7J9YDeWht2E8q2tzMXQTL40d2fX
6LP0DSE1WrwlK7iNSEhvAD9DxTebdw==
-----END CERTIFICATE-----";

    /// <summary>
    /// 证书颁发者信息
    /// </summary>
    public const string Issuer = "CN=CFTech Signature Authority Root, OU=Security, O=CFTech, L=Kunming, S=Yunnan, C=CN";

    /// <summary>
    /// 证书有效期开始时间（UTC）
    /// </summary>
    public static readonly DateTime ValidFrom = new DateTime(2025, 6, 25, 15, 25, 22, DateTimeKind.Utc);

    /// <summary>
    /// 证书有效期结束时间（UTC）
    /// </summary>
    public static readonly DateTime ValidTo = new DateTime(2035, 6, 25, 15, 25, 22, DateTimeKind.Utc);

    /// <summary>
    /// 证书指纹（SHA256）
    /// </summary>
    public const string Fingerprint = "be7a3b6ea1f8c1e1527961887a4344a891b77210c74a75815f14a80ad4500027";

    /// <summary>
    /// 验证指纹是否匹配
    /// </summary>
    /// <param name="fingerprint">要验证的指纹</param>
    /// <returns>是否匹配</returns>
    public static bool IsValidFingerprint(string fingerprint)
    {
        if (string.IsNullOrEmpty(fingerprint))
            return false;

        return string.Equals(fingerprint.Replace(":", "").Replace("-", "").ToLowerInvariant(),
            Fingerprint.ToLowerInvariant(),
            StringComparison.OrdinalIgnoreCase);
    }
}
