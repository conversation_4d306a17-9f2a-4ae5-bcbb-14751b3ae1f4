using DeviceGuard.Interface.Prism;
using DeviceGuard.Interface.Security;

namespace DeviceGuard.Shell.Security;

/// <summary>
/// 安全组件，用于注册安全相关的服务
/// </summary>
public class SecurityComponent : IPrismComponent
{
    public void RegisterTypes(IContainerRegistry container)
    {
        // 注册证书验证器
        container.RegisterSingleton<ICertificateValidator, CertificateValidator>();

        // 注册签名验证器
        container.RegisterSingleton<ISignatureVerifier, SignatureVerifier>();
    }
}
