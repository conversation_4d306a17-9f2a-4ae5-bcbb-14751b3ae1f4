using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using DeviceGuard.Interface.Security;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Newtonsoft.Json.Serialization;
using Serilog;

namespace DeviceGuard.Shell.Security;

/// <summary>
/// 证书验证器实现
/// </summary>
public class SignatureVerifier : ISignatureVerifier
{
    private readonly ICertificateValidator _certificateValidator;
    private readonly ILogger               _logger;

    public SignatureVerifier(ICertificateValidator certificateValidator, ILogger logger)
    {
        _certificateValidator = certificateValidator;
        _logger               = logger;
    }

    /// <summary>
    /// 验证签名请求的正确性
    /// </summary>
    /// <remarks>
    /// 验证
    /// 1. request.SignCertificatePublicKey 是否为系统保存的 CFTech 的 root 证书颁发
    /// 2. 使用 request.SignCertificatePublicKey 验证 request.Signature 是否正确, 签名由 Data, Nonce 组成
    /// </remarks>
    /// <param name="request"></param>
    /// <param name="cancellation"></param>
    /// <returns></returns>
    public async Task<bool> ValidateAsync(SignatureRequest request, CancellationToken cancellation)
    {
        try
        {
            // 1. 验证必要字段
            if (string.IsNullOrEmpty(request.SignCertificatePublicKey))
            {
                _logger.Warning("签名验证失败: 缺少签名证书公钥");
                return false;
            }

            if (string.IsNullOrEmpty(request.Signature))
            {
                _logger.Warning("签名验证失败: 缺少签名数据");
                return false;
            }

            if (request.Data == null)
            {
                _logger.Warning("签名验证失败: 缺少原始数据");
                return false;
            }

            if (string.IsNullOrEmpty(request.Nonce))
            {
                _logger.Warning("签名验证失败: 缺少随机数");
                return false;
            }

            // 2. 验证签名证书是否由CFTech根证书颁发
            using var signCertificate = new X509Certificate2(Encoding.UTF8.GetBytes(request.SignCertificatePublicKey));

            bool isCertificateValid = await _certificateValidator.ValidateCertificateAsync(signCertificate, cancellation);
            if (!isCertificateValid)
            {
                _logger.Warning("签名验证失败: 签名证书未通过CFTech根证书验证");
                return false;
            }

            // 3. 验证数据签名 - 签名由 Data + Nonce + Timestamp 组成（与服务端算法匹配）
            // 按照服务端的格式构建签名数据结构
            var signatureData = new
            {
                Data      = request.Data,
                Nonce     = request.Nonce,
                Timestamp = request.SignatureTimestamp
            };

            // 使用与服务端相同的JSON序列化设置
            var signatureDataJson = JsonConvert.SerializeObject(signatureData,
                new JsonSerializerSettings
                {
                    Converters       = [new StringEnumConverter(new CamelCaseNamingStrategy())],
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                });
            var dataBytes      = Encoding.UTF8.GetBytes(signatureDataJson);
            var signatureBytes = Convert.FromBase64String(request.Signature);

            using var rsa = signCertificate.GetRSAPublicKey();
            if (rsa == null)
            {
                _logger.Warning("签名验证失败: 无法从签名证书中获取RSA公钥");
                return false;
            }

            // 验证签名
            bool isSignatureValid = rsa.VerifyData(dataBytes, signatureBytes, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);
            if (!isSignatureValid)
            {
                _logger.Warning("签名验证失败: 数据签名验证失败");
                return false;
            }

            _logger.Information("签名验证成功");
            return true;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "验证签名时发生异常");
            return false;
        }
    }
}
