# CFTech 数字签名验证系统

## 概述

本系统实现了基于RSA数字签名的证书验证功能，用于验证服务端返回的签名数据是否由CFTech根证书颁发的有效证书签名。

## 安全特性

- **根证书硬编码**: 根证书数据直接编译到程序中，防止外部篡改
- **指纹验证**: 双重验证根证书指纹，确保证书完整性
- **证书链验证**: 完整的X.509证书链验证
- **有效期检查**: 自动检查证书和根证书的有效期
- **签名验证**: RSA-SHA256数字签名验证

## 核心组件

### 1. CFTechRootCertificate (静态类)
- 存储硬编码的CFTech根证书数据
- 提供指纹验证功能
- 确保根证书数据无法被外部修改

### 2. ICertificateValidator (接口)
- 证书验证的核心接口
- 提供证书链验证功能
- 支持PEM格式证书验证

### 3. CertificateValidator (实现类)
- ICertificateValidator的具体实现
- 使用硬编码根证书进行验证
- 提供详细的验证结果

### 4. SignatureVerifier (签名验证器)
- 专门用于验证服务端返回的JSON签名数据
- 验证证书有效性和数据签名
- 支持完整的签名验证流程

## 使用方法

### 1. 依赖注入配置

系统已自动注册到依赖注入容器中：

```csharp
// 在 SecurityComponent 中已配置
container.RegisterSingleton<ICertificateValidator, CertificateValidator>();
container.RegisterSingleton<SignatureVerifier>();
```

### 2. 验证服务端签名数据

```csharp
// 注入依赖
private readonly ICertificateValidator _certificateValidator;
private readonly SignatureVerifier _signatureVerifier;

// 验证服务端返回的签名数据
public async Task<bool> ValidateServerResponse(string serverResponseJson)
{
    var result = await _signatureVerifier.VerifySignatureAsync(serverResponseJson);
    return result.IsValid;
}
```

### 3. 验证单个证书

```csharp
// 验证PEM格式证书
public async Task<bool> ValidateCertificate(string certificatePem)
{
    return await _certificateValidator.ValidateCertificateAsync(certificatePem);
}
```

### 4. 获取详细验证结果

```csharp
// 获取详细的证书验证信息
public async Task<CertificateValidationResult> GetDetailedResult(string certificatePem)
{
    using var certificate = new X509Certificate2(Encoding.UTF8.GetBytes(certificatePem));
    return await _certificateValidator.ValidateCertificateChainAsync(certificate);
}
```

## 服务端JSON格式

系统支持验证以下格式的服务端签名数据：

```json
{
  "data": {
    "MachineSerialNumber": "11",
    "UserId": "f141ee3c-b917-4559-95eb-cde8baea6bae",
    "UserName": "13577137251",
    "Plugins": [],
    "QueryTimestamp": "2025-06-26T23:55:31.1128013+08:00"
  },
  "signature": "O5uSn2SJ11TT7hXu8c7gtkKaSqWs/w7q75EXWVdkQJkDZIvgLR2BNMuZVsA0OenCtzo3/7+Kd8Yq6q8FSkETrUJKeRnE/BDSiKBG6MzD5oDvVPDiurAPC1FQMUvWd/R7H2mrwyCvmEnGnlMijQJ3fC+r/2kQoty3V69k1zqrX7NnFGdfUQAE6p/NkbvfnCQFpUFHEAnpkJJizl9Ko1rgU99bzGJrqm0apRFCgSyAVBdMu8AdK58CjuNG//VdlBGtogVzaBDNunJA0GfhuxeHcyHOrooGWi17Yz9Gswwil8Ij6NQYKazObdRSIH+sQXlf2YjpXqqgtaG06A+B6IM1lQ==",
  "signCertificatePublicKey": "-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----\n",
  "algorithm": "SHA256withRSA",
  "nonce": "Ph3XPxAlD85YqVmnuAHA8+Oilr1/TOg8wwUXnDqYrPA=",
  "signatureTimestamp": "2025-06-26T15:55:31.1245568Z",
  "dataHash": "eh+pKAI6U0MNT1PyBCFObBGI3GX94/2vfrRkhLuhtlM="
}
```

## 验证流程

1. **根证书验证**: 检查内置根证书的指纹和有效期
2. **证书链验证**: 验证签名证书是否由根证书颁发
3. **证书有效期检查**: 确保签名证书在有效期内
4. **数据签名验证**: 使用签名证书的公钥验证数据签名
5. **数据哈希验证**: 可选的数据完整性检查

## 错误处理

系统提供详细的错误信息：

- 证书解析错误
- 证书链验证失败
- 签名验证失败
- 证书过期或尚未生效
- 根证书指纹不匹配

## 日志记录

系统使用Serilog进行详细的日志记录，包括：

- 验证过程的关键步骤
- 错误和警告信息
- 证书信息和验证结果

## 安全注意事项

1. **根证书保护**: 根证书数据硬编码在程序中，无法被外部修改
2. **指纹验证**: 双重验证确保根证书完整性
3. **时间验证**: 自动检查证书有效期，防止使用过期证书
4. **算法安全**: 使用RSA-SHA256算法，符合现代安全标准

## 测试

使用 `CertificateValidationTest.TestCertificateValidationAsync()` 方法进行功能测试。
