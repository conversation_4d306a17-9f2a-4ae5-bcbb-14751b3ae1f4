using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using DeviceGuard.Interface.Security;
using Serilog;

namespace DeviceGuard.Shell.Security;

/// <summary>
/// 证书验证器实现
/// </summary>
public class CertificateValidator : ICertificateValidator
{
    private readonly ILogger _logger;
    private readonly Lazy<X509Certificate2> _rootCertificate;
    private readonly Lazy<CertificateInfo> _rootCertificateInfo;

    public CertificateValidator(ILogger logger)
    {
        _logger = logger;
        _rootCertificate = new Lazy<X509Certificate2>(LoadRootCertificate);
        _rootCertificateInfo = new Lazy<CertificateInfo>(CreateRootCertificateInfo);
    }

    /// <summary>
    /// 验证指定的证书是否由CFTech根证书颁发
    /// </summary>
    public async Task<bool> ValidateCertificateAsync(X509Certificate2 certificate, CancellationToken cancellationToken = default)
    {
        try
        {
            var result = await ValidateCertificateChainAsync(certificate, null, cancellationToken);
            return result.IsValid;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "验证证书时发生异常");
            return false;
        }
    }

    /// <summary>
    /// 验证指定的证书是否由CFTech根证书颁发
    /// </summary>
    public async Task<bool> ValidateCertificateAsync(string certificatePem, CancellationToken cancellationToken = default)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(certificatePem))
            {
                _logger.Warning("证书PEM字符串为空或null");
                return false;
            }

            using var certificate = new X509Certificate2(Encoding.UTF8.GetBytes(certificatePem));
            return await ValidateCertificateAsync(certificate, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "解析PEM证书时发生异常: {CertificatePem}", certificatePem);
            return false;
        }
    }

    /// <summary>
    /// 获取CFTech根证书信息
    /// </summary>
    public CertificateInfo GetRootCertificateInfo()
    {
        return _rootCertificateInfo.Value;
    }

    /// <summary>
    /// 验证证书链
    /// </summary>
    public async Task<CertificateValidationResult> ValidateCertificateChainAsync(
        X509Certificate2 certificate, 
        X509Certificate2[]? intermediateCertificates = null,
        CancellationToken cancellationToken = default)
    {
        await Task.CompletedTask; // 使方法异步

        try
        {
            var rootCert = _rootCertificate.Value;
            var now = DateTime.UtcNow;

            // 1. 检查证书有效期
            if (certificate.NotBefore > now)
            {
                return CertificateValidationResult.Failure("证书尚未生效");
            }

            if (certificate.NotAfter < now)
            {
                return CertificateValidationResult.Failure("证书已过期");
            }

            // 2. 检查根证书有效期
            if (rootCert.NotBefore > now)
            {
                return CertificateValidationResult.Failure("根证书尚未生效");
            }

            if (rootCert.NotAfter < now)
            {
                return CertificateValidationResult.Failure("根证书已过期");
            }

            // 3. 验证证书链
            using var chain = new X509Chain();
            
            // 配置链验证选项
            chain.ChainPolicy.RevocationMode = X509RevocationMode.NoCheck; // 不检查吊销列表
            chain.ChainPolicy.VerificationFlags = X509VerificationFlags.AllowUnknownCertificateAuthority;
            
            // 添加根证书到受信任的根证书存储
            chain.ChainPolicy.ExtraStore.Add(rootCert);
            
            // 添加中间证书（如果有）
            if (intermediateCertificates != null)
            {
                foreach (var intermediateCert in intermediateCertificates)
                {
                    chain.ChainPolicy.ExtraStore.Add(intermediateCert);
                }
            }

            // 构建证书链
            bool chainIsValid = chain.Build(certificate);

            if (!chainIsValid)
            {
                var errors = new List<string>();
                foreach (X509ChainStatus status in chain.ChainStatus)
                {
                    // 忽略不受信任的根证书错误，因为我们使用自己的根证书
                    if (status.Status != X509ChainStatusFlags.UntrustedRoot)
                    {
                        errors.Add($"证书链验证失败: {status.Status} - {status.StatusInformation}");
                    }
                }

                if (errors.Count > 0)
                {
                    return CertificateValidationResult.Failure(errors.ToArray());
                }
            }

            // 4. 验证证书是否由我们的根证书签发
            bool isIssuedByRoot = IsIssuedByRootCertificate(certificate, rootCert);
            if (!isIssuedByRoot)
            {
                return CertificateValidationResult.Failure("证书不是由CFTech根证书颁发");
            }

            _logger.Information("证书验证成功: {Subject}", certificate.Subject);
            return CertificateValidationResult.Success();
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "验证证书链时发生异常");
            return CertificateValidationResult.Failure($"验证过程中发生异常: {ex.Message}");
        }
    }

    /// <summary>
    /// 检查证书是否由指定的根证书颁发
    /// </summary>
    private bool IsIssuedByRootCertificate(X509Certificate2 certificate, X509Certificate2 rootCertificate)
    {
        try
        {
            // 检查颁发者是否匹配
            if (certificate.Issuer != rootCertificate.Subject)
            {
                _logger.Debug("证书颁发者不匹配: {CertIssuer} != {RootSubject}",
                    certificate.Issuer, rootCertificate.Subject);
                return false;
            }

            // 使用.NET内置的证书链验证来检查签名
            // 这比手动解析ASN.1更可靠
            using var chain = new X509Chain();
            chain.ChainPolicy.RevocationMode = X509RevocationMode.NoCheck;
            chain.ChainPolicy.VerificationFlags = X509VerificationFlags.AllowUnknownCertificateAuthority;
            chain.ChainPolicy.ExtraStore.Add(rootCertificate);

            bool isValid = chain.Build(certificate);

            // 检查链中是否包含我们的根证书
            bool containsOurRoot = chain.ChainElements
                .Cast<X509ChainElement>()
                .Any(element => element.Certificate.Thumbprint.Equals(rootCertificate.Thumbprint, StringComparison.OrdinalIgnoreCase));

            if (!containsOurRoot)
            {
                _logger.Debug("证书链中不包含CFTech根证书");
                return false;
            }

            // 检查是否有严重的链状态错误（忽略UntrustedRoot，因为我们使用自签名根证书）
            var seriousErrors = chain.ChainStatus
                .Where(status => status.Status != X509ChainStatusFlags.UntrustedRoot &&
                               status.Status != X509ChainStatusFlags.NoError)
                .ToList();

            if (seriousErrors.Any())
            {
                _logger.Warning("证书链存在问题: {Errors}",
                    string.Join(", ", seriousErrors.Select(e => e.Status.ToString())));
                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "验证证书签名时发生异常");
            return false;
        }
    }

    /// <summary>
    /// 加载根证书（从硬编码数据中读取，确保安全性）
    /// </summary>
    private X509Certificate2 LoadRootCertificate()
    {
        try
        {
            // 优先使用硬编码的根证书数据，确保无法被篡改
            var certBytes = Encoding.UTF8.GetBytes(CFTechRootCertificate.PublicKeyPem);
            var certificate = new X509Certificate2(certBytes);

            // 验证证书指纹是否匹配
            var actualFingerprint = CalculateCertificateFingerprint(certificate);
            if (!CFTechRootCertificate.IsValidFingerprint(actualFingerprint))
            {
                throw new InvalidOperationException($"根证书指纹验证失败。期望: {CFTechRootCertificate.Fingerprint}, 实际: {actualFingerprint}");
            }

            _logger.Information("成功加载CFTech根证书: {Subject}, 指纹: {Fingerprint}",
                certificate.Subject, actualFingerprint);

            return certificate;
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "加载根证书时发生异常");
            throw;
        }
    }

    /// <summary>
    /// 创建根证书信息（使用硬编码数据）
    /// </summary>
    private CertificateInfo CreateRootCertificateInfo()
    {
        try
        {
            var rootCert = _rootCertificate.Value;

            return new CertificateInfo
            {
                PublicKey = CFTechRootCertificate.PublicKeyPem,
                Issuer = CFTechRootCertificate.Issuer,
                Subject = rootCert.Subject,
                ValidFrom = CFTechRootCertificate.ValidFrom,
                ValidTo = CFTechRootCertificate.ValidTo,
                Fingerprint = CFTechRootCertificate.Fingerprint,
                SerialNumber = rootCert.SerialNumber,
                CertificateType = "Root CA"
            };
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "创建根证书信息时发生异常");
            throw;
        }
    }

    /// <summary>
    /// 计算证书指纹
    /// </summary>
    private string CalculateCertificateFingerprint(X509Certificate2 certificate)
    {
        using var sha256 = SHA256.Create();
        var hash = sha256.ComputeHash(certificate.RawData);
        return BitConverter.ToString(hash).Replace("-", "").ToLowerInvariant();
    }
}
