<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>WinExe</OutputType>
        <UseWPF>true</UseWPF>
        <TargetFramework>net6.0-windows</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <RootNamespace>DeviceGuard.Shell</RootNamespace>
        <GenerateAssemblyInfo>false</GenerateAssemblyInfo>
        <Platforms>x86;x64</Platforms>
        <ApplicationIcon>app.ico</ApplicationIcon>
        <LangVersion>latest</LangVersion>
        <!-- Suppress CA1416 warnings since this is a Windows-only WPF application -->
        <NoWarn>$(NoWarn);CA1416</NoWarn>
    </PropertyGroup>

    <PropertyGroup Condition="'$(PlatformTarget)' == 'x64' AND '$(Configuration)' == 'Debug'">
        <RuntimeIdentifier Condition="'$(RuntimeIdentifier)' == ''">win-x64</RuntimeIdentifier>
        <OutputPath>..\..\bin\Debug\</OutputPath>
        <SelfContained Condition="'$(SelfContained)' == ''">false</SelfContained>
        <NoWarn>1701;1702;CA1416</NoWarn>
    </PropertyGroup>

	<PropertyGroup Condition="'$(PlatformTarget)' == 'x64' AND '$(Configuration)' == 'Release'">
		<RuntimeIdentifier Condition="'$(RuntimeIdentifier)' == ''">win-x64</RuntimeIdentifier>
		<OutputPath>..\..\bin\Release\</OutputPath>
        <PublishDir>$(OutputPath)publish\</PublishDir>
		<SelfContained Condition="'$(SelfContained)' == ''">false</SelfContained>
		<NoWarn>1701;1702;CA1416</NoWarn>
	</PropertyGroup>

    <ItemGroup>
        <PackageReference Include="CefSharp.Wpf.NETCore" Version="135.0.220" />
        <PackageReference Include="LinqAsync" Version="1.1.0" />
        <PackageReference Include="MaterialDesignThemes" Version="5.2.1" />
        <PackageReference Include="Prism.Unity" Version="9.0.537" />
        <PackageReference Include="Prism.Wpf" Version="9.0.537" />
        <PackageReference Include="RequiredMemberAttribute" Version="1.0.0">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
        <PackageReference Include="PropertyChanged.Fody" Version="4.1.0">
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="System.Management" Version="6.0.2" />
        <PackageReference Include="Tools.InnoSetup" Version="6.4.3" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Interfaces\DeviceGuard.Interface\DeviceGuard.Interface.csproj" />
        <ProjectReference Include="..\Interfaces\DeviceGuard.Modules.Interface\DeviceGuard.Modules.Interface.csproj" />
        <ProjectReference Include="..\Interfaces\DeviceGuard.Windows.Interface\DeviceGuard.Windows.Interface.csproj" />
        <ProjectReference Include="..\Services\Configuration\Configuration.csproj" />
        <ProjectReference Include="..\Services\DeviceGuard.Infrastructure\DeviceGuard.Infrastructure.csproj" />
        <ProjectReference Include="..\Services\DeviceGuard.Module.Core\DeviceGuard.Module.Core.csproj" />
        <ProjectReference Include="..\UI\DeviceGuard.Styles\DeviceGuard.Styles.csproj" />
        <ProjectReference Include="..\UI\DeviceGuard.Windows.Infrastructure\DeviceGuard.Windows.Infrastructure.csproj" />
        <ProjectReference Include="..\UI\DeviceGuard.Windows.Utility\DeviceGuard.Windows.Utility.csproj" />
    </ItemGroup>

    <ItemGroup>
        <Page Update="Views\Main\MainWindow.xaml">
            <Generator>MSBuild:Compile</Generator>
            <XamlRuntime>Wpf</XamlRuntime>
            <SubType>Designer</SubType>
        </Page>
        <Page Update="Views\PluginSelection\PluginSelectionView.xaml">
          <Generator>MSBuild:Compile</Generator>
          <XamlRuntime>Wpf</XamlRuntime>
          <SubType>Designer</SubType>
        </Page>
        <Page Update="Views\IndexPage\PageNotFoundView.xaml">
          <Generator>MSBuild:Compile</Generator>
          <XamlRuntime>Wpf</XamlRuntime>
          <SubType>Designer</SubType>
        </Page>
        <Page Update="Views\Pages\PluginSelection\PluginSelectionView.xaml">
          <Generator>MSBuild:Compile</Generator>
        </Page>
        <Page Update="Views\Pages\SettingsPage\SettingsPageView.xaml">
          <Generator>MSBuild:Compile</Generator>
          <XamlRuntime>Wpf</XamlRuntime>
          <SubType>Designer</SubType>
        </Page>
        <Page Update="Views\Pages\SearchPage\SearchPageView.xaml">
          <Generator>MSBuild:Compile</Generator>
          <XamlRuntime>Wpf</XamlRuntime>
          <SubType>Designer</SubType>
        </Page>
        <Page Update="Views\Pages\HistoryPage\HistoryPageView.xaml">
          <Generator>MSBuild:Compile</Generator>
          <XamlRuntime>Wpf</XamlRuntime>
          <SubType>Designer</SubType>
        </Page>
        <Page Update="Views\Pages\MyAccountPage\MyAccountPageView.xaml">
          <Generator>MSBuild:Compile</Generator>
          <XamlRuntime>Wpf</XamlRuntime>
          <SubType>Designer</SubType>
        </Page>
        <Page Update="Views\PluginMeta\PluginMetaView.xaml">
          <Generator>MSBuild:Compile</Generator>
        </Page>
        <Page Update="Views\Pages\SearchPage\SearchResultPageView.xaml">
          <Generator>MSBuild:Compile</Generator>
          <XamlRuntime>Wpf</XamlRuntime>
          <SubType>Designer</SubType>
        </Page>
        <Page Update="Views\CategoryDetails\CategoryDetailsView.xaml">
          <Generator>MSBuild:Compile</Generator>
          <XamlRuntime>Wpf</XamlRuntime>
          <SubType>Designer</SubType>
        </Page>
    </ItemGroup>

    <ItemGroup>
      <None Remove="Resources\nepton.jpg" />
      <Content Include="app.ico" />
      <Content Include="Resources\nepton.jpg">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </Content>

    </ItemGroup>

    <ItemGroup>
      <Reference Include="Microsoft.Xaml.Behaviors">
        <HintPath>C:\Users\<USER>\.nuget\packages\microsoft.xaml.behaviors.wpf\1.1.122\lib\net6.0-windows7.0\Microsoft.Xaml.Behaviors.dll</HintPath>
      </Reference>
    </ItemGroup>

    <ItemGroup>
      <Compile Update="Views\Pages\CategoryPage\CategoryPageView.cs">
        <DependentUpon>CategoryPageView.xaml</DependentUpon>
      </Compile>
      <Compile Update="Views\Pages\SettingsPage\SettingsPageView.cs">
        <DependentUpon>SettingsPageView.xaml</DependentUpon>
      </Compile>
      <Compile Update="Views\Pages\SearchPage\SearchPageView.cs">
        <DependentUpon>SearchPageView.xaml</DependentUpon>
      </Compile>
      <Compile Update="Views\Pages\MyAccountPage\MyAccountPageView.cs">
        <DependentUpon>MyAccountPageView.xaml</DependentUpon>
      </Compile>
      <Compile Update="Views\Pages\RecentPage\RecentPageView.cs">
        <DependentUpon>RecentPageView.xaml</DependentUpon>
      </Compile>
      <Compile Update="Views\Pages\HistoryPage\HistoryPageView.cs">
        <DependentUpon>HistoryPageView.xaml</DependentUpon>
      </Compile>
      <Compile Update="Views\Pages\SearchPage\SearchResultPageView.cs">
        <DependentUpon>SearchResultPageView.xaml</DependentUpon>
      </Compile>
      <Compile Remove="Security\CertificateValidationTest.cs" />
      <Compile Remove="Security\UsageExample.cs" />
    </ItemGroup>

    <ItemGroup>
      <None Remove="Resources\Undefined.ico" />
      <Resource Include="Resources\Undefined.ico" />
      <None Remove="Resources\Debug.ico" />
      <Resource Include="Resources\Debug.ico" />
    </ItemGroup>

    <ItemGroup>
      <None Remove="Properties\changelog.json" />
      <EmbeddedResource Include="Properties\changelog.json" />
    </ItemGroup>

    <Target Name="PreBuild" BeforeTargets="PreBuildEvent">
      <Exec Command="..\..\utils\ReleaseVersionConsole -g ./Properties/changelog.json" />
    </Target>
</Project>
