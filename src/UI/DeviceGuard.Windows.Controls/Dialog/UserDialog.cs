using System;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using DeviceGuard.Windows.Utility.Dialogs;

namespace DeviceGuard.Windows.Controls.Dialog;

/// <summary>
/// UserDialog just like the UserControl
/// The different is UserDialog can show by implement IDialog
/// </summary>
public class UserDialog : UserControl, IDialogView
{
    static UserDialog()
    {
        DefaultStyleKeyProperty.OverrideMetadata(typeof(UserDialog),
            new FrameworkPropertyMetadata(typeof(UserDialog)));
    }

    public const string PartCloseButton = "Part_CloseButton";

    public override void OnApplyTemplate()
    {
        base.OnApplyTemplate();

        if (GetTemplateChild(PartCloseButton) is Button button)
        {
            button.Click -= CloseButton_Click;
            button.Click += CloseButton_Click;
        }
    }

    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }

    public bool IsCloseButtonVisible
    {
        get => (bool)GetValue(IsCloseButtonVisibleProperty);
        set => SetValue(IsCloseButtonVisibleProperty, value);
    }

    // Using a DependencyProperty as the backing store for ShowCloseButton.  This enables animation, styling, binding, etc...
    public static readonly DependencyProperty IsCloseButtonVisibleProperty =
        DependencyProperty.Register(nameof(IsCloseButtonVisible),
            typeof(bool),
            typeof(UserDialog),
            new PropertyMetadata(true));

    public bool IsTitlebarVisible
    {
        get => (bool)GetValue(IsTitlebarVisibleProperty);
        set => SetValue(IsTitlebarVisibleProperty, value);
    }

    // Using a DependencyProperty as the backing store for ShowTitlebar.  This enables animation, styling, binding, etc...
    public static readonly DependencyProperty IsTitlebarVisibleProperty =
        DependencyProperty.Register(nameof(IsTitlebarVisible),
            typeof(bool),
            typeof(UserDialog),
            new PropertyMetadata(true));

    /// <summary>
    /// The title text. user can simply set the title by the string
    /// </summary>
    public string Title
    {
        get => (string)GetValue(TitleProperty);
        set => SetValue(TitleProperty, value);
    }

    // Using a DependencyProperty as the backing store for Title.  This enables animation, styling, binding, etc...
    public static readonly DependencyProperty TitleProperty =
        DependencyProperty.Register(nameof(Title),
            typeof(string),
            typeof(UserDialog),
            new FrameworkPropertyMetadata(null,
                FrameworkPropertyMetadataOptions.AffectsArrange | FrameworkPropertyMetadataOptions.AffectsRender));

    /// <summary>
    /// The template of title
    /// </summary>
    public DataTemplate TitleTemplate
    {
        get => (DataTemplate)GetValue(TitleTemplateProperty);
        set => SetValue(TitleTemplateProperty, value);
    }

    // Using a DependencyProperty as the backing store for TitleTemplate.  This enables animation, styling, binding, etc...
    public static readonly DependencyProperty TitleTemplateProperty =
        DependencyProperty.Register(nameof(TitleTemplate),
            typeof(DataTemplate),
            typeof(UserDialog),
            new FrameworkPropertyMetadata(null,
                FrameworkPropertyMetadataOptions.AffectsArrange | FrameworkPropertyMetadataOptions.AffectsRender));


    /// <summary>
    /// 标题条的背景色
    /// </summary>
    public Brush TitleBarBackground
    {
        get => (Brush)GetValue(TitleBarBackgroundProperty);
        set => SetValue(TitleBarBackgroundProperty, value);
    }

    // Using a DependencyProperty as the backing store for TitleBackground.  This enables animation, styling, binding, etc...
    public static readonly DependencyProperty TitleBarBackgroundProperty =
        DependencyProperty.Register(nameof(TitleBarBackground),
            typeof(Brush),
            typeof(UserDialog),
            new FrameworkPropertyMetadata(Brushes.White, FrameworkPropertyMetadataOptions.AffectsRender));


    /// <summary>
    /// 标题条前景色
    /// </summary>
    public Brush TitleBarForeground
    {
        get => (Brush)GetValue(TitleBarForegroundProperty);
        set => SetValue(TitleBarForegroundProperty, value);
    }

    // Using a DependencyProperty as the backing store for TitleBarForeground.  This enables animation, styling, binding, etc...
    public static readonly DependencyProperty TitleBarForegroundProperty =
        DependencyProperty.Register(nameof(TitleBarForeground),
            typeof(Brush),
            typeof(UserDialog),
            new FrameworkPropertyMetadata(Brushes.Black, FrameworkPropertyMetadataOptions.AffectsRender));


    /// <summary>
    /// 对话框的结果
    /// </summary>
    public bool? DialogResult
    {
        get => (bool?)GetValue(DialogResultProperty);
        set => SetValue(DialogResultProperty, value);
    }

    /// <summary>
    /// 对话框的控制方法
    /// </summary>
    public IDialogViewController? DialogController { get; set; }

    // Using a DependencyProperty as the backing store for DialogResult.  This enables animation, styling, binding, etc...
    public static readonly DependencyProperty DialogResultProperty =
        DependencyProperty.Register(nameof(DialogResult),
            typeof(bool?),
            typeof(UserDialog),
            new PropertyMetadata(null, DialogResultChangedEvent));

    private static void DialogResultChangedEvent(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is UserDialog { DialogResult: { } } dialog)
            dialog.Close();
    }

    public async void Close()
    {
        try
        {
            if (DialogController is not { } dialogController)
                throw new InvalidOperationException("DialogController is null");
            
            await dialogController.CloseAsync();
        }
        catch (Exception)
        {
            // no code here
        }
    }

    /// <summary>
    /// 处理关闭前, User can cancellation the close action
    /// </summary>
    /// <param name="cancelEventArgs"></param>
    public virtual void OnClosing(CancelEventArgs cancelEventArgs)
    {
    }
}
