<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
                    xmlns:dialog="clr-namespace:DeviceGuard.Windows.Controls.Dialog">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="pack://application:,,,/DeviceGuard.Windows.Controls;component/Controls/GradientFlatButtonStyle.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />

    <SolidColorBrush x:Key="UserDialog.TitleBar.Background"
                     Color="{DynamicResource Primary100}" />

    <SolidColorBrush x:Key="UserDialog.TitleBar.Foreground"
                     Color="Black" />

    <Style TargetType="{x:Type dialog:UserDialog}">
        <Setter Property="TitleBarBackground" Value="{DynamicResource UserDialog.TitleBar.Background}" />
        <Setter Property="TitleBarForeground" Value="{DynamicResource UserDialog.TitleBar.Foreground}" />
        <Setter Property="Background" Value="#FFF" />
        <Setter Property="BorderThickness"
                Value="0" />
        <Setter Property="BorderBrush"
                Value="#CCC" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type dialog:UserDialog}">
                    <Border Background="{TemplateBinding Background}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            Padding="{TemplateBinding Padding}"
                            SnapsToDevicePixels="true">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto" />
                                <RowDefinition Height="*" />
                            </Grid.RowDefinitions>
                            <Grid Grid.Row="0"
                                  Background="{TemplateBinding TitleBarBackground}"
                                  Visibility="{TemplateBinding IsTitlebarVisible,Converter={StaticResource BooleanToVisibilityConverter}}">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*" />
                                    <ColumnDefinition Width="Auto" />
                                </Grid.ColumnDefinitions>
                                <ContentControl Content="{TemplateBinding Title}"
                                                ContentTemplate="{TemplateBinding TitleTemplate}"
                                                FontSize="16"
                                                VerticalAlignment="Center"
                                                Margin="15 8 0 8" />
                                <Button Grid.Column="1"
                                        Background="Transparent"
                                        MinHeight="30"
                                        Width="{Binding ElementName=Part_CloseButton,Path=ActualHeight}"
                                        Style="{StaticResource ButtonStyle.GradientFlatButton}"
                                        x:Name="Part_CloseButton">
                                    <materialDesign:PackIcon Kind="WindowClose" />
                                </Button>
                            </Grid>
                            <ContentPresenter Grid.Row="1"
                                              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}" />
                        </Grid>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>