using System;
using System.Windows.Input;

namespace DeviceGuard.Windows.Controls.Extensions;

public static class CommandExtensions
{
    /// <summary>
    /// 尝试执行命令, 如果 CanExecute
    /// </summary>
    /// <param name="source"></param>
    /// <param name="parameter"></param>
    /// <returns></returns>
    public static bool TryExecute(this ICommand source, object? parameter)
    {
        if (source == null)
            throw new ArgumentNullException(nameof(source));

        try
        {
            if (!source.CanExecute(parameter))
                return false;

            source.Execute(parameter);
            return true;
        }
        catch
        {
            // ignored
        }

        return false;
    }

    /// <summary>
    /// 尝试执行命令
    /// </summary>
    /// <param name="source"></param>
    /// <returns></returns>
    public static bool TryExecute(this ICommand source)
    {
        return TryExecute(source, null);
    }
}