using System.Windows;

namespace DeviceGuard.Windows.Controls.Extensions;

public static class FrameworkElementExtensions
{
    public static bool IsChildrenOf(this FrameworkElement source, DependencyObject parent)
    {
        if (source == null)
            return false;

        var p = source.Parent as FrameworkElement;
        while (p != null)
        {
            if (p == parent)
                return true;

            p = p.Parent as FrameworkElement;
        }

        return false;
    }
}