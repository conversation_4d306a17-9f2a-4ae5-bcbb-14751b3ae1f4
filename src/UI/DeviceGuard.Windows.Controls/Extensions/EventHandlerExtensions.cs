using System;

namespace DeviceGuard.Windows.Controls.Extensions;

/// <summary>
/// EventHandler 的扩展函数
/// </summary>
public static class EventHandlerExtensions
{
    public static bool TryInvoke<TEventArgs>(this EventHandler<TEventArgs> source, object owner, TEventArgs args)
    {
        if (source == null)
            throw new ArgumentNullException(nameof(source));

        try
        {
            source.Invoke(owner, args);
            return true;
        }
        catch
        {
            // ignored
        }

        return false;
    }

    public static bool TryInvoke(this EventHandler source, object owner)
    {
        if (source == null)
            throw new ArgumentNullException(nameof(source));

        try
        {
            source.Invoke(owner, EventArgs.Empty);
            return true;
        }
        catch
        {
            // ignored
        }

        return false;
    }
}