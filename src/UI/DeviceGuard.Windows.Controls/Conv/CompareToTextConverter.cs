using System;
using System.Globalization;
using System.Windows.Data;

namespace DeviceGuard.Windows.Controls.Conv;

public class CompareToTextConverter : IValueConverter
{
    /// <summary>
    /// 比较方式
    /// </summary>
    public CompareOperation Operation { get; set; } = CompareOperation.Equal;

    /// <summary>
    /// 比较的参考期望值
    /// </summary>
    public object? Expected { get; set; }

    public string? True  { get; set; }
    public string? False { get; set; }

    #region Implementation of IValueConverter

    /// <summary>Converts a value.</summary>
    /// <param name="value">The value produced by the binding source.</param>
    /// <param name="targetType">The type of the binding target property.</param>
    /// <param name="parameter">The converter parameter to use.</param>
    /// <param name="culture">The culture to use in the converter.</param>
    /// <returns>A converted value. If the method returns <see langword="null" />, the valid null value is used.</returns>
    public object Convert(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        if (value is not IComparable comp)
            return False ?? throw new ArgumentException("'False' is not set");

        // 执行比较
        var r = comp.CompareTo(Expected);

        // 判断比较结果
        var isTrue = Operation switch
        {
            CompareOperation.Equal      => r == 0,
            CompareOperation.NotEqual   => r != 0,
            CompareOperation.GreatEqual => r >= 0,
            CompareOperation.Great      => r > 0,
            CompareOperation.LessEqual  => r <= 0,
            CompareOperation.Less       => r < 0,
            _                           => throw new ArgumentOutOfRangeException()
        };

        return isTrue
            ? True ?? throw new ArgumentException("'True' is not set")
            : False ?? throw new ArgumentException("'False' is not set");
    }

    /// <summary>Converts a value.</summary>
    /// <param name="value">The value that is produced by the binding target.</param>
    /// <param name="targetType">The type to convert to.</param>
    /// <param name="parameter">The converter parameter to use.</param>
    /// <param name="culture">The culture to use in the converter.</param>
    /// <returns>A converted value. If the method returns <see langword="null" />, the valid null value is used.</returns>
    public object ConvertBack(object? value, Type targetType, object? parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }

    #endregion
}
