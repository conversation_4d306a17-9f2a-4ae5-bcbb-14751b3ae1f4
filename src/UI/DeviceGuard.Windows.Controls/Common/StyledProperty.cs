using System;
using System.Windows;

namespace DeviceGuard.Windows.Controls.Common;

public sealed class StyledProperty<TValue>
{
    public DependencyProperty Property { get; }

    public StyledProperty(DependencyProperty property)
    {
        Property = property;
    }

    public StyledProperty<TValue> AddOwner<TOwner>() =>
        new StyledProperty<TValue>(Property.AddOwner(typeof(TOwner)));

    public Type PropertyType => Property.PropertyType;
}