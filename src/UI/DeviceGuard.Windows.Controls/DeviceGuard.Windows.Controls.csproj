<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <Platforms>AnyCPU;x64</Platforms>
    <!-- Suppress CA1416 warnings since this is a Windows-only WPF library -->
    <NoWarn>$(NoWarn);CA1416</NoWarn>
  </PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
		<DebugType>none</DebugType>
		<DebugSymbols>false</DebugSymbols>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
	  <DebugType>none</DebugType>
	  <DebugSymbols>false</DebugSymbols>
	</PropertyGroup>
  <ItemGroup>
    <PackageReference Include="MaterialDesignThemes" Version="5.2.1" />
    <PackageReference Include="Microsoft.Xaml.Behaviors.Wpf" Version="1.1.135" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\DeviceGuard.Windows.Utility\DeviceGuard.Windows.Utility.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Page Update="Themes\Generic.xaml">
      <XamlRuntime>$(DefaultXamlRuntime)</XamlRuntime>
      <SubType>Designer</SubType>
    </Page>
    <Page Update="Controls\GradientFlatButtonStyle.xaml">
      <XamlRuntime>$(DefaultXamlRuntime)</XamlRuntime>
      <SubType>Designer</SubType>
    </Page>
  </ItemGroup>

</Project>
