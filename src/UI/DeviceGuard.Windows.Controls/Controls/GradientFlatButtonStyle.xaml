<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:controls="clr-namespace:DeviceGuard.Windows.Controls">
    <Style x:Key="FocusVisual">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Rectangle Margin="2"
                               SnapsToDevicePixels="true"
                               Stroke="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}"
                               StrokeThickness="1"
                               StrokeDashArray="1 2" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style x:Key="ButtonStyle.GradientFlatButton"
           TargetType="{x:Type ButtonBase}">
        <Setter Property="FocusVisualStyle"
                Value="{StaticResource FocusVisual}" />
        <Setter Property="Background"
                Value="#FFF" />
        <Setter Property="BorderBrush"
                Value="#CCC" />
        <Setter Property="Foreground"
                Value="#000" />
        <Setter Property="FontSize"
                Value="16" />
        <Setter Property="BorderThickness"
                Value="0" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border x:Name="border"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Background="{TemplateBinding Background}"
                            SnapsToDevicePixels="true">
                        <Grid>
                            <ContentPresenter x:Name="contentPresenter"
                                              Focusable="False"
                                              HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              Margin="{TemplateBinding Padding}"
                                              RecognizesAccessKey="True"
                                              SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                                              VerticalAlignment="{TemplateBinding VerticalContentAlignment}" />
                            <Rectangle Name="coverRectangle"
                                       Fill="{TemplateBinding Foreground}"
                                       Opacity="0"
                                       HorizontalAlignment="Stretch"
                                       VerticalAlignment="Stretch" />
                        </Grid>

                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsDefaulted"
                                 Value="true">
                            <Setter Property="BorderBrush"
                                    TargetName="border"
                                    Value="{DynamicResource {x:Static SystemColors.HighlightBrushKey}}" />

                        </Trigger>
                        <Trigger Property="IsMouseOver"
                                 Value="true">
                            <Setter Property="Opacity"
                                    TargetName="coverRectangle"
                                    Value="0.3" />
                        </Trigger>
                        <Trigger Property="IsPressed"
                                 Value="true">
                            <Setter Property="Opacity"
                                    TargetName="coverRectangle"
                                    Value="0.5" />
                        </Trigger>

                        <Trigger Property="IsEnabled"
                                 Value="false">
                            <Setter Property="Fill"
                                    TargetName="coverRectangle"
                                    Value="LightGray" />
                            <Setter Property="Opacity"
                                    TargetName="coverRectangle"
                                    Value="0.7" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>


</ResourceDictionary>
