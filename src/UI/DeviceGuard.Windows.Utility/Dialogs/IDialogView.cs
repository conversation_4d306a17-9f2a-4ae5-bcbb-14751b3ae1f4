using System.ComponentModel;

namespace DeviceGuard.Windows.Utility.Dialogs
{
    /// <summary>
    /// This is a dialog can pop up the window
    /// 用户需要继承于 IDialogView 接口, IDialog 接口才能够显示对话框
    /// </summary>
    public interface IDialogView
    {
        /// <summary>
        /// 对话框的结果
        /// </summary>
        bool? DialogResult { get; set; }

        /// <summary>
        /// 对话框的控制方法
        /// </summary>
        IDialogViewController? DialogController { get; set; }

        /// <summary>
        /// 对话框关闭前的提示
        /// </summary>
        /// <param name="closeEvent"></param>
        void OnClosing(CancelEventArgs closeEvent);
    }
}
