using System;
using System.Threading.Tasks;
using System.Windows;

namespace DeviceGuard.Windows.Utility.Dialogs
{
    /// <summary>
    /// 消息框接口
    /// </summary>
    public interface IMessageBox
    {
        /// <summary>
        /// 显示消息对话框
        /// </summary>
        /// <returns></returns>
        Task<MessageBoxResult> ShowAsync(string text, MessageBoxSetting? settingAction = null);

        /// <summary>
        /// 显示一个信息
        /// </summary>
        public Task ShowInfoAsync(string text, Action<MessageBoxSetting>? settingAction = null)
        {
            var option = new MessageBoxSetting
            {
                Title  = "提示",
                Button = MessageBoxButton.OK,
                Icon   = MessageBoxImage.Information,
            };
            settingAction?.Invoke(option);

            return ShowAsync(text, option);
        }

        /// <summary>
        /// 显示一个警告
        /// </summary>
        public Task ShowWarningAsync(string text, Action<MessageBoxSetting>? settingAction = null)
        {
            var option = new MessageBoxSetting
            {
                Title  = "警告",
                Button = MessageBoxButton.OK,
                Icon   = MessageBoxImage.Warning,
            };
            settingAction?.Invoke(option);

            return ShowAsync(text, option);
        }

        /// <summary>
        /// 显示一个等待用户确认询问
        /// </summary>
        public async Task<bool?> ShowQuestionAsync(string text, Action<MessageBoxSetting>? settingAction = null)
        {
            var option = new MessageBoxSetting
            {
                Title  = "请选择",
                Button = MessageBoxButton.YesNo,
                Icon   = MessageBoxImage.Question,
            };
            settingAction?.Invoke(option);

            if (option.Button == MessageBoxButton.OK)
                throw new NotSupportedException();

            var result = await ShowAsync(text, option);
            return result switch
            {
                MessageBoxResult.None   => null,
                MessageBoxResult.OK     => true,
                MessageBoxResult.Cancel => null,
                MessageBoxResult.Yes    => true,
                MessageBoxResult.No     => false,
                _                       => throw new ArgumentOutOfRangeException()
            };
        }

        /// <summary>
        /// 显示故障
        /// </summary>
        /// <param name="message"></param>
        /// <param name="settingAction"></param>
        /// <returns></returns>
        public Task ShowErrorAsync(string message, Action<MessageBoxSetting>? settingAction = null)
        {
            var option = new MessageBoxSetting
            {
                Title  = "故障",
                Button = MessageBoxButton.OK,
                Icon   = MessageBoxImage.Error,
            };
            settingAction?.Invoke(option);

            return ShowAsync(message, option);
        }

        /// <summary>
        /// 显示异常消息
        /// </summary>
        /// <param name="ex">要显示的异常对象</param>
        /// <param name="message">显示的异常信息</param>
        /// <param name="settingAction">用于自定义消息框设置的操作</param>
        /// <returns>一个任务，表示异步操作的结果</returns>
        public Task ShowExceptionAsync(Exception ex, string message, Action<MessageBoxSetting>? settingAction = null)
        {
            if (ex is AggregateException ae)
            {
                ex = ae.InnerExceptions[0];
            }

            return ShowErrorAsync($"{message}: {ex.Message}\n\n{ex.StackTrace}", settingAction);
        }
    }
}
 