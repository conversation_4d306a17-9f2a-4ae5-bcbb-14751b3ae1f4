namespace DeviceGuard.Windows.Utility
{
    /// <summary>
    /// 窗口标题管理
    /// </summary>
    public interface IMainWindowTitle
    {
        /// <summary>
        /// 更改窗口标题
        /// </summary>
        /// <param name="title"></param>
        /// <param name="description"></param>
        void ChangeTitle(string title, string description);

        /// <summary>
        /// 更改窗口标题的描述部分
        /// </summary>
        /// <param name="description"></param>
        void ChangeDescription(string description);
    }
}
