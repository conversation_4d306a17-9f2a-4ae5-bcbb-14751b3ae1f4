using System.Collections;
using System.Collections.Specialized;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using Dragablz;
using Prism.Navigation.Regions;

namespace DeviceGuard.Windows.Utility.Prism.RegionAdapters
{
    /// <summary>
    /// RegionManager.RequestNavigator 时如何把内容添加至 TabablzControl
    /// </summary>
    public class TabablzControlRegionAdapter : RegionAdapterBase<TabablzControl>
    {
        public TabablzControlRegionAdapter(IRegionBehaviorFactory regionBehaviorFactory) : base(regionBehaviorFactory)
        {
        }

        protected override void Adapt(IRegion region, TabablzControl regionTarget)
        {
            region.ActiveViews.CollectionChanged += (_, e) =>
            {
                switch (e.Action)
                {
                    case NotifyCollectionChangedAction.Add:
                        foreach (var newItem in e.NewItems ?? new ArrayList())
                        {
                            var tb = new TabItem();

                            do
                            {
                                if (newItem is ITabItemHeader tabItemBinding)
                                {
                                    tb.SetBinding(HeaderedContentControl.HeaderProperty, CreateHeaderBinding(tabItemBinding));
                                    break;
                                }

                                if (newItem is FrameworkElement { DataContext: ITabItemHeader tabItemViewModelBinding })
                                {
                                    tb.SetBinding(HeaderedContentControl.HeaderProperty, CreateHeaderBinding(tabItemViewModelBinding));
                                    break;
                                }

                                tb.Header = newItem.GetType().ToString();
                            } while (false);

                            tb.Content = newItem;
                            regionTarget.Items.Insert(regionTarget.Items.Count, tb);
                            regionTarget.SelectedIndex = regionTarget.Items.Count - 1;
                        }

                        break;
                    case NotifyCollectionChangedAction.Remove:
                        foreach (var oldItem in e.OldItems ?? new ArrayList())
                        {
                            for (var i = 0; i < regionTarget.Items.Count; i++)
                            {
                                var tab = (TabItem)regionTarget.Items[i];
                                if (tab?.Content == oldItem)
                                {
                                    regionTarget.Items.Remove(tab);
                                }
                            }

                            regionTarget.SelectedIndex = regionTarget.Items.Count - 1;
                        }

                        break;
                }
            };
        }

        private Binding CreateHeaderBinding(ITabItemHeader tabItemBinding)
        {
            var binding = new Binding(nameof(tabItemBinding.Header))
            {
                Source = tabItemBinding
            };

            return binding;
        }

        protected override IRegion CreateRegion()
        {
            return new AllActiveRegion();
        }
    }
}
