using System.ComponentModel;

namespace DeviceGuard.Windows.Utility.Prism.RegionAdapters
{
    /// <summary>
    /// 通过 RegionManager 添加至 TabControl 的视图的 Header
    /// </summary>
    /// <remarks>
    /// 记住首先注册 Behavior: regionBehaviors.AddIfMissing(RegionHostedInTabControlAwareBehavior.BEHAVIOR_KEY);
    /// 具体详见 readme.md
    ///
    /// 注意: 如果 TabControl 需要在没有内容时隐藏, 必须设置为 Hidden 而不是 Collapsed, 以保证 Loaded 完成, 否则将无法找到对应的 TabItem 导致异常
    /// </remarks>
    public interface ITabItemHeader : INotifyPropertyChanged
    {
        /// <summary>
        /// the title of tab item
        /// </summary>
        string Header { get; }
    }
}
