| Property     | Value          
|:-------------|:-----------------------
| Author       | <PERSON> Li       


# Summary

在 TabControl 中使用 RegionManager 时, 会出现 Tab item Header 空白情况, 
因为 Prism 写入的就是一个 View, 而不是 TabItem 导致, 如下图

```xml
<TabControl mvvm:RegionManager.RegionName="Default" />
```

```c#
_regionManager.AddToRegion("Default", new CalibrateView());
_regionManager.AddToRegion("Default", new CalibrateView());
```

有各种方法解决这个问题, (例如在 AGC 中, 使用了一种折中的方法, 但是依然不好).


# Solution

在 app.xaml.cs 中, 实现方法
```C#
protected override void ConfigureRegionAdapterMappings(RegionAdapterMappings regionAdapterMappings)
{
    regionAdapterMappings.RegisterMapping<TabControl, TabControlRegionAdapter>();   // Tab Control
    regionAdapterMappings.RegisterMapping<TabablzControl, TabablzControlRegionAdapter>();   // Tabablz Control

    base.ConfigureRegionAdapterMappings(regionAdapterMappings);
}
```

在 View 中实现接口 ITabItemHeader, 并返回 Header 名.

> 注意: 也可以在对应的 ViewModel 中实现 ITabItemHeader 接口, 效果一样


```C#
public partial class CalibrateView: ITabItemHeader
{
    public CalibrateView()
    {
        InitializeComponent();
    }

    /// <summary>
    /// 返回处于 TabItem 的 Header
    /// </summary>
    public object Header { get; set; } = "Your header will display in TabControl :D";
}
```



Done :D

--- 

这是目前最完美的解决 TabControl 标题的方法


# Note

1. 如果 TabControl 需要在没有内容时隐藏, 必须设置为 Hidden 而不是 Collapsed, 以保证 Loaded 完成, 否则将无法找到对应的 TabItem 导致异常
2. 注意通过绑定后, TabItem.Header 的值将被更新为指定的字符串, 如果要使用`TabControl.ItemTemplate` 则绑定项为一个字符串

```xml
<TabControl.ItemTemplate>
    <DataTemplate DataType="system:String">
        <StackPanel Orientation="Horizontal">
            <TextBlock Text="{Binding}" />
        </StackPanel>
    </DataTemplate>
</TabControl.ItemTemplate>
```
