using System.Collections;
using System.Collections.Specialized;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using Prism.Navigation.Regions;

namespace DeviceGuard.Windows.Utility.Prism.RegionAdapters
{
    /// <summary>
    /// RegionManager.RequestNavigator 时如何把内容添加至 TabablzControl
    /// </summary>
    public class TabControlRegionAdapter : RegionAdapterBase<TabControl>
    {
        public TabControlRegionAdapter(IRegionBehaviorFactory regionBehaviorFactory) : base(regionBehaviorFactory)
        {
        }

        protected override void Adapt(IRegion region, TabControl regionTarget)
        {
            region.ActiveViews.CollectionChanged += (_, e) =>
            {
                switch (e.Action)
                {
                    case NotifyCollectionChangedAction.Add:
                        foreach (var newItem in e.NewItems ?? new ArrayList())
                        {
                            regionTarget.Items.Add(newItem);
                            if (regionTarget.ItemContainerGenerator.ContainerFromItem(newItem) is TabItem tb)
                            {
                                do
                                {
                                    if (newItem is ITabItemHeader tabItemBinding)
                                    {
                                        tb.SetBinding(HeaderedContentControl.HeaderProperty, CreateHeaderBinding(tabItemBinding));
                                        break;
                                    }

                                    if (newItem is FrameworkElement { DataContext: ITabItemHeader tabItemViewModelBinding })
                                    {
                                        tb.SetBinding(HeaderedContentControl.HeaderProperty, CreateHeaderBinding(tabItemViewModelBinding));
                                        break;
                                    }

                                    tb.Header = newItem.GetType().ToString();
                                } while (false);
                            }

                            regionTarget.SelectedIndex = regionTarget.Items.Count - 1;
                        }

                        break;
                    case NotifyCollectionChangedAction.Remove:
                        foreach (var oldItem in e.OldItems ?? new ArrayList())
                        {
                            for (var i = 0; i < regionTarget.Items.Count; i++)
                            {
                                var tab = regionTarget.Items[i];
                                if (tab == oldItem)
                                {
                                    regionTarget.Items.Remove(tab);
                                }
                            }

                            regionTarget.SelectedIndex = regionTarget.Items.Count - 1;
                        }

                        break;
                }
            };
        }

        private Binding CreateHeaderBinding(ITabItemHeader tabItemBinding)
        {
            var binding = new Binding(nameof(tabItemBinding.Header))
            {
                Source = tabItemBinding
            };

            return binding;
        }

        protected override IRegion CreateRegion()
        {
            return new AllActiveRegion();
        }
    }
}
