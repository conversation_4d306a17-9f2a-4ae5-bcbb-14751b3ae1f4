using System.Windows;
using DeviceGuard.Windows.Utility.Mvvm;

namespace DeviceGuard.Windows.Utility.Prism
{
    public class ViewModelBinder
    {
        public static bool? GetBindingView(DependencyObject obj)
        {
            return (bool?) obj.GetValue(BindingViewProperty);
        }

        public static void SetBindingView(DependencyObject obj, bool? value)
        {
            obj.SetValue(BindingViewProperty, value);
        }

        // Using a DependencyProperty as the backing store for BindingViewModel.  This enables animation, styling, binding, etc...
        public static readonly DependencyProperty BindingViewProperty =
            DependencyProperty.RegisterAttached("BindingView", typeof(bool?), typeof(ViewModelBinder), new PropertyMetadata(null, OnBindingViewChanged));

        private static void OnBindingViewChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
        {
            if (d is not FrameworkElement fe)
                return;

            if (e.NewValue is true)
            {
                if (fe.DataContext is ViewModelBase vm)
                    vm.BindingView(fe);

                fe.DataContextChanged -= FrameworkElement_DataContextChanged;
                fe.DataContextChanged += FrameworkElement_DataContextChanged;
            }
            else
            {
                if (fe.DataContext is ViewModelBase vm)
                    vm.UnbindingView(fe);
                fe.DataContextChanged -= FrameworkElement_DataContextChanged;
            }
        }

        private static void FrameworkElement_DataContextChanged(object sender, DependencyPropertyChangedEventArgs e)
        {
            if (sender is not FrameworkElement fe)
                return;

            if (GetBindingView(fe) is not true)
                return;

            if (e.OldValue is ViewModelBase old)
                old.UnbindingView(fe);

            if (e.NewValue is ViewModelBase vm)
                vm.BindingView(fe);
        }
    }
}
