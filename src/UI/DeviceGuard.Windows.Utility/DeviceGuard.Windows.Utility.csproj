<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <LangVersion>latest</LangVersion>
    <PackageId>DeviceGuard.Windows.Utility</PackageId>
    <Nullable>enable</Nullable>
    <Platforms>AnyCPU;x64</Platforms>
    <!-- Suppress CA1416 warnings since this is a Windows-only WPF library -->
    <NoWarn>$(NoWarn);CA1416</NoWarn>
  </PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
		<DebugType>none</DebugType>
		<DebugSymbols>false</DebugSymbols>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
	  <DebugType>none</DebugType>
	  <DebugSymbols>false</DebugSymbols>
	</PropertyGroup>
  <ItemGroup>
    <Compile Remove="Events\**" />
    <Compile Remove="Validator\**" />
    <EmbeddedResource Remove="Events\**" />
    <EmbeddedResource Remove="Validator\**" />
    <None Remove="Events\**" />
    <None Remove="Validator\**" />
    <Page Remove="Events\**" />
    <Page Remove="Validator\**" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Dragablz" Version="0.0.3.223" />
    <PackageReference Include="FluentValidation" Version="10.2.3" />
    <PackageReference Include="Prism.Wpf" Version="9.0.537" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Interfaces\DeviceGuard.Windows.Interface\DeviceGuard.Windows.Interface.csproj" />
    <ProjectReference Include="..\Hotwheels.Wpf\Hotwheels.Wpf.csproj" />
    <ProjectReference Include="..\..\Services\Configuration\Configuration.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Helper\" />
  </ItemGroup>
</Project>