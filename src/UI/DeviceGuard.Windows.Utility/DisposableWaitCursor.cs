using System;
using System.Windows.Input;

namespace DeviceGuard.Windows.Utility
{
    /// <summary>
    /// 实现一个可 disposable 的 cursor
    /// 使用方法: using var currentCursor = DisposableWaitCursor.WaitCursor();
    /// </summary>
    public class DisposableWaitCursor : IDisposable
    {
        public static DisposableWaitCursor WaitCursor()
        {
            return new(Cursors.Wait);
        }

        public DisposableWaitCursor(Cursor disposableCursor)
        {
            Mouse.OverrideCursor = disposableCursor;
        }


        #region IDisposable

        /// <summary>Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.</summary>
        public void Dispose()
        {
            Mouse.OverrideCursor = null;
        }

        #endregion
    }
}
