using System;
using Prism.Events;

namespace DeviceGuard.Windows.Utility.Mvvm
{
    /// <summary>
    /// ViewModelBase 扩展, 主要扩展事件订阅处理逻辑, 支持跟踪事件消息, 直道 ViewModel Destroy
    /// </summary>
    public static class ViewModelBaseExtensions
    {
        /// <summary>
        /// 默认处理线程
        /// </summary>
        private const ThreadOption DefaultThreadOptions = ThreadOption.PublisherThread;

        #region Event with Payload

        /// <summary>
        /// 订阅事件消息, 注意, 为了提高性能 在 IsLoaded == true 后订阅的消息, 在 Unloaded 时将被取消订阅
        /// </summary>
        /// <typeparam name="TPayload"></typeparam>
        /// <param name="source"></param>
        /// <param name="owner"></param>
        /// <param name="action"></param>
        /// <returns></returns>
        public static SubscriptionToken SubscribeTracking<TPayload>(
            this PubSubEvent<TPayload> source,
            ViewModelBase              owner,
            Action<TPayload>           action)
        {
            return SubscribeTrackingCore(source, owner, action, DefaultThreadOptions, null);
        }

        /// <summary>
        /// 订阅事件消息, 注意, 为了提高性能 在 IsLoaded == true 后订阅的消息, 在 Unloaded 时将被取消订阅
        /// </summary>
        /// <typeparam name="TPayload"></typeparam>
        /// <param name="source"></param>
        /// <param name="owner"></param>
        /// <param name="action"></param>
        /// <param name="filter"></param>
        /// <returns></returns>
        public static SubscriptionToken SubscribeTracking<TPayload>(
            this PubSubEvent<TPayload> source,
            ViewModelBase              owner,
            Action<TPayload>           action,
            Predicate<TPayload>        filter)
        {
            return SubscribeTrackingCore(source, owner, action, DefaultThreadOptions, filter);
        }

        /// <summary>
        /// 订阅事件消息, 注意, 为了提高性能 在 IsLoaded == true 后订阅的消息, 在 Unloaded 时将被取消订阅
        /// </summary>
        /// <typeparam name="TPayload"></typeparam>
        /// <param name="source"></param>
        /// <param name="owner"></param>
        /// <param name="action"></param>
        /// <param name="threadOption"></param>
        /// <returns></returns>
        public static SubscriptionToken SubscribeTracking<TPayload>(
            this PubSubEvent<TPayload> source,
            ViewModelBase              owner,
            Action<TPayload>           action,
            ThreadOption               threadOption)
        {
            return SubscribeTrackingCore(source, owner, action, threadOption, null);
        }

        /// <summary>
        /// 订阅事件消息, 注意, 为了提高性能 在 IsLoaded == true 后订阅的消息, 在 Unloaded 时将被取消订阅
        /// </summary>
        /// <typeparam name="TPayload"></typeparam>
        /// <param name="source"></param>
        /// <param name="owner"></param>
        /// <param name="action"></param>
        /// <param name="threadOption"></param>
        /// <param name="filter"></param>
        /// <returns></returns>
        public static SubscriptionToken SubscribeTracking<TPayload>(
            this PubSubEvent<TPayload> source,
            ViewModelBase              owner,
            Action<TPayload>           action,
            ThreadOption               threadOption,
            Predicate<TPayload>        filter)
        {
            return SubscribeTrackingCore(source, owner, action, threadOption, filter);
        }


        /// <summary>
        /// 订阅事件, 并维护句柄
        /// </summary>
        /// <typeparam name="TPayload"></typeparam>
        /// <param name="source"></param>
        /// <param name="owner"></param>
        /// <param name="action"></param>
        /// <param name="threadOption"></param>
        /// <param name="filter"></param>
        /// <returns></returns>
        private static SubscriptionToken SubscribeTrackingCore<TPayload>(
            PubSubEvent<TPayload> source,
            ViewModelBase         owner,
            Action<TPayload>      action,
            ThreadOption          threadOption,
            Predicate<TPayload>?  filter)
        {
            // note:
            // 1. 这里不使用强引用的方式保持事件 action 和 filter, 否则可能导致 Unsubscribe 调用不及时, 引起内存泄露
            // 2. ViewModelBase 对象 (owner) 维持 action 和 filter 的引用, 直到 owner 被回收
            var token = source.Subscribe(action, threadOption, false, filter);
            return owner.AddSubscribedToken(filter == null ? new EventSubscriptionHolder(source, token, action) : new EventSubscriptionHolder(source, token, action, filter));
        }

        #endregion

        #region Event without payload

        /// <summary>
        /// 订阅事件消息, 注意, 为了提高性能 在 IsLoaded == true 后订阅的消息, 在 Unloaded 时将被取消订阅
        /// </summary>
        /// <param name="source"></param>
        /// <param name="owner"></param>
        /// <param name="action"></param>
        /// <returns></returns>
        public static SubscriptionToken SubscribeTracking(
            this PubSubEvent source,
            ViewModelBase    owner,
            Action           action)
        {
            return SubscribeTrackingCore(source, owner, action, DefaultThreadOptions);
        }

        /// <summary>
        /// 订阅事件消息, 注意, 为了提高性能 在 IsLoaded == true 后订阅的消息, 在 Unloaded 时将被取消订阅
        /// </summary>
        /// <param name="source"></param>
        /// <param name="owner"></param>
        /// <param name="action"></param>
        /// <param name="threadOption"></param>
        /// <returns></returns>
        public static SubscriptionToken SubscribeTracking(
            this PubSubEvent source,
            ViewModelBase    owner,
            Action           action,
            ThreadOption     threadOption)
        {
            return SubscribeTrackingCore(source, owner, action, threadOption);
        }

        /// <summary>
        /// 订阅事件, 并维护句柄
        /// </summary>
        /// <param name="source"></param>
        /// <param name="owner"></param>
        /// <param name="action"></param>
        /// <param name="threadOption"></param>
        /// <returns></returns>
        private static SubscriptionToken SubscribeTrackingCore(
            PubSubEvent   source,
            ViewModelBase owner,
            Action        action,
            ThreadOption  threadOption)
        {
            if (source == null)
                throw new ArgumentNullException(nameof(source));
            if (owner == null)
                throw new ArgumentNullException(nameof(owner));
            if (action == null)
                throw new ArgumentNullException(nameof(action));

            // 注意: 不要使用强引用
            var token = source.Subscribe(action, threadOption, false);
            return owner.AddSubscribedToken(new EventSubscriptionHolder(source, token, action));
        }

        #endregion
    }
}
