using System;
using System.Collections;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using FluentValidation.Results;
using Prism.Navigation.Regions;

namespace DeviceGuard.Windows.Utility.Mvvm
{
    public class RegionViewModelBase : ViewModelBase, IConfirmNavigationRequest, IRegionMemberLifetime, IDataErrorInfo, INotifyDataErrorInfo
    {
        #region Implementation of IConfirmNavigationRequest

        async void IConfirmNavigationRequest.ConfirmNavigationRequest(NavigationContext navigationContext, Action<bool> continuationCallback)
        {
            try
            {
                await ConfirmNavigationRequestAsync(navigationContext, continuationCallback, CancellationToken.None);
            }
            catch (Exception e)
            {
                // TODO 处理
                Console.WriteLine(e);
            }
        }

        public virtual bool IsNavigationTarget(NavigationContext navigationContext)
        {
            return true;
        }

        async void IRegionAware.OnNavigatedFrom(NavigationContext navigationContext)
        {
            try
            {
                await OnNavigatedFromAsync(navigationContext, CancellationToken.None);
            }
            catch (Exception)
            {
                // ignored
            }
        }

        async void IRegionAware.OnNavigatedTo(NavigationContext navigationContext)
        {
            try
            {
                await OnNavigatedToAsync(navigationContext, CancellationToken.None);
            }
            catch (Exception)
            {
                // ignored
            }
        }

        public virtual Task ConfirmNavigationRequestAsync(NavigationContext navigationContext, Action<bool> continuationCallback, CancellationToken cancellation)
        {
            continuationCallback(true);
            return Task.CompletedTask;
        }

        public virtual Task OnNavigatedFromAsync(NavigationContext navigationContext, CancellationToken cancellation)
        {
            return Task.CompletedTask;
        }

        public virtual Task OnNavigatedToAsync(NavigationContext navigationContext, CancellationToken cancellation)
        {
            return Task.CompletedTask;
        }

        #endregion

        #region Implementation of IRegionMemberLifetime

        /// <summary>
        /// Gets a value indicating whether this instance should be kept-alive upon deactivation.
        /// </summary>
        public virtual bool KeepAlive => true;

        #endregion

        #region Implementation of IDataErrorInfo

        /// <summary>
        /// 数据验证器
        /// </summary>
        protected virtual ValidationResult? GetValidateResult()
        {
            return null;
        }

        protected override void OnPropertyChanged(PropertyChangedEventArgs args)
        {
            base.OnPropertyChanged(args);

            if (args.PropertyName != nameof(Error))
            {
                Validate();
            }
        }

        protected void Validate()
        {
            // 执行验证
            if (GetValidateResult() is { } result)
            {
                var handled = new HashSet<string>();
                var notices = new List<string>();

                // 使用本次验证更新错误信息
                foreach (var item in result.Errors)
                {
                    if (UpdateErrorMessage(item.PropertyName, item.ErrorMessage))
                        notices.Add(item.PropertyName);

                    handled.Add(item.PropertyName);
                }

                // 删除本次验证不存在的值
                foreach (var propertyName in _errors.Keys.ToArray())
                {
                    if (handled.Contains(propertyName))
                        continue;

                    if (UpdateErrorMessage(propertyName, null))
                        notices.Add(propertyName);
                }

                // 发送通知
                if (notices.Any())
                {
                    foreach (var propertyName in notices)
                    {
                        OnErrorsChanged(new DataErrorsChangedEventArgs(propertyName));
                    }

                    RaisePropertyChanged(nameof(Error));
                    RaisePropertyChanged(nameof(HasErrors));

                    OnErrorChanged();
                }
            }
        }

        /// <summary>
        /// 更新错误信息
        /// </summary>
        /// <param name="propertyName"></param>
        /// <param name="errorMessage"></param>
        /// <returns></returns>
        private bool UpdateErrorMessage(string propertyName, string? errorMessage)
        {
            if (!_errors.TryGetValue(propertyName, out var origin) && string.IsNullOrEmpty(errorMessage))
                return false;

            if (origin == errorMessage)
                return false;

            if (string.IsNullOrEmpty(errorMessage))
                _errors.Remove(propertyName);
            else
                _errors[propertyName] = errorMessage;

            return true;
        }

        private readonly Dictionary<string, string> _errors = new();

        /// <summary>
        /// 错误属性更新激发
        /// </summary>
        protected virtual void OnErrorChanged()
        {
        }

        /// <summary>Gets an error message indicating what is wrong with this object.</summary>
        /// <returns>An error message indicating what is wrong with this object. The default is an empty string ("").</returns>
        public string Error => string.Join("\n", _errors.Values);


        /// <summary>Gets the error message for the property with the given name.</summary>
        /// <param name="columnName">The name of the property whose error message to get.</param>
        /// <returns>The error message for the property. The default is an empty string ("").</returns>
        public string this[string columnName] => _errors.GetValueOrDefault(columnName) ?? string.Empty;

        public bool HasErrorIn(params string[] propertyNames)
        {
            foreach (var item in propertyNames)
            {
                if (_errors.ContainsKey(item))
                    return true;
            }

            return false;
        }

        #endregion

        #region Implementation of INotifyDataErrorInfo

        /// <summary>Gets the validation errors for a specified property or for the entire entity.</summary>
        /// <param name="propertyName">The name of the property to retrieve validation errors for; or <see langword="null" /> or <see cref="F:System.String.Empty" />, to retrieve entity-level errors.</param>
        /// <returns>The validation errors for the property or entity.</returns>
        public IEnumerable GetErrors(string? propertyName)
        {
            if (propertyName is null)
                return Array.Empty<string>();

            var error = this[propertyName];

            if (string.IsNullOrEmpty(error))
                return Array.Empty<string>();

            return new[] { this[propertyName] };
        }

        /// <summary>Gets a value that indicates whether the entity has validation errors.</summary>
        /// <returns>
        /// <see langword="true" /> if the entity currently has validation errors; otherwise, <see langword="false" />.</returns>
        public bool HasErrors => _errors.Any();

        /// <summary>Occurs when the validation errors have changed for a property or for the entire entity.</summary>
        public event EventHandler<DataErrorsChangedEventArgs>? ErrorsChanged;

        protected virtual void OnErrorsChanged(DataErrorsChangedEventArgs e)
        {
            ErrorsChanged?.Invoke(this, e);
        }

        #endregion
    }
}
