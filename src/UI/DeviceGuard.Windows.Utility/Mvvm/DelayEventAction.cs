using System;
using System.Diagnostics;

namespace DeviceGuard.Windows.Utility.Mvvm
{
    /// <summary>
    /// 延迟消息递送
    /// </summary>
    class DelayEventAction
    {
        private readonly Action    _action;
        private          Stopwatch _stopwatch = new();

        public DelayEventAction(Action action, int delay)
        {
            if (action == null)
                throw new ArgumentNullException(nameof(action));

            _action = action;
        }

        public void Action()
        {
            _action();
        }
    }
}