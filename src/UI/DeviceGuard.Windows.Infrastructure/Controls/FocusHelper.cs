using System;
using System.Collections.Generic;
using System.Windows;
using System.Windows.Controls;

namespace DeviceGuard.Windows.Infrastructure.Controls;

public static class FocusHelper
{
    static FocusHelper()
    {
        _boundElements = new List<string>();
    }

    public static readonly DependencyProperty IsFocusedProperty =
        DependencyProperty.RegisterAttached("IsFocused", typeof(bool?),
            typeof(FocusHelper), new FrameworkPropertyMetadata(false, IsFocusedChanged));

    private static readonly List<string> _boundElements;

    public static bool? GetIsFocused(DependencyObject element)
    {
        if (element == null)
            throw new ArgumentNullException(nameof(element));

        return (bool?) element.GetValue(IsFocusedProperty);
    }

    public static void SetIsFocused(DependencyObject element, bool? value)
    {
        if (element == null)
            throw new ArgumentNullException(nameof(element));

        element.SetValue(IsFocusedProperty, value);
    }

    private static void IsFocusedChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        var fe = (FrameworkElement) d;

        if (_boundElements.Contains(fe.Name) == false)
        {
            _boundElements.Add(fe.Name);
            fe.LostFocus += OnLostFocus;
            fe.GotFocus  += OnGotFocus;
        }


        if (!fe.IsVisible)
        {
            fe.IsVisibleChanged += fe_IsVisibleChanged;
        }

        if ((bool) e.NewValue)
        {
            fe.Focus();
        }
    }

    private static void fe_IsVisibleChanged(object sender, DependencyPropertyChangedEventArgs e)
    {
        var fe = (FrameworkElement) sender;

        if (fe.IsVisible && (bool) ((FrameworkElement) sender).GetValue(IsFocusedProperty))
        {
            fe.IsVisibleChanged -= fe_IsVisibleChanged;
            fe.Focus();
        }
    }

    private static void OnLostFocus(object sender, RoutedEventArgs e)
    {
        var s = sender as Control;
        s?.SetValue(IsFocusedProperty, false);
    }

    private static void OnGotFocus(object sender, RoutedEventArgs e)
    {
        var s = sender as Control;
        s?.SetValue(IsFocusedProperty, true);
    }
}