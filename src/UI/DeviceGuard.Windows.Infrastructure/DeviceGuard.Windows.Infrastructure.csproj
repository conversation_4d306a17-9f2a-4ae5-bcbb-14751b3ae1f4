<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net6.0-windows</TargetFramework>
        <UseWPF>true</UseWPF>
        <UseWindowsForms>true</UseWindowsForms>
        <RootNamespace>DeviceGuard.Windows.Infrastructure</RootNamespace>
        <LangVersion>latest</LangVersion>
        <Platforms>AnyCPU;x64</Platforms>
        <!-- Suppress CA1416 warnings since this is a Windows-only WPF library -->
        <NoWarn>$(NoWarn);CA1416</NoWarn>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
        <DebugType>none</DebugType>
        <DebugSymbols>false</DebugSymbols>
    </PropertyGroup>
    <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
      <DebugType>none</DebugType>
      <DebugSymbols>false</DebugSymbols>
    </PropertyGroup>
    <ItemGroup>
        <ProjectReference Include="..\..\Interfaces\DeviceGuard.Interface\DeviceGuard.Interface.csproj" />
        <ProjectReference Include="..\DeviceGuard.Styles\DeviceGuard.Styles.csproj" />
        <ProjectReference Include="..\DeviceGuard.Windows.Controls\DeviceGuard.Windows.Controls.csproj" />
        <ProjectReference Include="..\DeviceGuard.Windows.Utility\DeviceGuard.Windows.Utility.csproj" />
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="FluentValidation" Version="11.11.0" />
        <PackageReference Include="MaterialDesignThemes" Version="5.2.1" />
        <PackageReference Include="PropertyChanged.Fody" Version="4.1.0">
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
    </ItemGroup>

    <ItemGroup>
        <Compile Update="MessageBox\UserDialogImpl\I18n.Designer.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>I18n.resx</DependentUpon>
        </Compile>
        <Compile Update="MessageBox\UserDialogImpl\MessageBoxDialogView.xaml.cs">
            <SubType>Code</SubType>
        </Compile>
    </ItemGroup>

    <ItemGroup>
        <EmbeddedResource Update="MessageBox\UserDialogImpl\I18n.resx">
            <Generator>ResXFileCodeGenerator</Generator>
            <LastGenOutput>I18n.Designer.cs</LastGenOutput>
        </EmbeddedResource>
    </ItemGroup>

    <ItemGroup>
        <Page Update="Blank\BlankView.xaml">
            <XamlRuntime>$(DefaultXamlRuntime)</XamlRuntime>
            <SubType>Designer</SubType>
        </Page>
        <Page Update="MessageBox\UserDialogImpl\MessageBoxDialogView.xaml">
            <XamlRuntime>$(DefaultXamlRuntime)</XamlRuntime>
            <SubType>Designer</SubType>
        </Page>
    </ItemGroup>

</Project>
