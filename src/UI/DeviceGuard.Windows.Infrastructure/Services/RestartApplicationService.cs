using System;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Windows;
using DeviceGuard.Windows.Interface.Services;
using DeviceGuard.Windows.Utility.Dialogs;

namespace DeviceGuard.Windows.Infrastructure.Services
{
    /// <summary>
    /// 重启动软件服务
    /// </summary>
    public class ApplicationRestartService : IAppRestartService
    {
        private readonly IMessageBox _messageBox;

        public ApplicationRestartService(IMessageBox messageBox)
        {
            _messageBox = messageBox;
        }

        /// <summary>
        /// 重启动软件
        /// </summary>
        public void Restart()
        {
            // 获取当前进程的可执行文件路径
            var fileName = Process.GetCurrentProcess().MainModule?.FileName ?? throw new Exception("Can not retrieve exe file");

            // 启动新进程并传递启动参数
            var startInfo = new ProcessStartInfo(fileName)
            {
                Arguments       = "--duplicate", // 传递启动参数
                UseShellExecute = true,          // 使用外壳执行以便启动新的进程
                CreateNoWindow  = true           // 不创建命令行窗口
            };
            Process.Start(startInfo); // 启动新的进程

            Application.Current.Shutdown();
        }

        /// <summary>
        /// 询问后，重启动软件
        /// </summary>
        /// <param name="question">重启动软件的提示信息,传递 null 则使用默认信息</param>
        public async Task AskUserRestartAsync(string question = null)
        {
            if (await _messageBox.ShowQuestionAsync(question ?? "Are you sure to restart the application?") == true)
            {
                Restart();
            }
        }
    }
}
