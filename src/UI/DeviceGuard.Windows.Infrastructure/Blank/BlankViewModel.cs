using System.Threading;
using System.Threading.Tasks;
using DeviceGuard.Windows.Utility.Mvvm;
using Prism.Navigation.Regions;

namespace DeviceGuard.Windows.Infrastructure.Blank;

/// <summary>
/// ViewModel for EmptyChannelView
/// </summary>
public class BlankViewModel : RegionViewModelBase
{
    #region Navigation

    public override Task OnNavigatedToAsync(NavigationContext navigationContext, CancellationToken cancellation)
    {
        if (navigationContext.Parameters.TryGetValue("Message", out string message))
            Message = message;

        return base.OnNavigatedToAsync(navigationContext, cancellation);
    }

    #endregion

    public string Message { get; set; }
}
