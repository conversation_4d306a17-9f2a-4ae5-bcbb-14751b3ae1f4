#nullable enable
using DeviceGuard.Windows.Utility.ViewLoader;
using Prism.Navigation;
using Prism.Navigation.Regions;

namespace DeviceGuard.Windows.Infrastructure.Blank;

/// <summary>
/// 在指定区域显示一个空白页, 或者显示一个含有提示信息的空白页
/// </summary>
public class BlankViewLoader : IBlankViewLoader
{
    private readonly IRegionManager _regionManager;

    public BlankViewLoader(IRegionManager regionManager)
    {
        _regionManager = regionManager;
    }

    public static string BlankViewNavigationName => "65642E17-A8FB-4EC7-A90F-3F0C4445E9B9";

    /// <summary>
    /// 显示视图
    /// </summary>
    /// <param name="regionName"></param>
    /// <param name="message"></param>
    public void Show(string regionName, string message)
    {
        var p = new NavigationParameters()
        {
            { "Message", message }
        };
        _regionManager.RequestNavigate(regionName, BlankViewNavigationName, p);
    }
}
