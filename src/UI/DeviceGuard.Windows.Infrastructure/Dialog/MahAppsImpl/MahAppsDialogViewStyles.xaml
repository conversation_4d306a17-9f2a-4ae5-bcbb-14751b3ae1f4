<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:mah="http://metro.mahapps.com/winfx/xaml/controls"
                    xmlns:mahAppsImpl="clr-namespace:DeviceGuard.Windows.Infrastructure.Dialog.MahAppsImpl">
    <Storyboard x:Key="MahAppsDialogViewShow">
        <DoubleAnimation AccelerationRatio=".9"
                         BeginTime="0:0:0"
                         Storyboard.TargetProperty="Opacity"
                         To="1"
                         Duration="0:0:0.1" />
    </Storyboard>

    <Storyboard x:Key="MahAppsDialogViewClose">
        <DoubleAnimation AccelerationRatio=".9"
                         BeginTime="0:0:0"
                         Storyboard.TargetProperty="Opacity"
                         To="0"
                         Duration="0:0:2.1" />
    </Storyboard>

    <ControlTemplate x:Key="MahAppsDialogViewTemplate" TargetType="{x:Type mah:BaseMetroDialog}">
        <Grid Background="{TemplateBinding Background}">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>
            <ContentPresenter x:Name="PART_Top"
                              Grid.Row="0"
                              AutomationProperties.Name="Dialog top"
                              Content="{TemplateBinding DialogTop}"
                              SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
            <Grid Grid.Row="1">
                <TextBlock x:Name="PART_Title" Visibility="Collapsed" />
                <!--  Content area  -->
                <Grid Margin="{TemplateBinding Padding}">
                    <ContentPresenter x:Name="PART_Content"
                                      HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                      VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                      AutomationProperties.Name="Dialog content"
                                      Content="{TemplateBinding Content}"
                                      ContentStringFormat="{TemplateBinding ContentStringFormat}"
                                      ContentTemplate="{TemplateBinding ContentTemplate}"
                                      ContentTemplateSelector="{TemplateBinding ContentTemplateSelector}"
                                      SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                </Grid>
            </Grid>
            <ContentPresenter x:Name="PART_Bottom"
                              Grid.Row="2"
                              AutomationProperties.Name="Dialog bottom"
                              Content="{TemplateBinding DialogBottom}"
                              SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
        </Grid>
        <ControlTemplate.Triggers>
            <EventTrigger RoutedEvent="Loaded">
                <EventTrigger.Actions>
                    <BeginStoryboard Storyboard="{StaticResource MahAppsDialogViewShow}" />
                </EventTrigger.Actions>
            </EventTrigger>
            <EventTrigger RoutedEvent="Unloaded">
                <EventTrigger.Actions>
                    <BeginStoryboard Storyboard="{StaticResource MahAppsDialogViewClose}" />
                </EventTrigger.Actions>
            </EventTrigger>
        </ControlTemplate.Triggers>

    </ControlTemplate>

    <Style TargetType="{x:Type mahAppsImpl:MahAppsDialogView}">
        <Setter Property="Background" Value="{DynamicResource MahApps.Brushes.ThemeBackground}" />
        <Setter Property="DialogButtonFontSize" Value="14" />
        <Setter Property="DialogMessageFontSize" Value="14" />
        <Setter Property="DialogTitleFontSize" Value="16" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="Foreground" Value="{DynamicResource MahApps.Brushes.ThemeForeground}" />
        <Setter Property="HorizontalAlignment" Value="Stretch" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="KeyboardNavigation.ControlTabNavigation" Value="Cycle" />
        <Setter Property="KeyboardNavigation.DirectionalNavigation" Value="Cycle" />
        <Setter Property="KeyboardNavigation.TabNavigation" Value="Cycle" />
        <Setter Property="Opacity" Value="0" />
        <Setter Property="Padding" Value="0 10 0 0" />
        <Setter Property="Template" Value="{StaticResource MahAppsDialogViewTemplate}" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Stretch" />
    </Style>

</ResourceDictionary>