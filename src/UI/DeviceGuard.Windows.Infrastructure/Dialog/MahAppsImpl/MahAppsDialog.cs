using System;
using System.Threading.Tasks;
using System.Windows;
using DeviceGuard.Windows.Utility.Dialogs;
using MahApps.Metro.Controls;
using MahApps.Metro.Controls.Dialogs;

namespace DeviceGuard.Windows.Infrastructure.Dialog.MahAppsImpl;

/// <summary>
/// MahApps实现的对话框的框架, 负责对话框基本的外框, 以及关闭操作
/// </summary>
public class MahAppsDialog : IDialog
{
    /// <summary>
    /// 显示对话框
    /// </summary>
    /// <param name="dialogContent"></param>
    /// <returns></returns>
    public async Task<bool?> ShowDialogAsync(IDialogView dialogContent)
    {
        var settings = new MetroDialogSettings
        {
            AnimateHide = false,
            AnimateShow = false,
        };

        if (Application.Current.MainWindow is not MetroWindow { IsVisible: true } owner)
            throw new Exception("Main window is not ready");

        var dialog = new MahAppsDialogView(owner, settings)
        {
            Content = dialogContent,
        };
        dialogContent.DialogController = new MahAppsDialogViewController(owner, dialog);

        // show the dialog
        await owner.ShowMetroDialogAsync(dialog, settings);
        await dialog.WaitUntilUnloadedAsync();

        return dialogContent.DialogResult;
    }
}