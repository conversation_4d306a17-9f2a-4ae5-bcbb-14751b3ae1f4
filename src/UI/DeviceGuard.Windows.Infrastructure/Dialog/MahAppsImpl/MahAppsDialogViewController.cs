using System;
using System.Threading.Tasks;
using DeviceGuard.Windows.Utility.Dialogs;
using MahApps.Metro.Controls;
using MahApps.Metro.Controls.Dialogs;

namespace DeviceGuard.Windows.Infrastructure.Dialog.MahAppsImpl;

/// <summary>
/// MahApps窗口控制的实现
/// </summary>
public class MahAppsDialogViewController : IDialogViewController
{
    private readonly MetroWindow     _metroWindow;
    private readonly BaseMetroDialog _dialog;

    public MahAppsDialogViewController(MetroWindow metroWindow, BaseMetroDialog dialog)
    {
        _metroWindow = metroWindow ?? throw new ArgumentNullException(nameof(metroWindow));
        _dialog      = dialog;
    }

    /// <summary>
    /// 关闭窗口
    /// </summary>
    /// <returns></returns>
    public async Task CloseAsync()
    {
        await _metroWindow.HideMetroDialogAsync(_dialog);
    }
}