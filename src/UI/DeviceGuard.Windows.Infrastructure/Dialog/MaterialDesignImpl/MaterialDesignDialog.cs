using System;
using System.ComponentModel;
using System.Threading.Tasks;
using System.Windows;
using DeviceGuard.Windows.Utility.Dialogs;
using MaterialDesignThemes.Wpf;

namespace DeviceGuard.Windows.Infrastructure.Dialog.MaterialDesignImpl;

public class MaterialDesignDialog : IDialog
{
    public static readonly string MessageBoxDialogHost = "MessageBoxDialogHost";


    /// <summary>
    /// 显示对话框
    /// </summary>
    /// <param name="dialog"></param>
    /// <returns></returns>
    public async Task<bool?> ShowDialogAsync(IDialogView dialog)
    {
        var window = Application.Current.MainWindow;
        if (!(window?.FindName(MessageBoxDialogHost) is DialogHost dialogHost))
            throw new NotSupportedException("Cannot find dialog host in main window");

        if (dialogHost.IsOpen)
            dialogHost.IsOpen = false;

        if (dialogHost == null)
            throw new ArgumentNullException(nameof(dialogHost));

        if (dialogHost.IsOpen)
            dialogHost.IsOpen = false;

        await dialogHost.ShowDialog(dialog,
            (_, e) => { dialog.DialogController = new MaterialDesignDialogViewController(e); },
            (_, e) =>
            {
                var closeEvent = new CancelEventArgs(false);
                dialog.OnClosing(closeEvent);

                if (closeEvent.Cancel)
                    e.Cancel();
            });

        return dialog.DialogResult;
    }
}