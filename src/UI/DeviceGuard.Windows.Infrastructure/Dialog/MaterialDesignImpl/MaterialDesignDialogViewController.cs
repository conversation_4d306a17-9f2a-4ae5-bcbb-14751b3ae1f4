using System.Threading.Tasks;
using DeviceGuard.Windows.Utility.Dialogs;
using MaterialDesignThemes.Wpf;

namespace DeviceGuard.Windows.Infrastructure.Dialog.MaterialDesignImpl;

/// <summary>
/// Material design 窗口控制的实现
/// </summary>
public class MaterialDesignDialogViewController : IDialogViewController
{
    private readonly DialogOpenedEventArgs _dialogOpenedEventArgs;

    public MaterialDesignDialogViewController(DialogOpenedEventArgs dialogOpenedEventArgs)
    {
        _dialogOpenedEventArgs = dialogOpenedEventArgs;
    }

    /// <summary>
    /// 关闭窗口
    /// </summary>
    /// <returns></returns>
    public Task CloseAsync()
    {
        _dialogOpenedEventArgs.Session.Close();
        return Task.CompletedTask;
    }
}