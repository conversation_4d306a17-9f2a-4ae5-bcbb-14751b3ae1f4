using System;
using DeviceGuard.Interface.Prism;
using DeviceGuard.Windows.Infrastructure.Blank;
using DeviceGuard.Windows.Infrastructure.Dialog.MahAppsImpl;
using DeviceGuard.Windows.Infrastructure.FolderDialog;
using DeviceGuard.Windows.Infrastructure.MessageBox.UserDialogImpl;
using DeviceGuard.Windows.Infrastructure.Services;
using DeviceGuard.Windows.Interface.Services;
using DeviceGuard.Windows.Utility.Dialogs;
using DeviceGuard.Windows.Utility.ViewLoader;
using Prism.Ioc;

namespace DeviceGuard.Windows.Infrastructure.DependencyInjection;

public class WindowsInfrastructureComponent : IPrismComponent
{
    public void RegisterTypes(IContainerRegistry containerRegistry)
    {
        if (containerRegistry == null)
            throw new ArgumentNullException(nameof(containerRegistry));

        containerRegistry.Register<IMessageBox, UserDialogMessageBox>();
        //containerRegistry.Register<IMessageBox, WindowsMessageBox>();

        //containerRegistry.Register<IDialog, MaterialDesignDialog>();
        containerRegistry.Register<IDialog, MahAppsDialog>();

        containerRegistry.Register<IFolderDialog, WindowFolderDialog>();

        // blank view loader
        containerRegistry.RegisterForNavigation<BlankView>(BlankViewLoader.BlankViewNavigationName);
        containerRegistry.Register<IBlankViewLoader, BlankViewLoader>();

        // restart
        containerRegistry.RegisterSingleton<IAppRestartService, ApplicationRestartService>();
    }
}
