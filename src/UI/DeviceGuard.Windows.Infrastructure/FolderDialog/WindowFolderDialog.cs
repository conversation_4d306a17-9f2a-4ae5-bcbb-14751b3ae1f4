using System.Windows.Forms;
using DeviceGuard.Windows.Utility.Dialogs;

namespace DeviceGuard.Windows.Infrastructure.FolderDialog;

public class WindowFolderDialog : IFolderDialog
{
    /// <summary>
    /// 显示打开文件对话框
    /// </summary>
    /// <param name="folder">初始文件夹</param>
    /// <returns>返回用户选择的文件夹,如果用户取消选择,返回null</returns>
    public string ShowDialog(string folder)
    {
        var folderDialog = new FolderBrowserDialog
        {
            SelectedPath = folder
        };

        var result = folderDialog.ShowDialog();
        if (result == DialogResult.OK)
            return folderDialog.SelectedPath;

        return null;
    }
}