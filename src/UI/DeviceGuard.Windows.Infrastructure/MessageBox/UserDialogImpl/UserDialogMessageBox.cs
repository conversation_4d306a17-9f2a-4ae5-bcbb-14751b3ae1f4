using System.Threading.Tasks;
using System.Windows;
using DeviceGuard.Windows.Utility.Dialogs;

namespace DeviceGuard.Windows.Infrastructure.MessageBox.UserDialogImpl;

/// <summary>
/// 基于 UserDialog 的消息框
/// </summary>
public class UserDialogMessageBox : IMessageBox
{
    private readonly IDialog _dialog;

    public UserDialogMessageBox(IDialog dialog)
    {
        _dialog = dialog;
    }

    /// <summary>
    /// 显示消息对话框
    /// </summary>
    /// <returns></returns>
    public async Task<MessageBoxResult> ShowAsync(string text, MessageBoxSetting setting = null)
    {
        setting ??= new MessageBoxSetting
        {
            Title  = "提示",
            Button = MessageBoxButton.OK,
            Icon   = MessageBoxImage.None,
        };

        var dialog = new MessageBoxDialogView();
        dialog.ViewModel.Setting = setting;
        dialog.ViewModel.Text    = text;

        await _dialog.ShowDialogAsync(dialog);

        return dialog.ViewModel.Result;
    }
}