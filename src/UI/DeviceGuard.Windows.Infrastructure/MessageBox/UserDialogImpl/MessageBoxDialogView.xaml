<dialog:UserDialog x:Class="DeviceGuard.Windows.Infrastructure.MessageBox.UserDialogImpl.MessageBoxDialogView"
                   xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                   xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                   xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                   xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                   xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
                   xmlns:mvvm="http://prismlibrary.com/"
                   xmlns:conv="clr-namespace:DeviceGuard.Windows.Controls.Conv;assembly=DeviceGuard.Windows.Controls"
                   xmlns:dialog="clr-namespace:DeviceGuard.Windows.Controls.Dialog;assembly=DeviceGuard.Windows.Controls"
                   xmlns:styles="clr-namespace:DeviceGuard.Styles;assembly=DeviceGuard.Styles"
                   xmlns:userDialogImpl="clr-namespace:DeviceGuard.Windows.Infrastructure.MessageBox.UserDialogImpl"
                   xmlns:prism="clr-namespace:DeviceGuard.Windows.Utility.Prism;assembly=DeviceGuard.Windows.Utility"
                   mc:Ignorable="d"
                   d:DataContext="{d:DesignInstance Type=userDialogImpl:MessageBoxDialogViewModel}"
                   mvvm:ViewModelLocator.AutoWireViewModel="True"
                   prism:ViewModelBinder.BindingView="True"
                   DialogResult="{Binding DialogResult}"
                   IsTitlebarVisible="False"
                   MinWidth="450"
                   MaxWidth="700"
                   MaxHeight="300">
    <dialog:UserDialog.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!--Design time style resources-->
                <styles:DesignTimeResourceDictionary Source="pack://application:,,,/DeviceGuard.Styles;component/AppStyle.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </dialog:UserDialog.Resources>
    <dialog:UserDialog.InputBindings>
        <KeyBinding Key="Enter"
                    Command="{Binding EnterCommand}" />
        <KeyBinding Key="Escape"
                    Command="{Binding EscapeCommand}" />
    </dialog:UserDialog.InputBindings>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <Label Name="TitleLabel"
               Content="{Binding TitleOrDefault,FallbackValue=温馨提示}"
               FontSize="20"
               Padding="0 10"
               HorizontalContentAlignment="Center"
               HorizontalAlignment="Stretch">
            <Label.Background>
                <Binding Path="Setting.Icon"
                         FallbackValue="#B0C4DE">
                    <Binding.Converter>
                        <conv:SwitchConverter>
                            <conv:SwitchCase When="Information"
                                             Then="#B0C4DE" />
                            <conv:SwitchCase When="Warning"
                                             Then="#F9A670" />
                            <conv:SwitchCase When="Error"
                                             Then="#FA8458" />
                            <conv:SwitchCase When="Question"
                                             Then="#B0C4DE" />
                        </conv:SwitchConverter>
                    </Binding.Converter>
                </Binding>
            </Label.Background>
        </Label>
        <Grid Grid.Row="1"
              Margin="20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>
            <materialDesign:PackIcon x:Name="TypeIcon"
                                     Width="40"
                                     Height="40">
                <materialDesign:PackIcon.Foreground>
                    <Binding Path="Setting.Icon"
                             FallbackValue="#555">
                        <Binding.Converter>
                            <conv:SwitchConverter>
                                <conv:SwitchCase When="Information"
                                                 Then="#818FA1" />
                                <conv:SwitchCase When="Warning"
                                                 Then="#D89110" />
                                <conv:SwitchCase When="Error"
                                                 Then="OrangeRed" />
                                <conv:SwitchCase When="Question"
                                                 Then="#818FA1" />
                            </conv:SwitchConverter>
                        </Binding.Converter>
                    </Binding>
                </materialDesign:PackIcon.Foreground>
                <materialDesign:PackIcon.Kind>
                    <Binding Path="Setting.Icon">
                        <Binding.Converter>
                            <conv:SwitchConverter Else="Help">
                                <conv:SwitchCase When="Information"
                                                 Then="InformationOutline" />
                                <conv:SwitchCase When="Warning"
                                                 Then="AlertCircleOutline" />
                                <conv:SwitchCase When="Error"
                                                 Then="CloseOctagon" />
                                <conv:SwitchCase When="Question"
                                                 Then="HelpCircleOutline" />
                            </conv:SwitchConverter>
                        </Binding.Converter>
                    </Binding>
                </materialDesign:PackIcon.Kind>
            </materialDesign:PackIcon>
            <TextBox Grid.Column="1"
                     Name="PromptTextBox"
                     Margin="10 8 0 0"
                     Text="{Binding Text,FallbackValue=温馨提示}"
                     BorderThickness="0"
                     VerticalScrollBarVisibility="Auto"
                     TextWrapping="WrapWithOverflow"
                     IsReadOnly="True"
                     IsTabStop="False"
                     FontSize="15">
                <TextBox.Style>
                    <Style TargetType="TextBox" />
                </TextBox.Style>
            </TextBox>
        </Grid>
        <ItemsControl Grid.Row="2"
                      Margin="20 10 20 20"
                      HorizontalAlignment="Right"
                      ItemsSource="{Binding Buttons}">
            <ItemsControl.Resources>
                <userDialogImpl:StyleConverter x:Key="ButtonStyleConverter"
                                               True="{StaticResource MaterialDesignFlatMidBgButton}"
                                               False="{StaticResource MaterialDesignOutlinedButton}" />
            </ItemsControl.Resources>
            <ItemsControl.ItemsPanel>
                <ItemsPanelTemplate>
                    <StackPanel Orientation="Horizontal"
                                IsItemsHost="True" />
                </ItemsPanelTemplate>
            </ItemsControl.ItemsPanel>
            <ItemsControl.ItemTemplate>
                <DataTemplate DataType="userDialogImpl:ButtonBehavior">
                    <Button HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Height="32"
                            Padding="35 0"
                            Margin="15 0 0 0"
                            Content="{Binding Text}"
                            FocusManager.IsFocusScope="{Binding IsDefault}"
                            IsDefault="{Binding IsDefault}"
                            IsCancel="{Binding IsCancel}"
                            Style="{Binding IsDefault,Converter={StaticResource ButtonStyleConverter}}"
                            Command="{Binding Path=DataContext.SelectCommand, RelativeSource={RelativeSource AncestorType={x:Type ItemsControl}}}"
                            CommandParameter="{Binding Result}" />
                </DataTemplate>
            </ItemsControl.ItemTemplate>
        </ItemsControl>
    </Grid>
</dialog:UserDialog>