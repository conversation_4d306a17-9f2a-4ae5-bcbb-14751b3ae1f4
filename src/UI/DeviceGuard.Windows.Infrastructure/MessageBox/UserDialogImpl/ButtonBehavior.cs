using System.Windows;

namespace DeviceGuard.Windows.Infrastructure.MessageBox.UserDialogImpl;

public class ButtonBehavior
{
    public ButtonBehavior(string v1, MessageBoxResult oK, bool v2, bool v3)
    {
        Text      = v1;
        Result    = oK;
        IsDefault = v2;
        IsCancel  = v3;
    }

    public string           Text      { get; }
    public MessageBoxResult Result    { get; }
    public bool             IsDefault { get; }
    public bool             IsCancel  { get; }
}