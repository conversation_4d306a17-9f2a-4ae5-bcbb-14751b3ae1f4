using System;
using System.Linq;
using System.Windows;
using System.Windows.Input;
using DeviceGuard.Windows.Utility.Dialogs;
using DeviceGuard.Windows.Utility.Mvvm;
using Prism.Commands;

namespace DeviceGuard.Windows.Infrastructure.MessageBox.UserDialogImpl;

public class MessageBoxDialogViewModel : ViewModelBase
{
    /// <summary>
    /// 显示的文本
    /// </summary>
    public string Text { get; set; }

    /// <summary>
    /// 用户指定的消息框类型
    /// </summary>
    public MessageBoxSetting Setting { get; set; }

    /// <summary>
    /// 用户选择的结果
    /// </summary>
    public MessageBoxResult Result { get; set; } = MessageBoxResult.None;

    /// <summary>
    /// 关闭对话框(内部)
    /// </summary>
    public bool? DialogResult { get; set; }

    /// <summary>
    /// 显示的标题(内部)
    /// </summary>
    public string TitleOrDefault => Setting?.Title ?? Setting?.Icon switch
    {
        MessageBoxImage.Information => "提示",
        MessageBoxImage.Warning     => "警告",
        MessageBoxImage.Error       => "错误",
        MessageBoxImage.Question    => "请选择",
        _                           => "提示"
    };

    /// <summary>
    /// 按钮
    /// </summary>
    public ButtonBehavior[] Buttons
    {
        get
        {
            var setting = Setting;
            if (setting == null)
                return new ButtonBehavior[0];

            var buttonTexts = setting.ButtonTexts;

            switch (setting.Button)
            {
                case MessageBoxButton.OK:
                    buttonTexts = EnsureSize(buttonTexts, 1);
                    return new[]
                    {
                        new ButtonBehavior(buttonTexts[0] ?? "确定", MessageBoxResult.OK, true, true),
                    };
                case MessageBoxButton.OKCancel:
                    buttonTexts = EnsureSize(buttonTexts, 2);
                    return new[]
                    {
                        new ButtonBehavior(buttonTexts[0] ?? "确定", MessageBoxResult.OK,     true,  false),
                        new ButtonBehavior(buttonTexts[1] ?? "取消", MessageBoxResult.Cancel, false, true),
                    };
                case MessageBoxButton.YesNoCancel:
                    buttonTexts = EnsureSize(buttonTexts, 3);
                    return new[]
                    {
                        new ButtonBehavior(buttonTexts[0] ?? "是",  MessageBoxResult.Yes,    true,  false),
                        new ButtonBehavior(buttonTexts[1] ?? "否",  MessageBoxResult.No,     false, false),
                        new ButtonBehavior(buttonTexts[2] ?? "取消", MessageBoxResult.Cancel, false, true),
                    };
                case MessageBoxButton.YesNo:
                    buttonTexts = EnsureSize(buttonTexts, 2);
                    return new[]
                    {
                        new ButtonBehavior(buttonTexts[0] ?? "是", MessageBoxResult.Yes, true,  false),
                        new ButtonBehavior(buttonTexts[1] ?? "否", MessageBoxResult.No,  false, true),
                    };
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }
    }

    /// <summary>
    /// 确保数组的尺寸, 防止访问时候抛出异常
    /// </summary>
    /// <param name="array"></param>
    /// <param name="size"></param>
    /// <returns></returns>
    private string[] EnsureSize(string[] array, int size)
    {
        if (array == null)
            return new string[size];

        if (array.Length >= size)
            return array;

        var newArray = new string[size];
        Array.Copy(array, newArray, array.Length);

        return newArray;
    }

    public ICommand SelectCommand => GetPropertyCached(() => new DelegateCommand<MessageBoxResult?>(result =>
    {
        DialogResult = true;
        Result       = result ?? throw new InvalidOperationException("未知的返回值");
    }));

    public ICommand EnterCommand => GetPropertyCached(() => new DelegateCommand(() =>
    {
        if (Buttons.FirstOrDefault(c => c.IsDefault)?.Result is { } result)
        {
            DialogResult = true;
            Result       = result;
        }
    }));

    public ICommand EscapeCommand => GetPropertyCached(() => new DelegateCommand(() =>
    {
        if (Buttons.FirstOrDefault(c => c.IsCancel)?.Result is { } result)
        {
            DialogResult = true;
            Result       = result;
        }
    }));
}