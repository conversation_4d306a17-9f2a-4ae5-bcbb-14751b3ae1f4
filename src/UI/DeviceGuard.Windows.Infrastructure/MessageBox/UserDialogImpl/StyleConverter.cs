using System;
using System.Windows;
using System.Windows.Data;

namespace DeviceGuard.Windows.Infrastructure.MessageBox.UserDialogImpl;

public class StyleConverter : IValueConverter
{
    public Style True { get; set; }

    public Style False { get; set; }

    public object Convert(object values, Type targetType, object parameter, System.Globalization.CultureInfo culture)
    {
        if (values is bool val)
            return val ? True : False;

        return null;
    }

    public object ConvertBack(object value, Type targetTypes, object parameter, System.Globalization.CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}