using DeviceGuard.Windows.Controls.Dialog;

namespace DeviceGuard.Windows.Infrastructure.MessageBox.UserDialogImpl;

/// <summary>
/// Interaction logic for SampleDialog.xaml
/// </summary>
public partial class MessageBoxDialogView : UserDialog
{
    public MessageBoxDialogView()
    {
        InitializeComponent();

        Loaded += MessageBoxDialogView_Loaded;
    }

    private void MessageBoxDialogView_Loaded(object sender, System.Windows.RoutedEventArgs e)
    {
        PromptTextBox.Focus();
    }

    public MessageBoxDialogViewModel ViewModel => (MessageBoxDialogViewModel)DataContext;
}