using System.Threading;
using System.Threading.Tasks;
using System.Windows.Input;

namespace Hotwheels.Wpf;

public interface IAsyncCommand : ICommand
{
    bool IsExecuting { get; set; }

    /// <summary>
    /// Execute
    /// </summary>
    /// <param name="cancel"></param>
    /// <returns></returns>
    Task ExecuteAsync(CancellationToken cancel = default);

    /// <summary>
    /// Cancel the execution
    /// </summary>
    void Cancel();
}