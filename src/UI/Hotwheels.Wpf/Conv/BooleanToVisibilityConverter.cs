using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace Hotwheels.Wpf.Conv;

/// <summary>
/// 将 Boolean 装换为 Visibility 或者反过来
/// </summary>
public sealed class BooleanToVisibilityConverter : IValueConverter
{
    /// <summary>The visibility value for a not null value.</summary>
    public Visibility True { get; set; } = Visibility.Visible;

    /// <summary>The visibility value for a null value.</summary>
    public Visibility False { get; set; } = Visibility.Collapsed;

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        return value is true ? True : False;
    }

    public object ConvertBack(
        object      value,
        Type        targetType,
        object      parameter,
        CultureInfo culture)
    {
        return Binding.DoNothing;
    }
}