using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace Hotwheels.Wpf.Conv;

/// <summary>
/// Converts a null value to a <see cref="T:System.Windows.Visibility" />.
/// </summary>
public class NullToVisibilityConverter : IValueConverter
{
    /// <summary>The visibility value for a null value.</summary>
    public Visibility NullValue { get; set; } = Visibility.Collapsed;

    /// <summary>The visibility value for a not null value.</summary>
    public Visibility NotNullValue { get; set; } = Visibility.Visible;

    /// <summary>
    /// 空字符视为null
    /// </summary>
    public bool EmptyStringIsNull { get; set; } = true;

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        var isNull = value == null;

        if (EmptyStringIsNull && value is string str)
            isNull = string.IsNullOrEmpty(str);

        return isNull ? NullValue : NotNullValue;
    }

    public object ConvertBack(
        object      value,
        Type        targetType,
        object      parameter,
        CultureInfo culture)
    {
        return Binding.DoNothing;
    }
}