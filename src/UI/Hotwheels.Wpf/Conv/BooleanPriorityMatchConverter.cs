using System;
using System.Globalization;
using System.Windows.Data;

namespace Hotwheels.Wpf.Conv;

/// <summary>
/// 将 Boolean 装换为 参数给定的值或者反过来
/// </summary>
public sealed class BooleanPriorityMatchConverter : IValueConverter
{
    /// <summary>
    /// 当为 true 时，返回参数给定的值，否则返回 Binding.DoNothing
    /// </summary>
    public ParameterUsePolicy UseParameterWhen { get; set; }

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        switch (UseParameterWhen)
        {
            case ParameterUsePolicy.Null:
                return value is null ? parameter : Binding.DoNothing;
            case ParameterUsePolicy.NotNull:
                return value is not null ? parameter : Binding.DoNothing;
            case ParameterUsePolicy.True:
                return value is true ? parameter : Binding.DoNothing;
            case ParameterUsePolicy.False:
                return value is false ? parameter : Binding.DoNothing;
        }

        throw new NotSupportedException();
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}

public enum ParameterUsePolicy
{
    Null,
    NotNull,
    True, 
    False,
}
