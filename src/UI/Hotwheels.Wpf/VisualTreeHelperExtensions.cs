//*********************************************************
//
// Copyright (c) Microsoft. All rights reserved.
// This code is licensed under the MIT License (MIT).
// THE SOFTWARE IS PROVIDED “AS IS”, WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, 
// INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, 
// FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. 
// IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, 
// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, 
// TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH 
// THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
//
//*********************************************************

using System.Collections.Generic;
using System.Linq;
using System.Windows;
using System.Windows.Media;

namespace Hotwheels.Wpf;

public static class VisualTreeHelperExtensions
{
    public static T GetFirstChildrenOfType<T>(this DependencyObject start) where T : DependencyObject
    {
        return start.GetChildrenOfType<T>().FirstOrDefault();
    }

    public static IEnumerable<T> GetChildrenOfType<T>(this DependencyObject start) where T : DependencyObject
    {
        return start.GetChildren().OfType<T>();
    }

    public static IEnumerable<DependencyObject> GetChildren(this DependencyObject start)
    {
        var queue = new Queue<DependencyObject>();
        var count = VisualTreeHelper.GetChildrenCount(start);

        for (int i = 0; i < count; i++)
        {
            var child = VisualTreeHelper.GetChild(start, i);
            yield return child;
            queue.Enqueue(child);
        }

        while (queue.Count > 0)
        {
            var parent = queue.Dequeue();
            var count2 = VisualTreeHelper.GetChildrenCount(parent);

            for (int i = 0; i < count2; i++)
            {
                var child = VisualTreeHelper.GetChild(parent, i);
                yield return child;
                queue.Enqueue(child);
            }
        }
    }

    /// <summary>
    /// 向上查祖先
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="start"></param>
    /// <returns></returns>
    public static T GetFirstParentOfType<T>(this DependencyObject start) where T : DependencyObject
    {
        return start.GetParentsOfType<T>().FirstOrDefault();
    }

    public static IEnumerable<T> GetParentsOfType<T>(this DependencyObject start) where T : DependencyObject
    {
        return start.GetParents().OfType<T>();
    }

    public static IEnumerable<DependencyObject> GetParents(this DependencyObject start)
    {
        var parent = VisualTreeHelper.GetParent(start);

        while (parent != null)
        {
            yield return parent;
            parent = VisualTreeHelper.GetParent(parent);
        }
    }
}