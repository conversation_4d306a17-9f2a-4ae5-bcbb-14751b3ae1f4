<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <LangVersion>latest</LangVersion>
    <Platforms>AnyCPU;x64</Platforms>
    <!-- Suppress CA1416 warnings since this is a Windows-only WPF library -->
    <NoWarn>$(NoWarn);CA1416</NoWarn>
  </PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
		<DebugType>none</DebugType>
		<DebugSymbols>false</DebugSymbols>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
	  <DebugType>none</DebugType>
	  <DebugSymbols>false</DebugSymbols>
	</PropertyGroup>
	<ItemGroup>
	  <PackageReference Include="Doulex" Version="5.16.0" />
	</ItemGroup>

</Project>
