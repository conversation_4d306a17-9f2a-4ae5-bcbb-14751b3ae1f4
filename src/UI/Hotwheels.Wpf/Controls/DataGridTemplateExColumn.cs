using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;

namespace Hotwheels.Wpf.Controls;

/// <summary>
/// 扩展的模板列
/// 1. 支持焦点转移，用于在Cell获取焦点后，自动转移给内置的checkbox or button 上，请注意：checkbox or button 需要申明在 CellTemplate上不是CellEditingTemplate上
/// </summary>
public class DataGridTemplateExColumn : DataGridTemplateColumn
{
    /// <summary>
    /// 鼠标按下进入编辑状态
    /// </summary>
    public bool IsMouseClickEnterEditing
    {
        get => (bool)GetValue(IsMouseClickEnterEditingProperty);
        set => SetValue(IsMouseClickEnterEditingProperty, value);
    }

    // Using a DependencyProperty as the backing store for IsMouseClickEnterEditing.  This enables animation, styling, binding, etc...
    public static readonly DependencyProperty IsMouseClickEnterEditingProperty =
        DependencyProperty.Register(nameof(IsMouseClickEnterEditing), typeof(bool), typeof(DataGridTemplateExColumn), new PropertyMetadata(true));

    /// <summary>
    /// 空格键进入编辑状态
    /// </summary>
    public bool IsSpaceKeyEnterEditing
    {
        get => (bool)GetValue(IsSpaceKeyEnterEditingProperty);
        set => SetValue(IsSpaceKeyEnterEditingProperty, value);
    }

    // Using a DependencyProperty as the backing store for IsAnyKeyEnterEditing.  This enables animation, styling, binding, etc...
    public static readonly DependencyProperty IsSpaceKeyEnterEditingProperty =
        DependencyProperty.Register(nameof(IsSpaceKeyEnterEditing), typeof(bool), typeof(DataGridTemplateExColumn), new PropertyMetadata(true));


    /// <summary>
    /// 是否总是把焦点转移到里边的控件上（CellTemplate）
    /// 注意，请确保CellTemplate控件可以接收焦点，通常，Checkbox 直接定义在 CellTemplate 模板上的时候，使用这种方法让控件直接得到焦点
    /// </summary>
    public bool MoveFocusToElement
    {
        get => (bool)GetValue(MoveFocusToElementProperty);
        set => SetValue(MoveFocusToElementProperty, value);
    }

    // Using a DependencyProperty as the backing store for MoveFocusToElement.  This enables animation, styling, binding, etc...
    public static readonly DependencyProperty MoveFocusToElementProperty =
        DependencyProperty.Register(nameof(MoveFocusToElement), typeof(bool), typeof(DataGridTemplateExColumn), new PropertyMetadata(false));

    protected override FrameworkElement GenerateElement(DataGridCell cell, object dataItem)
    {
        if (MoveFocusToElement)
        {
            cell.GotFocus -= Cell_GotFocus;
            cell.GotFocus += Cell_GotFocus;
        }

        // 订阅鼠标事件
        cell.MouseDown -= Cell_MouseDown;
        cell.MouseDown += Cell_MouseDown;


        // 订阅键盘事件
        cell.KeyDown -= Cell_KeyDown;
        cell.KeyDown += Cell_KeyDown;

        return base.GenerateElement(cell, dataItem);
    }

    private void Cell_MouseDown(object sender, MouseButtonEventArgs e)
    {
        if (!(sender is DataGridCell cell) || cell.IsEditing || cell.IsReadOnly || !cell.IsEnabled)
            return;

        if (!cell.IsFocused)
            return;

        if (IsMouseClickEnterEditing && e.LeftButton == MouseButtonState.Pressed)
        {
            DataGridOwner.BeginEdit(e);
        }
    }


    /// <summary>
    /// 键盘按下，处理进入编辑的逻辑
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void Cell_KeyDown(object sender, KeyEventArgs e)
    {
        if (!(sender is DataGridCell cell) || cell.IsEditing || cell.IsReadOnly || !cell.IsEnabled)
            return;

        // TODO 这个属性应该改为控件键盘的风格像 TextBox ComboBox....
        if (IsSpaceKeyEnterEditing && e.Key == Key.Space)
        {
            DataGridOwner.BeginEdit();
        }
    }

    /// <summary>
    /// 单元格得到焦点时候，向前一步，让里边的控件得到焦点
    /// </summary>
    /// <param name="sender"></param>
    /// <param name="e"></param>
    private void Cell_GotFocus(object sender, RoutedEventArgs e)
    {
        if (sender is DataGridCell cell)
        {
            Dispatcher?.BeginInvoke(new Action(() =>
            {
                cell.MoveFocus(new TraversalRequest(FocusNavigationDirection.Next));
            }));
        }
    }
}