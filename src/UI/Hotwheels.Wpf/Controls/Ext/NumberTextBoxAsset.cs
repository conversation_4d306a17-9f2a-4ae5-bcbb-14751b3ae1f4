using System;
using System.Windows;
using System.Windows.Controls;

namespace Hotwheels.Wpf.Controls.Ext;

/// <summary>
/// 文本框数字控制
/// </summary>
public class NumberTextBoxAsset
{
    #region The Value that specified by valueType

    public static object GetValue(DependencyObject obj)
    {
        return obj.GetValue(ValueProperty);
    }

    public static void SetValue(DependencyObject obj, object value)
    {
        obj.SetValue(ValueProperty, value);
    }

    // Using a DependencyProperty as the backing store for Value.  This enables animation, styling, binding, etc...
    public static readonly DependencyProperty ValueProperty =
        DependencyProperty.RegisterAttached("Value", typeof(object), typeof(NumberTextBoxAsset),
            new FrameworkPropertyMetadata(null, FrameworkPropertyMetadataOptions.BindsTwoWayByDefault, OnValueChanged));

    private static void OnValueChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is not TextBox textBox)
            throw new InvalidOperationException("Just support TextBox control");

        var parsed = ParseText(textBox.Text, GetValueType(textBox));
        var value  = GetValue(textBox);

        // Ignore value when text can parse as the text
        if (value?.Equals(parsed) != true)
        {
            textBox.Text = value?.ToString() ?? "";
        }
    }

    #endregion

    #region Value Type indicate the type of the Value property

    public static ValueType GetValueType(DependencyObject obj)
    {
        return (ValueType) obj.GetValue(ValueTypeProperty);
    }

    public static void SetValueType(DependencyObject obj, ValueType value)
    {
        obj.SetValue(ValueTypeProperty, value);
    }

    // Using a DependencyProperty as the backing store for ValueType.  This enables animation, styling, binding, etc...
    public static readonly DependencyProperty ValueTypeProperty =
        DependencyProperty.RegisterAttached("ValueType", typeof(ValueType), typeof(NumberTextBoxAsset),
            new PropertyMetadata(ValueType.Any, OnValueTypeChanged));

    private static void OnValueTypeChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is not TextBox textBox)
            throw new InvalidOperationException("Just support TextBox control");

        textBox.TextChanged -= TextBox_TextChanged;

        var type = GetValueType(textBox);
        switch (type)
        {
            case ValueType.Int32:
            case ValueType.Double:
            case ValueType.UInt32:
                textBox.TextChanged += TextBox_TextChanged;
                break;
            case ValueType.Any:
                break;
            default:
                throw new NotSupportedException();
        }

        var value = ParseText(textBox.Text, type);
        SetValue(textBox, value);
    }

    private static void TextBox_TextChanged(object sender, TextChangedEventArgs e)
    {
        if (sender is not TextBox textBox)
            return;

        var type  = GetValueType(textBox);
        var value = ParseText(textBox.Text, type);
        SetValue(textBox, value);
    }

    /// <summary>
    /// 解析文本
    /// </summary>
    /// <param name="text"></param>
    /// <param name="type"></param>
    /// <returns></returns>
    private static object ParseText(string text, ValueType type)
    {
        switch (type)
        {
            case ValueType.Any:
                return text;
            case ValueType.Int32:
            {
                return int.TryParse(text, out var val) ? val : null;
            }
            case ValueType.Double:
            {
                return double.TryParse(text, out var val) ? val : null;
            }
            case ValueType.UInt32:
            {
                return uint.TryParse(text, out var val) ? val : null;
            }
        }

        throw new NotSupportedException();
    }

    #endregion
}