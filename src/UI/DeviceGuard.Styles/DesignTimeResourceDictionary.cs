using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Windows;

namespace DeviceGuard.Styles;

/// <summary>
/// 设计时引用的资源
/// https://stackoverflow.com/questions/995101/how-do-i-get-wysiwyg-design-in-blend-vs-without-merged-resourcedictionary-in-ever
/// </summary>
/// <remark>
/// HOW TO USE
/// <ResourceDictionary.MergedDictionaries>
///     <skin:DesignTimeResourceDictionary Source="pack://application:,,,/skin;component/DefaultStyle.xaml" />
/// </ResourceDictionary.MergedDictionaries >
/// </remark>
public class DesignTimeResourceDictionary : ResourceDictionary
{
    public bool IsInDesignMode =>
        (bool) DependencyPropertyDescriptor.FromProperty(
            DesignerProperties.IsInDesignModeProperty,
            typeof(DependencyObject)
        ).Metadata.DefaultValue;

    public new Uri Source
    {
        get => base.Source;
        set
        {
            if (!IsInDesignMode)
                return;

            Debug.WriteLine("Setting Source = " + value);
            base.Source = value;
        }
    }
}