<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- MahApps Brushes -->
    <SolidColorBrush x:Key="MahApps.Brushes.Highlight"
                     Color="{DynamicResource Primary100}" />
    <SolidColorBrush x:Key="MahApps.Brushes.AccentBase"
                     Color="{DynamicResource Primary600}" />
    <SolidColorBrush x:Key="MahApps.Brushes.Accent"
                     Color="{DynamicResource Primary500}" />
    <SolidColorBrush x:Key="MahApps.Brushes.Accent2"
                     Color="{DynamicResource Primary400}" />
    <SolidColorBrush x:Key="MahApps.Brushes.Accent3"
                     Color="{DynamicResource Primary300}" />
    <SolidColorBrush x:Key="MahApps.Brushes.Accent4"
                     Color="{DynamicResource Primary200}" />
    <SolidColorBrush x:Key="MahApps.Brushes.WindowTitle"
                     Color="{DynamicResource Primary700}" />
    <SolidColorBrush x:Key="MahApps.Brushes.WindowTitle.NonActive"
                     Color="{DynamicResource Primary200}" />

    <SolidColorBrush x:Key="MahApps.Brushes.Selected.Foreground"
                     Color="{DynamicResource Primary500Foreground}" />
    <LinearGradientBrush x:Key="MahApps.Brushes.Progress"
                         EndPoint="0.001,0.5"
                         StartPoint="1.002,0.5">
        <GradientStop Color="{DynamicResource Primary700}"
                      Offset="0" />
        <GradientStop Color="{DynamicResource Primary300}"
                      Offset="1" />
    </LinearGradientBrush>
    <SolidColorBrush x:Key="MahApps.Brushes.CheckmarkFill"
                     Color="{DynamicResource Primary500}" />
    <SolidColorBrush x:Key="MahApps.Brushes.RightArrowFill"
                     Color="{DynamicResource Primary500}" />
    <SolidColorBrush x:Key="MahApps.Brushes.IdealForeground"
                     Color="{DynamicResource Primary500Foreground}" />
    <SolidColorBrush x:Key="MahApps.Brushes.IdealForegroundDisabled"
                     Color="{DynamicResource Primary500}"
                     Opacity="0.4" />

</ResourceDictionary>
