<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:wpf="http://materialdesigninxaml.net/winfx/xaml/themes">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.CheckBox.xaml" />
        <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.ComboBox.xaml" />
        <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Menu.xaml" />
        <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.RadioButton.xaml" />
        <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Shadows.xaml" />
        <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.TextBox.xaml" />
        <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Button.xaml" />
        <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.ToggleButton.xaml" />
        <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Font.xaml" />

        <ResourceDictionary Source="pack://application:,,,/DeviceGuard.Styles;component/Override/MaterialDesignTheme.ListBox.Override.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />

    <Style x:Key="MaterialDesignToolBarVerticalOverflowButtonStyle"
           TargetType="{x:Type ToggleButton}">
        <Setter Property="Background"
                Value="{DynamicResource MaterialDesignToolBarBackground}" />
        <Setter Property="MinHeight"
                Value="0" />
        <Setter Property="MinWidth"
                Value="0" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToggleButton}">
                    <Border x:Name="Bd"
                            Background="{TemplateBinding Background}"
                            CornerRadius="0,0,3,3"
                            SnapsToDevicePixels="true">
                        <Viewbox Width="16"
                                 Height="16">
                            <Canvas Width="24"
                                    Height="24">
                                <Path
                                    Data="M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z"
                                    Fill="{TemplateBinding Foreground}" />
                            </Canvas>
                        </Viewbox>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver"
                                 Value="true">
                            <!--Setter Property="Background" TargetName="Bd" Value="{StaticResource ToolBarButtonHover}"/-->
                        </Trigger>
                        <Trigger Property="IsKeyboardFocused"
                                 Value="true">
                            <!--Setter Property="Background" TargetName="Bd" Value="{StaticResource ToolBarButtonHover}"/-->
                        </Trigger>
                        <Trigger Property="IsEnabled"
                                 Value="false">
                            <Setter Property="Opacity"
                                    Value=".16" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <DataTrigger Binding="{Binding Source={x:Static SystemParameters.HighContrast}}"
                         Value="true">
                <Setter Property="Background"
                        Value="{DynamicResource {x:Static SystemColors.ControlBrushKey}}" />
            </DataTrigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="MaterialDesignToolBarHorizontalOverflowButtonStyle"
           TargetType="{x:Type ToggleButton}">
        <Setter Property="Background"
                Value="{DynamicResource MaterialDesignToolBarBackground}" />
        <Setter Property="MinHeight"
                Value="0" />
        <Setter Property="MinWidth"
                Value="0" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToggleButton}">
                    <Border x:Name="Bd"
                            Background="{TemplateBinding Background}"
                            CornerRadius="0,3,3,0"
                            SnapsToDevicePixels="true">
                        <Viewbox Width="16"
                                 Height="16"
                                 Margin="8 0 8 0">
                            <Canvas Width="24"
                                    Height="24">
                                <Path
                                    Data="M12,16A2,2 0 0,1 14,18A2,2 0 0,1 12,20A2,2 0 0,1 10,18A2,2 0 0,1 12,16M12,10A2,2 0 0,1 14,12A2,2 0 0,1 12,14A2,2 0 0,1 10,12A2,2 0 0,1 12,10M12,4A2,2 0 0,1 14,6A2,2 0 0,1 12,8A2,2 0 0,1 10,6A2,2 0 0,1 12,4Z"
                                    Fill="{TemplateBinding Foreground}" />
                            </Canvas>
                        </Viewbox>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver"
                                 Value="true">
                            <!--Setter Property="Background" TargetName="Bd" Value="{StaticResource ToolBarButtonHover}"/-->
                        </Trigger>
                        <Trigger Property="IsKeyboardFocused"
                                 Value="true">
                            <!--Setter Property="Background" TargetName="Bd" Value="{StaticResource ToolBarButtonHover}"/-->
                        </Trigger>
                        <Trigger Property="IsEnabled"
                                 Value="false">
                            <Setter Property="Opacity"
                                    Value=".16" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <DataTrigger Binding="{Binding Source={x:Static SystemParameters.HighContrast}}"
                         Value="true">
                <Setter Property="Background"
                        Value="{DynamicResource {x:Static SystemColors.ControlBrushKey}}" />
            </DataTrigger>
        </Style.Triggers>
    </Style>

    <Style x:Key="MaterialDesignToolBarThumbStyle"
           TargetType="{x:Type Thumb}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Thumb}">
                    <Border Background="Transparent"
                            SnapsToDevicePixels="True"
                            Padding="{TemplateBinding Padding}">
                        <Rectangle>
                            <Rectangle.Fill>
                                <DrawingBrush TileMode="Tile"
                                              Viewbox="0,0,4,4"
                                              Viewport="0,0,4,4"
                                              ViewportUnits="Absolute"
                                              ViewboxUnits="Absolute">
                                    <DrawingBrush.Drawing>
                                        <DrawingGroup>
                                            <GeometryDrawing Brush="{DynamicResource MaterialDesignDivider}"
                                                             Geometry="M 0 0 L 0 2 L 2 2 L 2 0 z" />
                                        </DrawingGroup>
                                    </DrawingBrush.Drawing>
                                </DrawingBrush>
                            </Rectangle.Fill>
                        </Rectangle>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver"
                                 Value="true">
                            <Setter Property="Cursor"
                                    Value="SizeAll" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="MaterialDesignToolBarMainPanelBorderStyle"
           TargetType="{x:Type Border}">
        <Setter Property="Margin"
                Value="0,0,11,0" />
        <Setter Property="CornerRadius"
                Value="3,3,3,3" />
    </Style>

    <Style x:Key="MaterialDesignToolBar"
           TargetType="{x:Type ToolBar}">
        <Setter Property="Background"
                Value="{DynamicResource MaterialDesignToolBarBackground}" />
        <Setter Property="Foreground"
                Value="{DynamicResource MaterialDesignBody}" />
        <Setter Property="ClipToBounds"
                Value="True" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToolBar}">
                    <ControlTemplate.Resources>
                        <Style TargetType="{x:Type wpf:PackIcon}">
                            <!-- ReSharper disable Xaml.RedundantPropertyTypeQualifier -->
                            <Setter Property="FrameworkElement.Width"
                                    Value="20" />
                            <Setter Property="FrameworkElement.Height"
                                    Value="20" />
                            <!-- ReSharper restore Xaml.RedundantPropertyTypeQualifier -->
                        </Style>
                        <Style TargetType="ListBox"
                               BasedOn="{StaticResource MaterialDesignToolToggleFlatListBox}">
                            <Setter Property="ItemContainerStyle">
                                <Setter.Value>
                                    <Style TargetType="ListBoxItem"
                                           BasedOn="{StaticResource MaterialDesignToolToggleListBoxItem}">
                                        <Setter Property="TextBlock.FontWeight"
                                                Value="DemiBold" />
                                        <Setter Property="TextBlock.FontSize"
                                                Value="18" />
                                        <Setter Property="MinWidth"
                                                Value="42" />
                                        <Setter Property="HorizontalContentAlignment"
                                                Value="Center" />
                                        <Setter Property="Padding"
                                                Value="10" />
                                        <Setter Property="Opacity"
                                                Value=".92" />
                                        <Style.Triggers>
                                            <Trigger Property="IsEnabled"
                                                     Value="False">
                                                <Setter Property="Opacity"
                                                        Value=".16" />
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </ControlTemplate.Resources>
                    <DockPanel x:Name="Grid"
                               Margin="3,1,1,1"
                               SnapsToDevicePixels="true"
                               Background="{TemplateBinding Background}">
                        <Grid DockPanel.Dock="Right"
                              x:Name="OverflowGrid"
                              HorizontalAlignment="Right">
                            <ToggleButton x:Name="OverflowButton"
                                          ClickMode="Press"
                                          FocusVisualStyle="{x:Null}"
                                          IsChecked="{Binding IsOverflowOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                          Visibility="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=HasOverflowItems, Converter={StaticResource BooleanToVisibilityConverter}}"
                                          Style="{StaticResource MaterialDesignToolBarHorizontalOverflowButtonStyle}"
                                          Foreground="{TemplateBinding Foreground}" />
                            <Popup x:Name="OverflowPopup"
                                   AllowsTransparency="true"
                                   Focusable="false"
                                   IsOpen="{Binding IsOverflowOpen, RelativeSource={RelativeSource TemplatedParent}}"
                                   PopupAnimation="{DynamicResource {x:Static SystemParameters.ComboBoxPopupAnimationKey}}"
                                   Placement="Bottom"
                                   StaysOpen="false"
                                   Margin="1">
                                <!-- warning, this will cache the inner item as well, consider separating the shadow from the item if possible -->
                                <Popup.CacheMode>
                                    <BitmapCache EnableClearType="True"
                                                 SnapsToDevicePixels="True" />
                                </Popup.CacheMode>
                                <Border x:Name="ToolBarSubMenuBorder"
                                        BorderBrush="{DynamicResource MaterialDesignDivider}"
                                        BorderThickness="1"
                                        Background="{DynamicResource MaterialDesignToolBarBackground}"
                                        TextBlock.FontWeight="ExtraBold"
                                        CornerRadius="2"
                                        Margin="1"
                                        Effect="{StaticResource MaterialDesignShadowDepth2}"
                                        RenderOptions.ClearTypeHint="Enabled">
                                    <Border Background="Transparent">
                                        <ToolBarOverflowPanel x:Name="PART_ToolBarOverflowPanel"
                                                              KeyboardNavigation.DirectionalNavigation="Cycle"
                                                              FocusVisualStyle="{x:Null}"
                                                              Focusable="true"
                                                              Margin="2"
                                                              SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                                                              KeyboardNavigation.TabNavigation="Cycle"
                                                              WrapWidth="200" />
                                    </Border>
                                </Border>
                            </Popup>
                        </Grid>
                        <Border x:Name="MainPanelBorder"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                Background="{TemplateBinding Background}"
                                Padding="{TemplateBinding Padding}"
                                Style="{StaticResource MaterialDesignToolBarMainPanelBorderStyle}">
                            <DockPanel KeyboardNavigation.TabIndex="1"
                                       KeyboardNavigation.TabNavigation="Local">
                                <Thumb x:Name="ToolBarThumb"
                                       Margin="-3,-1,4,0"
                                       Padding="6,5,1,3"
                                       Style="{StaticResource MaterialDesignToolBarThumbStyle}"
                                       Width="10" />
                                <ContentPresenter x:Name="ToolBarHeader"
                                                  ContentSource="Header"
                                                  HorizontalAlignment="Center"
                                                  Margin="4,0,4,0"
                                                  SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                                                  VerticalAlignment="Center" />
                                <ToolBarPanel x:Name="PART_ToolBarPanel"
                                              IsItemsHost="true"
                                              Margin="0,0,2,0"
                                              SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                            </DockPanel>
                        </Border>
                    </DockPanel>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsOverflowOpen"
                                 Value="true">
                            <Setter Property="IsEnabled"
                                    TargetName="ToolBarThumb"
                                    Value="false" />
                        </Trigger>
                        <Trigger Property="Header"
                                 Value="{x:Null}">
                            <Setter Property="Visibility"
                                    TargetName="ToolBarHeader"
                                    Value="Collapsed" />
                        </Trigger>
                        <Trigger Property="ToolBarTray.IsLocked"
                                 Value="true">
                            <Setter Property="Visibility"
                                    TargetName="ToolBarThumb"
                                    Value="Collapsed" />
                        </Trigger>
                        <Trigger Property="HasDropShadow"
                                 SourceName="OverflowPopup"
                                 Value="true">
                            <Setter Property="Margin"
                                    TargetName="ToolBarSubMenuBorder"
                                    Value="5,5,5,5" />
                        </Trigger>
                        <Trigger Property="Orientation"
                                 Value="Vertical">
                            <Setter Property="Margin"
                                    TargetName="Grid"
                                    Value="1,3,1,1" />
                            <Setter Property="Style"
                                    TargetName="OverflowButton"
                                    Value="{StaticResource MaterialDesignToolBarVerticalOverflowButtonStyle}" />
                            <Setter Property="Height"
                                    TargetName="ToolBarThumb"
                                    Value="10" />
                            <Setter Property="Width"
                                    TargetName="ToolBarThumb"
                                    Value="Auto" />
                            <Setter Property="Margin"
                                    TargetName="ToolBarThumb"
                                    Value="-1,-3,0,0" />
                            <Setter Property="Padding"
                                    TargetName="ToolBarThumb"
                                    Value="5,6,6,1" />
                            <Setter Property="Margin"
                                    TargetName="ToolBarHeader"
                                    Value="0,0,0,4" />
                            <Setter Property="Margin"
                                    TargetName="PART_ToolBarPanel"
                                    Value="1,0,2,2" />
                            <Setter Property="DockPanel.Dock"
                                    TargetName="ToolBarThumb"
                                    Value="Top" />
                            <Setter Property="DockPanel.Dock"
                                    TargetName="ToolBarHeader"
                                    Value="Top" />
                            <Setter Property="HorizontalAlignment"
                                    TargetName="OverflowGrid"
                                    Value="Stretch" />
                            <Setter Property="VerticalAlignment"
                                    TargetName="OverflowGrid"
                                    Value="Bottom" />
                            <Setter Property="Placement"
                                    TargetName="OverflowPopup"
                                    Value="Right" />
                            <Setter Property="Margin"
                                    TargetName="MainPanelBorder"
                                    Value="0,0,0,11" />
                            <Setter Property="Background"
                                    Value="{DynamicResource MaterialDesignToolBarBackground}" />
                        </Trigger>
                        <Trigger Property="IsEnabled"
                                 Value="false">
                            <Setter Property="Opacity"
                                    Value=".16" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="{x:Static ToolBar.CheckBoxStyleKey}"
           TargetType="{x:Type CheckBox}"
           BasedOn="{StaticResource MaterialDesignCheckBox}">
        <Setter Property="Margin"
                Value="8 0 8 0" />
    </Style>
    <Style x:Key="{x:Static ToolBar.ComboBoxStyleKey}"
           TargetType="{x:Type ComboBox}"
           BasedOn="{StaticResource MaterialDesignComboBox}">
        <Setter Property="Margin"
                Value="8 0 8 0" />
    </Style>
    <Style x:Key="{x:Static ToolBar.MenuStyleKey}"
           TargetType="{x:Type Menu}"
           BasedOn="{StaticResource MaterialDesignMenu}">
        <Setter Property="Margin"
                Value="8 0 8 0" />
    </Style>
    <Style x:Key="{x:Static ToolBar.RadioButtonStyleKey}"
           TargetType="{x:Type RadioButton}"
           BasedOn="{StaticResource MaterialDesignRadioButton}">
        <Setter Property="Margin"
                Value="8 0 8 0" />
    </Style>
    <Style x:Key="{x:Static ToolBar.SeparatorStyleKey}"
           TargetType="Separator">
        <Setter Property="Background"
                Value="{DynamicResource MaterialDesignDivider}" />
        <Setter Property="BorderBrush"
                Value="{DynamicResource MaterialDesignDivider}" />
        <Setter Property="MinHeight"
                Value="1" />
        <Setter Property="Margin"
                Value="6 11 6 11" />
        <Setter Property="SnapsToDevicePixels"
                Value="true" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Separator}">
                    <Border BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Background="{TemplateBinding Background}" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    <Style x:Key="{x:Static ToolBar.TextBoxStyleKey}"
           TargetType="{x:Type TextBox}"
           BasedOn="{StaticResource MaterialDesignTextBox}">
        <Setter Property="Margin"
                Value="8 0 8 0" />
    </Style>
    <Style x:Key="{x:Static ToolBar.ToggleButtonStyleKey}"
           TargetType="{x:Type ToggleButton}"
           BasedOn="{StaticResource MaterialDesignSwitchToggleButton}">
        <Setter Property="Margin"
                Value="8 0 8 0" />
    </Style>

    <Style x:Key="{x:Static ToolBar.ButtonStyleKey}"
           TargetType="Button">
        <Setter Property="BorderThickness"
                Value="1" />
        <Setter Property="Padding"
                Value="10" />
        <Setter Property="HorizontalContentAlignment"
                Value="Center" />
        <Setter Property="VerticalContentAlignment"
                Value="Center" />
        <Setter Property="TextBlock.FontWeight"
                Value="DemiBold" />
        <Setter Property="Foreground"
                Value="{DynamicResource MaterialDesignBody}" />
        <Setter Property="wpf:RippleAssist.Feedback"
                Value="{DynamicResource MaterialDesignFlatButtonRipple}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Button}">
                    <Border Background="Transparent"
                            x:Name="border"
                            CornerRadius="2">
                        <wpf:Ripple x:Name="ripple"
                                    Content="{TemplateBinding Content}"
                                    ContentTemplate="{TemplateBinding ContentTemplate}"
                                    Focusable="False"
                                    Feedback="{TemplateBinding Foreground}"
                                    HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                    VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                    Padding="{TemplateBinding Padding}"
                                    SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled"
                                 Value="false">
                            <Setter Property="Opacity"
                                    Value=".16" />
                        </Trigger>
                        <Trigger Property="IsMouseOver"
                                 Value="true">
                            <Setter Property="BorderBrush"
                                    TargetName="border"
                                    Value="{DynamicResource MaterialDesignFlatButtonClick}" />
                            <Setter Property="Background"
                                    TargetName="border"
                                    Value="{DynamicResource MaterialDesignFlatButtonClick}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
