<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:wpf="http://materialdesigninxaml.net/winfx/xaml/themes"
                    xmlns:converters="http://materialdesigninxaml.net/winfx/xaml/themes">

    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary
            Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Shadows.xaml" />
        <ResourceDictionary
            Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.CheckBox.xaml" />
        <ResourceDictionary
            Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.RadioButton.xaml" />
        <ResourceDictionary>
            <converters:BrushRoundConverter x:Key="BrushRoundConverter" />
            <converters:EqualityToVisibilityConverter x:Key="EqualityToVisibilityConverter" />
        </ResourceDictionary>
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="MaterialDesignListBoxItem" TargetType="{x:Type ListBoxItem}">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="HorizontalContentAlignment"
                Value="{Binding HorizontalContentAlignment, RelativeSource={RelativeSource AncestorType={x:Type ItemsControl}}}" />
        <Setter Property="VerticalContentAlignment"
                Value="{Binding VerticalContentAlignment, RelativeSource={RelativeSource AncestorType={x:Type ItemsControl}}}" />
        <Setter Property="Padding" Value="8" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="wpf:ListBoxItemAssist.ShowSelection" Value="True" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ListBoxItem}">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Margin="{TemplateBinding Margin}"
                            ClipToBounds="{TemplateBinding ClipToBounds}">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup Name="CommonStates">
                                <VisualStateGroup.Transitions>
                                    <VisualTransition GeneratedDuration="0:0:0.03" To="Normal">
                                        <VisualTransition.GeneratedEasingFunction>
                                            <CircleEase EasingMode="EaseOut" />
                                        </VisualTransition.GeneratedEasingFunction>
                                    </VisualTransition>
                                </VisualStateGroup.Transitions>
                                <VisualState Name="Normal" />
                                <VisualState Name="MouseOver">
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="MouseOverBorder"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="0.1" Duration="0" />
                                    </Storyboard>
                                </VisualState>
                                <VisualState Name="Disabled" />
                            </VisualStateGroup>
                            <VisualStateGroup Name="SelectionStates">
                                <VisualStateGroup.Transitions>
                                    <VisualTransition GeneratedDuration="0:0:0.06" />
                                </VisualStateGroup.Transitions>
                                <VisualState Name="Selected">
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="SelectedBorder"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="0.18" Duration="0" />
                                    </Storyboard>
                                </VisualState>
                                <VisualState Name="Unselected" />
                                <VisualState Name="SelectedUnfocused">
                                    <Storyboard>
                                        <DoubleAnimation Storyboard.TargetName="SelectedBorder"
                                                         Storyboard.TargetProperty="Opacity"
                                                         To="0.15" Duration="0" />
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Grid>
                            <Border x:Name="MouseOverBorder"
                                    Opacity="0"
                                    Background="{TemplateBinding Foreground, Converter={StaticResource BrushRoundConverter}}" />

                            <Border x:Name="SelectedBorder" Opacity="0"
                                    Background="{TemplateBinding Foreground, Converter={StaticResource BrushRoundConverter}}">
                            </Border>

                            <Border
                                Padding="{TemplateBinding Padding}"
                                HorizontalAlignment="Stretch"
                                VerticalAlignment="Stretch">

                                <ContentPresenter
                                    Focusable="False"
                                    Content="{TemplateBinding Content}"
                                    ContentTemplate="{TemplateBinding ContentTemplate}"
                                    ContentTemplateSelector="{TemplateBinding ContentTemplateSelector}"
                                    SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                                    HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                    VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                     />
                            </Border>
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" Value=".56" />
                        </Trigger>
                        <DataTrigger
                            Binding="{Binding RelativeSource={RelativeSource Self}, Path=(wpf:ListBoxItemAssist.ShowSelection)}"
                            Value="False">
                            <Setter TargetName="MouseOverBorder" Property="Visibility" Value="Collapsed" />
                            <Setter TargetName="SelectedBorder" Property="Visibility" Value="Collapsed" />
                        </DataTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="MaterialDesignListBox" TargetType="{x:Type ListBox}">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}" />
        <Setter Property="ItemContainerStyle" Value="{StaticResource MaterialDesignListBoxItem}" />
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Disabled" />
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto" />
        <Setter Property="ScrollViewer.CanContentScroll" Value="true" />
        <Setter Property="ScrollViewer.PanningMode" Value="Both" />
        <Setter Property="Stylus.IsFlicksEnabled" Value="False" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ListBox}">
                    <Border x:Name="Bd" BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            Background="{TemplateBinding Background}" SnapsToDevicePixels="true">
                        <ScrollViewer Focusable="false" Padding="{TemplateBinding Padding}">
                            <ItemsPresenter SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                        </ScrollViewer>
                    </Border>
                    <ControlTemplate.Triggers>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsGrouping" Value="true" />
                                <Condition Property="VirtualizingPanel.IsVirtualizingWhenGrouping" Value="false" />
                            </MultiTrigger.Conditions>
                            <Setter Property="ScrollViewer.CanContentScroll" Value="false" />
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style BasedOn="{StaticResource MaterialDesignListBox}" TargetType="{x:Type ListBox}">
    </Style>
</ResourceDictionary>