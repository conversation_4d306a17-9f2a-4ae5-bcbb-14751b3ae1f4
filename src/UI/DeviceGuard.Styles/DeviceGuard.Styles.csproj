<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <OutputType>Library</OutputType>
    <PackageId>DeviceGuard.Styles</PackageId>
    <LangVersion>default</LangVersion>
    <Platforms>AnyCPU;x64</Platforms>
  </PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
		<DebugType>none</DebugType>
		<DebugSymbols>false</DebugSymbols>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
	  <DebugType>none</DebugType>
	  <DebugSymbols>false</DebugSymbols>
	</PropertyGroup>
  <ItemGroup>
    <Page Remove="AppStyle.xaml" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="Dragablz" Version="0.0.3.223" />
    <PackageReference Include="MahApps.Metro" Version="2.4.10" />
    <PackageReference Include="MaterialDesignExtensions" Version="3.3.0" />
    <PackageReference Include="MaterialDesignThemes" Version="5.2.1" />
    <PackageReference Include="MaterialDesignThemes.MahApps" Version="0.1.7" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\Hotwheels.Wpf\Hotwheels.Wpf.csproj" />
    <ProjectReference Include="..\DeviceGuard.Windows.Controls\DeviceGuard.Windows.Controls.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="AppStyle.xaml">
      <ContainsDesignTimeResources>true</ContainsDesignTimeResources>
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Resource>
  </ItemGroup>
  <ItemGroup>
    <Page Update="Override\MaterialDesignTheme.ListBox.Override.xaml">
      <XamlRuntime>$(DefaultXamlRuntime)</XamlRuntime>
      <SubType>Designer</SubType>
    </Page>
    <Page Update="Override\MaterialDesignTheme.Toolbar.Override.xaml">
      <XamlRuntime>$(DefaultXamlRuntime)</XamlRuntime>
      <SubType>Designer</SubType>
    </Page>
    <Page Update="Styles\ChromeTabControlStyle.xaml">
      <XamlRuntime>$(DefaultXamlRuntime)</XamlRuntime>
      <SubType>Designer</SubType>
    </Page>
  </ItemGroup>
</Project>
