<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Style x:Key="TopTabItemStyle"
           TargetType="{x:Type TabItem}">
        <Setter Property="Background"
                Value="Transparent" />
        <Setter Property="BorderBrush"
                Value="Transparent" />
        <Setter Property="BorderThickness"
                Value="0" />
        <Setter Property="FocusVisualStyle"
                Value="{x:Null}" />
        <Setter Property="Foreground"
                Value="Gray" />
        <Setter Property="HorizontalContentAlignment"
                Value="Stretch" />
        <Setter Property="IsTabStop"
                Value="False" />
        <Setter Property="MinHeight"
                Value="5" />
        <Setter Property="MinWidth"
                Value="5" />
        <Setter Property="Padding"
                Value="30,6" />
        <Setter Property="SnapsToDevicePixels"
                Value="True" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TabItem}">
                    <Border x:Name="Border"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="1 3 1 0"
                            Background="{TemplateBinding Background}"
                            HorizontalAlignment="Stretch"
                            SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}">
                        <Grid HorizontalAlignment="Stretch"
                              UseLayoutRounding="True"
                              VerticalAlignment="Stretch">
                            <ContentControl x:Name="ContentSite"
                                            Margin="{TemplateBinding Padding}"
                                            ContentTemplate="{TemplateBinding HeaderTemplate}"
                                            Content="{TemplateBinding Header}"
                                            ContentStringFormat="{TemplateBinding HeaderStringFormat}"
                                            Foreground="{TemplateBinding Foreground}"
                                            FontStyle="{TemplateBinding FontStyle}"
                                            FontFamily="{TemplateBinding FontFamily}"
                                            FontSize="18"
                                            SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                                            UseLayoutRounding="False" />
                            <Rectangle Name="CoverRectangle"
                                       Opacity="0"
                                       Fill="{TemplateBinding Foreground}" />
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver"
                                 SourceName="Border"
                                 Value="True">
                            <Setter Property="Opacity"
                                    TargetName="CoverRectangle"
                                    Value="0.3" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="VerticalContentAlignment"
                Value="Stretch" />
        <Style.Triggers>
            <Trigger Property="IsSelected"
                     Value="True">
                <Setter Property="Foreground"
                        Value="Black" />
                <Setter Property="Background"
                        Value="White" />
                <Setter Property="BorderBrush"
                        Value="{Binding RelativeSource={RelativeSource FindAncestor, AncestorType=TabControl}, Path=BorderBrush}" />
                <Setter Property="Margin"
                        Value="0 0 0 -1" />
            </Trigger>
            <Trigger Property="IsEnabled"
                     Value="False">
                <Setter Property="Opacity"
                        Value="0.2" />
            </Trigger>

        </Style.Triggers>
    </Style>

    <Style x:Key="TopTabControlStyle"
           TargetType="{x:Type TabControl}">
        <Setter Property="ItemContainerStyle"
                Value="{StaticResource TopTabItemStyle}" />
        <Setter Property="HorizontalContentAlignment"
                Value="Left" />
        <Setter Property="BorderBrush"
                Value="#3F51B5" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TabControl}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition x:Name="RowDefinition0"
                                           Height="Auto" />
                            <RowDefinition x:Name="RowDefinition1"
                                           Height="*" />
                        </Grid.RowDefinitions>
                        <StackPanel x:Name="HeaderPanel"
                                    Orientation="Horizontal"
                                    IsItemsHost="True"
                                    HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                    Grid.Row="0"
                                    Panel.ZIndex="1"
                                    KeyboardNavigation.TabIndex="1" />
                        <Border x:Name="ContentPanel"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                Background="{TemplateBinding Background}"
                                KeyboardNavigation.DirectionalNavigation="Contained"
                                Grid.Row="1"
                                SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}"
                                KeyboardNavigation.TabIndex="2"
                                KeyboardNavigation.TabNavigation="Local">
                            <ContentPresenter x:Name="PART_SelectedContentHost"
                                              ContentTemplate="{TemplateBinding SelectedContentTemplate}"
                                              Content="{TemplateBinding SelectedContent}"
                                              ContentStringFormat="{TemplateBinding SelectedContentStringFormat}"
                                              ContentSource="SelectedContent"
                                              Margin="{TemplateBinding Padding}"
                                              SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" />
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
