namespace DeviceGuard.Modules.Interface;

/// <summary>
/// 模块的启动初始化接口, 每一个模块都必须实现这个接口 
/// </summary>
public interface IModuleLoader
{
    /// <summary>
    /// 使用传入模块的选项开关初始化模块
    /// </summary>
    Task InitializeAsync(string? options, CancellationToken cancellationToken);

    /// <summary>
    /// 获取模块的视图加载器 
    /// </summary>
    /// <returns>返回模块的视图加载器, 如果不支持, 返回 null</returns>
    IModuleViewLoader[] GetViewLoaders(ModuleViewCategory viewCategory);
}
