using DeviceGuard.Windows.Interface.ViewLoader;

namespace DeviceGuard.Modules.Interface;

/// <summary>
/// 模块的视图对象, 用于在 Bootstrap 加载
/// </summary>
public interface IModuleViewLoader : IViewLoader
{
    /// <summary>
    /// 视图的名字
    /// </summary>
    public string Name { get; }

    /// <summary>
    /// 获取当前视图类型
    /// </summary>
    public ModuleViewCategory Category { get; }

    /// <summary>
    /// 在 RegionManager 中显示当前视图
    /// </summary>
    /// <param name="regionName"></param>
    void Show(string regionName);
}
