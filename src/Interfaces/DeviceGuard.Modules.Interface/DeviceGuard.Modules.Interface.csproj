<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net6.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <Platforms>AnyCPU;x64</Platforms>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Prism.Core" Version="9.0.537" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\DeviceGuard.Windows.Interface\DeviceGuard.Windows.Interface.csproj" />
    </ItemGroup>

</Project>
