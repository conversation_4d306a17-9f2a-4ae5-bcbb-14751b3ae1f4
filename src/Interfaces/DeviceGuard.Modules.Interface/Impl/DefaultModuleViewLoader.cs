namespace DeviceGuard.Modules.Interface.Impl;

/// <summary>
/// IModuleView 的默认实现
/// </summary>
public sealed class DefaultModuleViewLoader : IModuleViewLoader
{
    public string Name { get; }

    public ModuleViewCategory Category { get; }

    private readonly IRegionManager _regionManager;
    private readonly string         _navigationName;

    public DefaultModuleViewLoader(IRegionManager regionManager, ModuleViewCategory category, string name, string navigationName)
    {
        _regionManager  = regionManager ?? throw new ArgumentNullException(nameof(regionManager));
        _navigationName = navigationName ?? throw new ArgumentNullException(nameof(navigationName));
        Name            = name ?? throw new ArgumentNullException(nameof(name));
        Category        = category;
    }

    public void Show(string regionName)
    {
        _regionManager.RequestNavigate(regionName, _navigationName);
    }
}
