namespace DeviceGuard.Windows.Interface.Services
{
    /// <summary>
    /// 重启动软件服务
    /// </summary>
    public interface IAppRestartService
    {
        /// <summary>
        /// 重启动软件
        /// </summary>
        void Restart();

        /// <summary>
        /// 询问用户, 重启动软件
        /// </summary>
        /// <param name="question">重启动软件的提示信息,传递 null 则使用默认信息</param>
        Task AskUserRestartAsync(string? question = null);
    }
}
