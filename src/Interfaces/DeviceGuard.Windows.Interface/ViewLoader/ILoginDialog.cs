using DeviceGuard.Interface.Cloud;

namespace DeviceGuard.Windows.Interface.ViewLoader;

/// <summary>
/// 登录对话框接口
/// </summary>
public interface ILoginDialog
{
    /// <summary>
    /// 显示登录对话框
    /// </summary>
    /// <param name="cancellation"></param>
    /// <returns>返回用户是否登录成功</returns>
    Task<TokenResponse> ShowAsync(CancellationToken cancellation);

    // /// <summary>
    // /// 在需要(没有登录)的时候, 显示登录对话框
    // /// </summary>
    // /// <param name="cancellation"></param>
    // /// <returns></returns>
    // Task<bool> ShowIfNeededAsync(CancellationToken cancellation);
}
