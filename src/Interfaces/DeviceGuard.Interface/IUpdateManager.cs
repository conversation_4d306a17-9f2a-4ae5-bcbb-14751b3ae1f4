using System.Threading;
using System.Threading.Tasks;

namespace DeviceGuard.Interface;

/// <summary>
/// 这个接口负责检查是否有新版本的软件, 如果有, 下载并更新
/// 每一次下载完成, 都保存在本地, 等用户下一次启动的时候, 提示用户更新软件
/// </summary>
public interface IUpdateManager
{
    /// <summary>
    /// 下载更新
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task DownloadUpdatesAsync(CancellationToken cancellationToken);

    /// <summary>
    /// 获取等待安装的更新版本号
    /// </summary>
    /// <param name="cancellationToken">取消令牌，允许异步操作被取消</param>
    /// <returns>返回等待安装的版本号，如果没有等待更新的版本则返回 null</returns>
    Task<string?> GetUpdatePendingVersionAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 安装更新
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task InstallUpdatesAsync(CancellationToken cancellationToken);
}
