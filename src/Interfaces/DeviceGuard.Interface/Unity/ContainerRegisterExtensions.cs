using System;
using Unity;

namespace DeviceGuard.Interface.Unity;

public static class ContainerRegisterExtensions
{
    /// <summary>
    /// 增加核心组件方法
    /// </summary>
    /// <typeparam name="TComponent"></typeparam>
    /// <param name="container"></param>
    public static void RegisterComponent<TComponent>(this IUnityContainer container) where TComponent : IUnityComponent, new()
    {
        if (container == null)
            throw new ArgumentNullException(nameof(container));

        var corePart = new TComponent();
        corePart.RegisterTypes(container);
    }
}