namespace DeviceGuard.Interface.FileSystem;

/// <summary>
/// 文件夹测试
/// </summary>
public interface IFolderTester
{
    /// <summary>
    /// 文件夹是否可写入
    /// </summary>
    /// <param name="folderName"></param>
    /// <returns></returns>
    bool CanWrite(string folderName);

    /// <summary>
    /// 是否拥有足够的空间
    /// </summary>
    /// <param name="folderName">文件夹的名称</param>
    /// <param name="minimumSpaceRequired">最低空间检查</param>
    /// <returns></returns>
    bool HasEnoughSpace(string folderName, double minimumSpaceRequired);

    /// <summary>
    /// 文件名是否合法
    /// </summary>
    /// <param name="folderName"></param>
    /// <returns></returns>
    bool IsFolderNameLegal(string folderName);
}