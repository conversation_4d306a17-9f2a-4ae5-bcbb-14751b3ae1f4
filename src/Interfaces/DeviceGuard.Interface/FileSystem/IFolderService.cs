namespace DeviceGuard.Interface.FileSystem;

/// <summary>
/// 文件夹存储服务, 该接口负责记住用户使用的最后文件夹, 如果不存在则返回默认目录
/// </summary>
public interface IFolderService
{
    /// <summary>
    /// 获取文件夹
    /// </summary>
    /// <param name="appFolder">文件夹类型</param>
    /// <returns></returns>
    string GetFolderOrDefault(AppFolder appFolder);

    /// <summary>
    /// 保存用户最后访问的文件夹
    /// </summary>
    /// <param name="appFolder">文件夹类型</param>
    /// <param name="recentPath">路径</param>
    void SaveFolder(AppFolder appFolder, string recentPath);
}