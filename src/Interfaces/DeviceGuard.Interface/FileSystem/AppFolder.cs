using System;
using System.Diagnostics.CodeAnalysis;
using System.IO;
using System.Linq;
using System.Reflection;

namespace DeviceGuard.Interface.FileSystem;

/// <summary>
/// 预定义文件夹
/// 不要直接调用, 使用 IFolderService 调用
/// TODO 这个设计不好, 会有反向依赖
/// </summary>
public class AppFolder
{
    /// <summary>
    /// 保存设置参数
    /// </summary>
    public static readonly AppFolder Settings = new(nameof(Settings), @"System", false);

    /// <summary>
    /// 下载后缓存在本地的模块
    /// </summary>
    public static readonly AppFolder ModulePackages = new(nameof(ModulePackages), "Downloads/Modules", false);

    /// <summary>
    /// 下载后缓存在本地的插件
    /// </summary>
    public static readonly AppFolder PluginPackages = new(nameof(PluginPackages), "Downloads/Plugins", false);

    /// <summary>
    /// 模块
    /// </summary>
    public static readonly AppFolder Modules = new(nameof(Modules), "Modules", false);

    /// <summary>
    /// 保存模块, 固定保存在 Exe 文件路径下
    /// </summary>
    public static readonly string DebuggingModules = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "DebuggingModules");

    /// <summary>
    /// 保存插件
    /// </summary>
    public static readonly AppFolder Plugins = new(nameof(Plugins), @"Plugins", false);

    /// <summary>
    /// 保存插件
    /// </summary>
    public static readonly AppFolder PluginDb = new(nameof(PluginDb), @"PluginDb", false);

    /// <summary>
    /// 插件数据库索引文件
    /// </summary>
    public static readonly string PluginDbIndexFileName = "plugin-db.json";

    /// <summary>
    /// 保存日志
    /// </summary>
    public static readonly AppFolder Logs = new(nameof(Logs), @"Logs", false);

    /// <summary>
    /// 保存软件更新文件
    /// </summary>
    public static readonly AppFolder Updates = new(nameof(Updates), "Downloads/Updates", false);

    #region 静态方法

    /// <summary>
    /// 获取所有预定义的AppFolder
    /// </summary>
    /// <returns></returns>
    public static AppFolder[] GetAppFolders()
    {
        var fields = typeof(AppFolder).GetFields(BindingFlags.Public | BindingFlags.Static);

        var q = from f in fields
                where f.FieldType == typeof(AppFolder)
                let appFolder = f.GetValue(null) as AppFolder
                where appFolder != null
                select appFolder;
        return q.ToArray();
    }

    #endregion

    #region 对象属性

    /// <summary>
    /// 文件夹的KEY, 记录最近打开文件夹扮演KEY的作用
    /// </summary>
    public string Key { get; }

    /// <summary>
    /// 记住默认的文件夹, 不要直接调用, 使用 IFolderService 调用
    /// </summary>
    public string DefaultFolder { get; }

    /// <summary>
    /// 记住最后一个文件夹, 拥有KEY才能记住保存最后一个文件夹
    /// </summary>
    public bool RememberLatestFolder { get; }

    /// <summary>
    /// 提供记录更改的文件夹的KEY, 和默认的文件夹, 所有数据均保存在用户文件夹
    /// </summary>
    /// <param name="storeKey">KEY</param>
    /// <param name="defaultFolder">默认的文件夹</param>
    /// <param name="rememberLatestFolder">支持记住最后的文件夹</param>
    public AppFolder(string storeKey, string defaultFolder, bool rememberLatestFolder)
    {
        Key                  = storeKey;
        DefaultFolder        = defaultFolder;
        RememberLatestFolder = rememberLatestFolder;
    }

    #endregion
}
