namespace DeviceGuard.Interface.FileSystem;

/// <summary>
/// 应用程序各种默认目录的管理
/// </summary>
public interface IAppFolderService
{
    /// <summary>
    /// 获取默认文件夹
    /// </summary>
    /// <param name="appFolder">文件夹类型</param>
    /// <returns></returns>
    string GetOrCreateFolder(AppFolder appFolder);


    /// <summary>
    /// 准备默认的目录
    /// </summary>
    void PrepareDefaultFolders(AppFolder[] appFolders);
}