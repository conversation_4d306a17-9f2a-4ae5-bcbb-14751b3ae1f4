namespace DeviceGuard.Interface.FileSystem;

/// <summary>
/// 文件读写测试接口, 验证文件指定文件是否可写
/// </summary>
public interface IFileTester
{
    /// <summary>
    /// 文件可以写
    /// </summary>
    /// <param name="filePath"></param>
    /// <returns></returns>
    bool CanWrite(string filePath);

    /// <summary>
    /// 文件可以读
    /// </summary>
    /// <param name="filePath"></param>
    /// <returns></returns>
    bool CanRead(string filePath);

    /// <summary>
    /// 文件名是否合法
    /// </summary>
    /// <param name="filePath"></param>
    /// <returns></returns>
    bool IsFileNameLegal(string filePath);
}