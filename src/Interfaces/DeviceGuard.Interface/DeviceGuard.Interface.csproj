<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
    <Platforms>AnyCPU;x64</Platforms>
  </PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
		<DebugType>none</DebugType>
		<DebugSymbols>false</DebugSymbols>
	</PropertyGroup>
	<PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
	  <DebugType>none</DebugType>
	  <DebugSymbols>false</DebugSymbols>
	</PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Prism.Core" Version="9.0.537" />
    <PackageReference Include="Unity.Abstractions" Version="5.11.7" />
  </ItemGroup>

</Project>
