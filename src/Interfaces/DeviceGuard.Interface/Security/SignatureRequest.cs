using System;
using Newtonsoft.Json.Linq;

namespace DeviceGuard.Interface.Security;

/// <summary>
/// 用户拥有的插件响应模型
/// </summary>
public class SignatureRequest
{
    /// <summary>
    /// 响应数据
    /// </summary>
    public JObject? Data { get; set; } = new();

    /// <summary>
    /// 数字签名
    /// </summary>
    public string Signature { get; set; } = "";

    /// <summary>
    /// 签名证书公钥
    /// </summary>
    public string SignCertificatePublicKey { get; set; } = "";

    /// <summary>
    /// 签名算法
    /// </summary>
    public string Algorithm { get; set; } = "";

    /// <summary>
    /// 随机数
    /// </summary>
    public string Nonce { get; set; } = "";

    /// <summary>
    /// 签名时间戳
    /// </summary>
    public DateTime SignatureTimestamp { get; set; }
}
