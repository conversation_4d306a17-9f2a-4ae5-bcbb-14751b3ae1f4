using System.Security.Cryptography.X509Certificates;
using System.Threading;
using System.Threading.Tasks;

namespace DeviceGuard.Interface.Security;

/// <summary>
/// 证书验证接口
/// </summary>
public interface ICertificateValidator
{
    /// <summary>
    /// 验证指定的证书是否由CFTech根证书颁发
    /// </summary>
    /// <param name="certificate">要验证的证书</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<bool> ValidateCertificateAsync(X509Certificate2 certificate, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证指定的证书是否由CFTech根证书颁发
    /// </summary>
    /// <param name="certificatePem">PEM格式的证书字符串</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>验证结果</returns>
    Task<bool> ValidateCertificateAsync(string certificatePem, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取CFTech根证书信息
    /// </summary>
    /// <returns>根证书信息</returns>
    CertificateInfo GetRootCertificateInfo();

    /// <summary>
    /// 验证证书链
    /// </summary>
    /// <param name="certificate">要验证的证书</param>
    /// <param name="intermediateCertificates">中间证书（可选）</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>证书验证结果</returns>
    Task<CertificateValidationResult> ValidateCertificateChainAsync(
        X509Certificate2    certificate,
        X509Certificate2[]? intermediateCertificates = null,
        CancellationToken   cancellationToken        = default);
}
