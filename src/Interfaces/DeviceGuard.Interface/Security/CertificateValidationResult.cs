using System;

namespace DeviceGuard.Interface.Security;

/// <summary>
/// 证书验证结果
/// </summary>
public class CertificateValidationResult
{
    /// <summary>
    /// 验证是否成功
    /// </summary>
    public bool IsValid { get; set; }

    /// <summary>
    /// 错误消息列表
    /// </summary>
    public string[] ErrorMessages { get; set; } = Array.Empty<string>();

    /// <summary>
    /// 创建成功的验证结果
    /// </summary>
    /// <returns>成功的验证结果</returns>
    public static CertificateValidationResult Success()
    {
        return new CertificateValidationResult
        {
            IsValid = true,
            ErrorMessages = Array.Empty<string>()
        };
    }

    /// <summary>
    /// 创建失败的验证结果
    /// </summary>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>失败的验证结果</returns>
    public static CertificateValidationResult Failure(string errorMessage)
    {
        return new CertificateValidationResult
        {
            IsValid = false,
            ErrorMessages = new[] { errorMessage }
        };
    }

    /// <summary>
    /// 创建失败的验证结果
    /// </summary>
    /// <param name="errorMessages">错误消息列表</param>
    /// <returns>失败的验证结果</returns>
    public static CertificateValidationResult Failure(string[] errorMessages)
    {
        return new CertificateValidationResult
        {
            IsValid = false,
            ErrorMessages = errorMessages
        };
    }
}
