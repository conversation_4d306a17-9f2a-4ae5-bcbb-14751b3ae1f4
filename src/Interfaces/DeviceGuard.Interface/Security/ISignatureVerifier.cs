using System.Threading;
using System.Threading.Tasks;

namespace DeviceGuard.Interface.Security;

/// <summary>
/// 证书验证接口，提供证书链验证功能
/// </summary>
public interface ISignatureVerifier
{
    /// <summary>
    /// 验证签名请求的正确性
    /// </summary>
    /// <remarks>
    /// 验证
    /// 1. request.SignCertificatePublicKey 是否为系统保存的 CFTech 的 root 证书颁发
    /// 2. 使用 request.SignCertificatePublicKey 验证 request.Signature 是否正确, 签名由 Data, Nonce 组成
    /// </remarks>
    /// <param name="request"></param>
    /// <param name="cancellation"></param>
    /// <returns></returns>
    Task<bool> ValidateAsync(SignatureRequest request, CancellationToken cancellation);
}
