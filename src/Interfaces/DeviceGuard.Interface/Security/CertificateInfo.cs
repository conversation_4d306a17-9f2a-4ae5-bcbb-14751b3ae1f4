using System;

namespace DeviceGuard.Interface.Security;

/// <summary>
/// 证书信息
/// </summary>
public class CertificateInfo
{
    /// <summary>
    /// 证书公钥（PEM格式）
    /// </summary>
    public string PublicKey { get; set; } = "";

    /// <summary>
    /// 证书颁发者
    /// </summary>
    public string Issuer { get; set; } = "";

    /// <summary>
    /// 证书主题
    /// </summary>
    public string Subject { get; set; } = "";

    /// <summary>
    /// 证书生效时间
    /// </summary>
    public DateTime ValidFrom { get; set; }

    /// <summary>
    /// 证书过期时间
    /// </summary>
    public DateTime ValidTo { get; set; }

    /// <summary>
    /// 证书指纹
    /// </summary>
    public string Fingerprint { get; set; } = "";

    /// <summary>
    /// 证书序列号
    /// </summary>
    public string SerialNumber { get; set; } = "";

    /// <summary>
    /// 证书类型
    /// </summary>
    public string CertificateType { get; set; } = "";
}
