using System.Threading;
using System.Threading.Tasks;

namespace DeviceGuard.Interface.Cloud;

/// <summary>
/// 许可证客户端
/// </summary>
public interface ILicenseClient
{
    /// <summary>
    /// 激活 License
    /// </summary>
    /// <param name="sku"></param>
    /// <param name="serialNumber"></param>
    /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
    /// <returns>Success</returns>
    Task<string> ActivateLicenseAsync(string sku, string serialNumber, CancellationToken cancellationToken);
}