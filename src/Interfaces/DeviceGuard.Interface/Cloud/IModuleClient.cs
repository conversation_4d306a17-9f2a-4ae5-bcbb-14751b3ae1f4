using System.Threading;
using System.Threading.Tasks;

namespace DeviceGuard.Interface.Cloud;

/// <summary>
/// Represents an interface for a module client that provides functionality to download modules.
/// </summary>
public interface IModuleClient
{
    /// <summary>
    /// 下载模块
    /// </summary>
    /// <param name="moduleCode">模块编码</param>
    /// <param name="version">版本号（最新版本=latest）</param>
    /// <param name="cancellation"></param>
    /// <returns>下载结果</returns>
    Task<byte[]> DownloadAsync(string moduleCode, string version, CancellationToken cancellation);
}