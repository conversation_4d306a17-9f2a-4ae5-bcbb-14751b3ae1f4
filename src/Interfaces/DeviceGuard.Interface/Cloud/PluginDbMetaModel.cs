namespace DeviceGuard.Interface.Cloud;

/// <summary>
/// 插件数据库元模型，用于存储插件文件的元数据信息
/// </summary>
public class PluginDbMetaModel
{
    /// <summary>
    /// 文件名称
    /// </summary>
    public string FileName { get; set; } = "";

    /// <summary>
    /// 文件大小（单位：字节）
    /// </summary>
    public uint Size { get; set; } = 0;

    /// <summary>
    /// 文件MD5校验值
    /// </summary>
    public string Md5 { get; set; } = "";

    /// <summary>
    /// 文件SHA1校验值
    /// </summary>
    public string Sha1 { get; set; } = "";
}
