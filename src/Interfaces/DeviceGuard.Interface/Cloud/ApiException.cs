using System;

namespace DeviceGuard.Interface.Cloud;

/// <summary>
/// API异常类
/// </summary>
public class ApiException : Exception
{
    public string? ErrorCode  { get; set; }
    public string? RequestId  { get; set; }
    public int     StatusCode { get; set; }

    public ApiException(string message) : base(message)
    {
    }

    public ApiException(string message, Exception innerException) : base(message, innerException)
    {
    }
}
