using System.Threading;
using System.Threading.Tasks;

namespace DeviceGuard.Interface.Cloud;

/// <summary>
/// Interface for interacting with the cloud to download plugins.
/// </summary>
public interface IPluginClient
{
    /// <summary>
    /// Downloads a plugin binary for the specified plugin ID and version.
    /// </summary>
    /// <param name="pluginId">The unique identifier of the plugin to download.</param>
    /// <param name="version">The version of the plugin to download.</param>
    /// <param name="cancellationToken">A token to monitor for cancellation requests.</param>
    /// <returns>A task that represents the asynchronous operation. The task result contains the binary data of the plugin.</returns>
    Task<byte[]> DownloadPluginAsync(string pluginId, string version, CancellationToken cancellationToken);
}
