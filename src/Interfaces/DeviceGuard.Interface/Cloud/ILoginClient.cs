using System.Threading;
using System.Threading.Tasks;

namespace DeviceGuard.Interface.Cloud;

/// <summary>
/// 登录客户端, 提供给系统所有组件, 用来查询登录情况, 并执行登录, 退出登录等动作
/// </summary>
public interface ILoginClient
{
    /// <summary>
    /// 使用用户名和密码在云端登录
    /// </summary>
    /// <param name="userName"></param>
    /// <param name="password">密码在内存中不加密处理, 但是不得持久化密码, 不得明文网络传输密码</param>
    /// <returns></returns>
    Task<TokenResponse> LoginAsync(string userName, string password);

    /// <summary>
    /// 刷新访问令牌, 使用现有的刷新令牌来获取新的访问令牌和刷新令牌
    /// </summary>
    /// <param name="refreshToken">用于刷新令牌的字符串, 必须在现有刷新令牌过期之前调用</param>
    /// <param name="cancellation"></param>
    /// <returns>返回包含新令牌信息的 <see cref="TokenResponse"/> 对象</returns>
    Task<TokenResponse> RefreshTokenAsync(string refreshToken, CancellationToken cancellation);
}
