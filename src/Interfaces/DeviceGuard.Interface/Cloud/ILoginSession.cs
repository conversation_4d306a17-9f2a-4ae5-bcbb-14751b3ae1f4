using System.Threading;
using System.Threading.Tasks;

namespace DeviceGuard.Interface.Cloud;

/// <summary>
/// 登录客户端, 提供给系统所有组件, 用来查询登录情况, 并执行登录, 退出登录等动作
/// </summary>
public interface ILoginSession
{
    /// <summary>
    /// 执行用户登录操作
    /// </summary>
    /// <param name="cancellation"></param>
    /// <returns></returns>
    Task LoginAsync(CancellationToken cancellation);

    /// <summary>
    /// 注销当前的登录
    /// </summary>
    /// <param name="cancellation"></param>
    /// <returns></returns>
    Task LogoutAsync(CancellationToken cancellation);

    /// <summary>
    /// 获取当前登录用户的基本信息
    /// </summary>
    /// <param name="cancellation"></param>
    /// <returns></returns>
    Task<LoginUserInfo?> GetLoginUserAsync(CancellationToken cancellation);

    /// <summary>
    /// 指出用户是否已经登录
    /// </summary>
    Task<bool> IsLoggedInAsync(CancellationToken cancellation);

    /// <summary>
    /// 获取 access_token, 如果这个token过期了, 则使用 refresh_token换一个, 如果 refresh_token 过期了, 则登录
    /// </summary>
    /// <param name="cancellation"></param>
    /// <returns></returns>
    Task<string> GetAccessTokenAsync(CancellationToken cancellation);

    /// <summary>
    /// 确保已经登录
    /// </summary>
    /// <param name="cancellationToken"></param>
    /// <returns></returns>
    Task EnsureLoginAsync(CancellationToken cancellationToken);
}
