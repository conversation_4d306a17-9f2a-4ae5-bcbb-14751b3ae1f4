using System.Threading;
using System.Threading.Tasks;

namespace DeviceGuard.Interface.Cloud;

/// <summary>
/// 插件分类API客户端
/// </summary>
public interface IPluginDbClient
{
    /// <summary>
    /// 获取插件分类树
    /// </summary>
    /// <param name="host">平台类型（可选）</param>
    /// <param name="cancellationToken"></param>
    /// <returns>返回提供给客户端使用的插件分类树，包含所有支持的产品</returns>
    Task<TreeNode> GetPluginTreeAsync(PluginHost host, CancellationToken cancellationToken);

    /// <summary>
    /// 获取插件数据库的元数据信息
    /// </summary>
    /// <param name="host"></param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>返回包含插件数据库元数据的PluginDbMetaModel对象</returns>
    Task<PluginDbMetaModel> GetPluginDbMetaAsync(PluginHost host, CancellationToken cancellationToken);

    /// <summary>
    /// 下载插件数据库
    /// </summary>
    /// <param name="host">平台类型</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>返回插件数据库的字节数组</returns>
    Task<byte[]> DownloadPluginDbAsync(PluginHost host, CancellationToken cancellationToken);
}
