using System;
using Newtonsoft.Json;

namespace DeviceGuard.Interface.Cloud;

/// <summary>
/// Represents the response received after a token request, including access and refresh tokens,
/// their expiration durations, and associated metadata.
/// </summary>
public class TokenResponse
{
    /// <summary>
    /// The access token used for authenticating API requests.
    /// This token is typically acquired during login or token refresh operations and is time-sensitive.
    /// </summary>
    [JsonProperty("access_token")] public string AccessToken { get; set; } = "";

    /// <summary>
    /// The token used to refresh the access token after it has expired.
    /// </summary>
    [JsonProperty("refresh_token")]
    public string RefreshToken { get; set; } = "";

    /// <summary>
    /// 表示访问令牌的有效期，单位为秒。
    /// </summary>
    [JsonProperty("expires_in")]
    public int ExpiresIn { get; set; }

    /// <summary>
    /// Determines whether the access token is expired by checking if the current time has surpassed
    /// the token's expiration time. The expiration time is calculated based on the token acquisition time
    /// and its lifespan in seconds.
    /// </summary>
    /// <returns>
    /// True if the access token has expired; otherwise, false.
    /// </returns>
    public bool AccessTokenExpired() => AcquiredAt.AddSeconds(ExpiresIn) < DateTime.Now;

    /// <summary>
    /// The duration in seconds after which the refresh token expires.
    /// </summary>
    [JsonProperty("refresh_expires_in")]
    public int RefreshExpiresIn { get; set; }

    /// <summary>
    /// Determines whether the refresh token has expired based on the time it was acquired and its expiration duration.
    /// </summary>
    /// <returns>
    /// True if the refresh token has expired; otherwise, false.
    /// </returns>
    public bool RefreshTokenExpired() => AcquiredAt.AddSeconds(RefreshExpiresIn) < DateTime.Now;

    /// <summary>
    /// Indicates the type of token issued, defining its usage or characteristics.
    /// </summary>
    [JsonProperty("token_type")]
    public string TokenType { get; set; } = "";

    /// <summary>
    /// 表示分配给 TOKEN 的权限范围。
    /// </summary>
    [JsonProperty("scope")]
    public string Scope { get; set; } = "";

    /// <summary>
    /// The date and time when the token was acquired.
    /// </summary>
    public DateTime AcquiredAt { get; set; } = DateTime.Now;
}
