using System.Threading;
using System.Threading.Tasks;

namespace DeviceGuard.Interface.Cloud;

public interface IUserPluginClient
{
    /// <summary>
    /// Downloads plugins that the user owns associated with the specified machine.
    /// </summary>
    /// <param name="host">Identifies the platform type where the plugins will be applied.</param>
    /// <param name="serialNumber">The serial number of the machine to identify ownership of the plugins.</param>
    /// <param name="cancellationToken">A cancellation token that can be used by other objects or threads to receive notice of cancellation.</param>
    /// <returns>A task that represents the asynchronous operation, containing the downloaded JSON string content.</returns>
    Task<string> DownloadMyOwnedPluginsAsync(PluginHost host, string serialNumber, CancellationToken cancellationToken);
}
