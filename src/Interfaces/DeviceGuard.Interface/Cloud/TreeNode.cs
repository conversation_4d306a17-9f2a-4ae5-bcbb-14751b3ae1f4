using System.Collections.Generic;

namespace DeviceGuard.Interface.Cloud
{
    /// <summary>
    /// 树节点模型
    /// </summary>
    public class TreeNode
    {
        public string?       Id    { get; set; }
        public string?       Name  { get; set; }
        public TreeNodeType? Type  { get; set; }
        public string[]?     Tags  { get; set; }
        public string?       Brief { get; set; }
        public object?       Badge { get; set; }
    }
}

// 扩展：如果需要更方便的使用，可以添加扩展方法
