using System.Threading;
using System.Threading.Tasks;

namespace DeviceGuard.Interface.Cloud;

/// <summary>
/// Represents a client interface for managing and retrieving software artifacts.
/// </summary>
public interface IArtifactClient
{
    /// <summary>
    /// Retrieves the latest version string of the specified software.
    /// </summary>
    /// <param name="softwareCode">
    /// A unique identifier for the software whose latest version is being retrieved.
    /// </param>
    /// <param name="cancellationToken">
    /// A token to monitor for cancellation requests.
    /// </param>
    /// <returns>
    /// A task representing the asynchronous operation. The task result contains the latest version string of the specified software.
    /// </returns>
    Task<string> GetLatestSoftwareVersionAsync(string softwareCode, CancellationToken cancellationToken);

    /// <summary>
    /// Downloads the specified version of the software for the given software code asynchronously.
    /// </summary>
    /// <param name="softwareCode">The code representing the software to be downloaded.</param>
    /// <param name="version">The specific version of the software to download.</param>
    /// <param name="cancellationToken">A token to monitor for cancellation requests.</param>
    /// <returns>A byte array containing the downloaded software data.</returns>
    Task<byte[]> DownloadSoftwareAsync(string softwareCode, string version, CancellationToken cancellationToken);
}
