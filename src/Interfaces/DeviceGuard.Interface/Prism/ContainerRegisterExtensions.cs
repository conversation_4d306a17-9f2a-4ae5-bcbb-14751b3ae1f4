using System;
using Prism.Ioc;

namespace DeviceGuard.Interface.Prism;

public static class ContainerRegisterExtensions
{
    /// <summary>
    /// 增加核心组件方法
    /// </summary>
    /// <typeparam name="TPrismComponent"></typeparam>
    /// <param name="containerRegistry"></param>
    public static void RegisterComponent<TPrismComponent>(this IContainerRegistry containerRegistry) where TPrismComponent : IPrismComponent, new()
    {
        if (containerRegistry == null)
            throw new ArgumentNullException(nameof(containerRegistry));

        var corePart = new TPrismComponent();
        corePart.RegisterTypes(containerRegistry);
    }
}