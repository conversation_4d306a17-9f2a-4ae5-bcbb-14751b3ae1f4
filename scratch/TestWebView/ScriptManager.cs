using System.Runtime.InteropServices;
using System.Windows;

namespace TestWebView;

[ClassInterface(ClassInterfaceType.AutoDual)]
[ComVisible(true)]
public class ScriptManager
{
    // 示例方法：弹出消息框，实际应用中可调用串口或其它 API
    public void ShowMessage(string message)
    {
        MessageBox.Show(message, "WPF Dialog");
    }

    // 这里可以添加更多方法，例如打开串口、发送数据等
    public string OpenSerialPort(string portName, int baudRate)
    {
        // 这里可以调用 System.IO.Ports.SerialPort 等 API 来操作串口
        // 仅做示例，实际实现中需要捕获异常和管理资源
        // SerialPort sp = new SerialPort(portName, baudRate);
        // sp.Open();
        // return "串口已打开";
        return $"打开串口 {portName}，波特率 {baudRate}";
    }
}
