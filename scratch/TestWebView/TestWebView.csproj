<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>WinExe</OutputType>
        <TargetFramework>net8.0-windows</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <UseWPF>true</UseWPF>
        <Platforms>AnyCPU;x64</Platforms>
        <UseCurrentRuntimeIdentifier Condition="'$(UseCurrentRuntimeIdentifier)' == ''">true</UseCurrentRuntimeIdentifier>
    </PropertyGroup>

    <ItemGroup>
      <None Remove="Html\index.html" />
      <Content Include="Html\index.html">
        <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      </Content>
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="CefSharp.Wpf.NETCore" Version="135.0.220" />
      <PackageReference Include="Microsoft.Web.WebView2" Version="1.0.3124.44" />
      <PackageReference Include="WebView2.Runtime.X64" Version="132.0.2957.140" />
    </ItemGroup>

</Project>
