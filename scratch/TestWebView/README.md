# Summary

在 WPF 项目中，使用 WebView2 控件成功承载 HTML 内容，并实现 JavaScript 调用 WPF 的 API。


## 重要提示

发现微软的 WebView2 控件有BUG, 他会永远停留在顶层, 导致遮盖可能错误的情况。

目前测试: 
- https://www.cnblogs.com/ives/p/17719809.html 的方案无法解决
- CefSharp 对 .net 8 兼容可能有问题



**1. 引入 WebView2 控件**

首先，确保项目中已安装 `Microsoft.Web.WebView2` NuGet 包。

在 XAML 文件中，添加 WebView2 控件：

```xml
<Window x:Class="YourNamespace.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:wv2="clr-namespace:Microsoft.Web.WebView2.Wpf;assembly=Microsoft.Web.WebView2.Wpf"
        Title="MainWindow" Height="450" Width="800">
    <Grid>
        <wv2:WebView2 x:Name="webView" />
    </Grid>
</Window>

```

**2. 在代码中初始化 WebView2**

在窗口的代码隐藏文件中，初始化 WebView2 控件并加载本地 HTML 文件：

```csharp
using System;
using System.IO;
using System.Windows;

namespace ChargerGuard.Win
{
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
            InitializeWebView();
        }

        private async void InitializeWebView()
        {
            await webView.EnsureCoreWebView2Async(null);
            string htmlFilePath = Path.Combine(Environment.CurrentDirectory, "Html", "index.html");
            webView.Source = new Uri($"file:///{htmlFilePath}");
        }
    }
}

```

**3. 定义可供 JavaScript 调用的 .NET 类**

创建一个带有公开方法的 C# 类，并确保该类对 COM 可见，以便 JavaScript 可以通过该类调用 WPF 的方法：

```csharp
using System.Runtime.InteropServices;
using System.Windows;

[ClassInterface(ClassInterfaceType.AutoDual)]
[ComVisible(true)]
public class ScriptManager
{
    public void ShowMessage(string message)
    {
        MessageBox.Show(message, "来自 JavaScript 的消息");
    }
}

```

**4. 在 WebView2 中注册该对象**

在初始化 WebView2 控件时，将上述对象添加为主机对象，使其可供 JavaScript 调用：

```csharp
using Microsoft.Web.WebView2.Core;
using System.Windows;

public partial class MainWindow : Window
{
    public MainWindow()
    {
        InitializeComponent();
        InitializeWebView();
    }

    private async void InitializeWebView()
    {
        await webView.EnsureCoreWebView2Async(null);
        webView.CoreWebView2.AddHostObjectToScript("scriptManager", new ScriptManager());
        string htmlFilePath = Path.Combine(Environment.CurrentDirectory, "Html", "index.html");
        webView.Source = new Uri($"file:///{htmlFilePath}");
    }
}

```

**5. 在 JavaScript 中调用 .NET 方法**

在加载的网页中，JavaScript 可以通过先前注册的对象来调用 .NET 方法：

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <title>WebView2 与 WPF 交互示例</title>
    <script type="text/javascript">
        function callWpfMethod() {
            window.chrome.webview.hostObjects.scriptManager.ShowMessage("Hello from JavaScript");
        }
    </script>
</head>
<body>
    <h1>WebView2 与 WPF 交互示例</h1>
    <button onclick="callWpfMethod()">调用 WPF 方法</button>
</body>
</html>

```
