<Window x:Class="TestWebView.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:wv2="clr-namespace:Microsoft.Web.WebView2.Wpf;assembly=Microsoft.Web.WebView2.Wpf"
        xmlns:local="clr-namespace:TestWebView"
        xmlns:wpf="clr-namespace:CefSharp.Wpf;assembly=CefSharp.Wpf"
        mc:Ignorable="d"
        Title="MainWindow" Height="450" Width="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
        </Grid.RowDefinitions>
        <StackPanel Orientation="Horizontal" Margin="20">
            <Button Content="Test" Click="Button_Click"></Button>
        </StackPanel>
        <!-- <WebBrowser Grid.Row="1" x:Name="WebBrowser"></WebBrowser> -->
        <!-- <wv2:WebView2 Grid.Row="1" x:Name="webView" Source="https://www.bing.com" /> -->
        <wpf:ChromiumWebBrowser Grid.Row="1" Name="browser" Address="https://www.qq.com" />


    </Grid>
</Window>