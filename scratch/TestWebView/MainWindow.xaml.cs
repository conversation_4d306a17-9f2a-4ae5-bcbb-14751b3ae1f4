using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace TestWebView;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    public MainWindow()
    {
        InitializeComponent();
        //
        // // 将 ScriptManager 对象注入给 JavaScript
        // WebBrowser.ObjectForScripting = new ScriptManager();
        //
        // // 加载 HTML 页面（可以是本地文件或字符串）
        // // WebBrowser.Navigate(new Uri("pack://application:,,,/Html/index.html"));
        // WebBrowser.Navigate(new Uri("pack://siteoforigin:,,,/Html/index.html"));

        InitializeWebView();
    }

    private void InitializeWebView()
    {
        // await webView.EnsureCoreWebView2Async(null);
        // webView.CoreWebView2.AddHostObjectToScript("scriptManager", new ScriptManager());
        // string htmlFilePath = System.IO.Path.Combine(Environment.CurrentDirectory, "Html", "index.html");
        // webView.Source = new Uri($"file:///{htmlFilePath}");
    }

    private void Button_Click(object sender, RoutedEventArgs e)
    {
        // WebBrowser.InvokeScript("showAlarm", new object[] { "Hello from C#" });
    }
}
