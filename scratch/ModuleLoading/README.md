# Summary

这个目录的项目用来测试动态加载不同版本的多个模块. 测试一个模块多版本共存情况下软件的实现方式.

# WPF 加载和卸载的结论

从 AssemblyLoadContext 的 LoadFromAssemblyPath 加载的 Assembly 如何使用? 我发现从这个加载的 Assembly 中查找类, 然后 Activator.CreateInstance(type) 的方式 或者 Assembly.CreateInstance("typeNameInPlugin") 的方式创建对象的时候, 我的主 AppDomain 的 Modules 立即就会把这个插件的相关 DLL 加载了, 导致后期就算卸载了 AssemblyLoadContext , 但是因为插件的 DLL 被加载到主 AppDomain, 而无法彻底卸载插件.

经测试, 创建了 UserControl后, AssemblyLoadContext 的 Unload 无法彻底的从内存中移除模块DLL


## References
https://github.com/dotnet/runtime/issues/13226  
It is 2022 and I presume this problem still has no solution to it. I have a WPF app, and I too cannot unload the plugin if it creates a WPF window. I don't care about unloading the actual WPF-related dependency assemblies that get loaded, since like I said my base app is WPF anyways. I just need a way to unload the actual plugin with the WPF window in it.

Is there currently no way in .NET to create an application that has GUI plugins and unload them?


https://github.com/dotnet/wpf/issues/6247

找到核心原因:
WPF 的用户控件一旦创建, 微软底层会缓存控件上的 DependencyProperty, 并且会缓存控件的 DependencyProperty 的值.
这些缓存是全局变量, 而且不会释放. 因此加载的控件, 无法卸载

![img.png](img.png)

## 版本共存

加载不同版本的同一个控件, 或者不同版本的依赖包, 如何共存

使用这种方式加载的控件, 无法支持多版本共存
```csharp
string pluginPath = @".\v1.3\MyModule.dll";
pluginPath = Path.GetFullPath(pluginPath);
var control = GetMyControl(pluginPath);
regionManager.Regions["Content"].Add(control);
```

我们将得到异常
```csharp
System.IO.FileLoadException: Assembly with same name is already loaded
   at System.Runtime.Loader.AssemblyLoadContext.LoadFromAssemblyPath(String assemblyPath)
   at System.Reflection.Assembly.LoadFrom(String assemblyFile)
   at MainProgram.MainWindowViewModel.GetMyControl(String dllPath) in D:\Project\WhatIsName\device-guard-win\scratch\ModuleLoading\MainProgram\MainWindowViewModel.cs:line 16
   at MainProgram.MainWindowViewModel.<>c__DisplayClass4_0.<.ctor>b__1() in D:\Project\WhatIsName\device-guard-win\scratch\ModuleLoading\MainProgram\MainWindowViewModel.cs:line 44
   at Prism.Commands.DelegateCommand.Execute()
```

使用这个方式加载可以实现多版本共存
```csharp
var context = new PluginLoadContext(@".\v1.2");
var control = context.GetMyControl();
regionManager.Regions["Content"].Add(control);
```