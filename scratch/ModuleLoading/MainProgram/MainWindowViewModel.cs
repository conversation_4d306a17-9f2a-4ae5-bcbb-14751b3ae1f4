using System.Reflection;
using System.Windows.Controls;

namespace MainProgram;

public class MainWindowViewModel
{
    UserControl GetMyControl(string dllPath)
    {
        var  assembly = Assembly.LoadFrom(dllPath);
        Type type     = assembly.GetType("MyModule.MyControl")!;
        var  instance = Activator.CreateInstance(type)!;

        return (UserControl)instance;
    }

    private PluginLoadContext? _alc;

    public MainWindowViewModel(IRegionManager regionManager)
    {
        ShowCommand = new DelegateCommand(() =>
        {
            var context = new PluginLoadContext(@".\v1.2");
            var control = context.GetMyControl();
            regionManager.Regions["Content"].Add(control);
        });
        ShowLocalCommand = new DelegateCommand(() =>
        {
            var context = new PluginLoadContext(@".\v1.3");
            var control = context.GetMyControl();
            regionManager.Regions["Content"].Add(control);
        });
        HideCommand = new DelegateCommand(() =>
        {
            // regionManager.RequestNavigate("Content", nameof(TestInMain));
            regionManager.Regions["Content"].RemoveAll();
            return;
        });
        UnloadCommand = new DelegateCommand(() =>
        {
            regionManager.Regions["Content"].RemoveAll();
            _alc?.Unload();

            WeakReference wr = new WeakReference(_alc);
            _alc = null;

            while (wr.IsAlive)
            {
                GC.Collect();
                GC.WaitForPendingFinalizers();
                Thread.Sleep(100);
            }
        });
    }

    public DelegateCommand ShowCommand      { get; set; }
    public DelegateCommand ShowLocalCommand { get; set; }
    public DelegateCommand HideCommand      { get; set; }

    public DelegateCommand UnloadCommand { get; set; }
}
