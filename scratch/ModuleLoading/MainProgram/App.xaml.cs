using System.Globalization;
using System.Reflection;
using System.Windows;

namespace MainProgram;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App
{
    // public PluginLoadContext? PLC1 { get; set; }
    //
    // public PluginLoadContext? PLC2 { get; set; }

    protected override IContainerExtension CreateContainerExtension()
    {
        // add resolve diagnostic information when error
        return new UnityContainerExtension(new UnityContainer().AddExtension(new Diagnostic()));
    }

    /// <summary>
    /// 注册启动必要的模块
    /// </summary>
    /// <param name="containerRegistry"></param>
    protected override void RegisterTypes(IContainerRegistry containerRegistry)
    {
        // Main window
        containerRegistry.GetContainer().RegisterSingleton<MainWindowViewModel>(); // serilog output
        containerRegistry.RegisterForNavigation<TestInMain>(nameof(TestInMain));

        // PLC1 = new PluginLoadContext(@".\v1.0");
        // PLC1.Register(containerRegistry);

        // PLC2 = new PluginLoadContext(@".\v1.2");
        // PLC2.Register(containerRegistry);

        // Plugins
    }

    /// <summary>
    /// 创建 Shell
    /// </summary>
    /// <returns></returns>
    protected override Window CreateShell()
    {
        // 首次在UI解析 EventAggregator
        var view = Container.Resolve<MainWindow>();
        return view;
    }

    protected override void ConfigureViewModelLocator()
    {
        base.ConfigureViewModelLocator();

        // todo 有时间重写 Prism 的方法, 静态方法无法 override 导致每次窗口都要手动 BindingLoadedUnloaded
        ViewModelLocationProvider.SetDefaultViewTypeToViewModelTypeResolver(viewType =>
        {
            var viewName = viewType.FullName;
            if (viewName == null)
                throw new ArgumentNullException(nameof(viewName));

            // same folder but end with ViewModel
            var suffix        = viewName.EndsWith("View") ? "Model" : "ViewModel";
            var viewModelName = string.Format(CultureInfo.InvariantCulture, "{0}{1}", viewName, suffix);

            var assembly = viewType.GetTypeInfo().Assembly;
            var type     = assembly.GetType(viewModelName, true);

            return type;
        });
    }
}
