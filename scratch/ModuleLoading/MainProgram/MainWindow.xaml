<Window x:Class="MainProgram.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:local="clr-namespace:MainProgram"
        xmlns:regions="http://prismlibrary.com/"
        mc:Ignorable="d"
        d:DataContext="{d:DesignInstance local:MainWindowViewModel, IsDesignTimeCreatable=True}"
        Title="MainWindow" Height="450" Width="800">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
        </Grid.RowDefinitions>
        <StackPanel Grid.Row="0" Orientation="Horizontal">
            <Button Content="Show" Command="{Binding ShowCommand}"></Button>
            <Button Content="Show from local" Command="{Binding ShowLocalCommand}"></Button>
            <Button Content="Hide" Command="{Binding HideCommand}"></Button>
            <Button Content="Unload" Command="{Binding UnloadCommand}"></Button>
        </StackPanel>
        <ContentControl Grid.Row="1" Margin="0 10 0 0" regions:RegionManager.RegionName="Content" />
    </Grid>
</Window>