<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net8.0-windows</TargetFramework>
        <UseWPF>true</UseWPF>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <Platforms>AnyCPU;x64</Platforms>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Prism.Unity" Version="9.0.537" />
        <PackageReference Include="Prism.Wpf" Version="9.0.537" />
    </ItemGroup>

    <ItemGroup>
        <None Update="v1.0\Framework.dll">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="v1.0\Framework.pdb">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="v1.0\MyLibrary.dll">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="v1.0\MyLibrary.pdb">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="v1.0\MyModule.dll">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="v1.0\MyModule.pdb">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="v1.0\MyModule.deps.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
        <None Update="v1.2\Framework.dll">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="v1.2\Framework.pdb">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="v1.2\MyLibrary.dll">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="v1.2\MyLibrary.pdb">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="v1.2\MyModule.dll">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="v1.2\MyModule.pdb">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="v1.2\MyModule.deps.json">
            <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="v1.2\MainProgram.pdb">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="v1.2\MainProgram.deps.json">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="v1.2\MainProgram.runtimeconfig.json">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="v1.3\Framework.dll">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="v1.3\Framework.pdb">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="v1.3\MyLibrary.dll">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="v1.3\MyLibrary.pdb">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="v1.3\MyModule.dll">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="v1.3\MyModule.pdb">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
        <None Update="v1.3\MyModule.deps.json">
          <CopyToOutputDirectory>Always</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\Framework\Framework.csproj" />
    </ItemGroup>

</Project>
