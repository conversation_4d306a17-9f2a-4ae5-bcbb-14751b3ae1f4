using System.IO;
using System.Reflection;
using System.Runtime.Loader;
using System.Windows.Controls;

namespace MainProgram;

public class PluginLoadContext : AssemblyLoadContext
{
    private readonly string _pluginPath;

    public PluginLoadContext(string pluginPath) : base(isCollectible: true) // 可回收
    {
        this._pluginPath = pluginPath;
    }

    protected override Assembly? Load(AssemblyName assemblyName)
    {
        // return base.Load(assemblyName);
        string assemblyPath = System.IO.Path.Combine(_pluginPath, assemblyName.Name + ".dll");
        assemblyPath = Path.GetFullPath(assemblyPath);
        if (System.IO.File.Exists(assemblyPath))
        {
            return LoadFromAssemblyPath(assemblyPath);
        }
        
        return null;
    }
    
    public Assembly LoadMyModule()
    {
        var path = Path.Combine(_pluginPath, "MyModule.dll");
        path = Path.GetFullPath(path);

        return LoadFromAssemblyPath(path);
    }

    public void Register(IContainerRegistry containerRegistry)
    {
        // 试验结果, 无法实现, .NET 不支持
        // var assembly = this.LoadMyModule();
        // var diType   = assembly.GetType("MyModule.DI");
        // var method   = diType.GetMethod("Register", BindingFlags.Public | BindingFlags.Instance);
        // method.Invoke(diType, [containerRegistry]);

        var assembly    = this.LoadMyModule();
        var userCtlType = assembly.GetType("MyModule.MyControl");
        // var inst = Activator.CreateInstance(userCtlType);
        containerRegistry.RegisterForNavigation(userCtlType, "MyControl");
    }

    public UserControl GetMyControl()
    {
        var assembly    = this.LoadMyModule();
        var userCtlType = assembly.GetType("MyModule.MyControl");
        var inst        = Activator.CreateInstance(userCtlType ?? throw new Exception());

        return inst as UserControl ?? throw new Exception();
    }
}
