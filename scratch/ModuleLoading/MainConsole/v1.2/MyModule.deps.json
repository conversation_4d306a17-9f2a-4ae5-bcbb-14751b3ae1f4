{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"MyModule/1.2.0": {"dependencies": {"Framework": "1.0.0", "MyLibrary": "1.2.0", "Prism.Unity": "9.0.537", "Prism.Wpf": "9.0.537"}, "runtime": {"MyModule.dll": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.1": {"runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.324.11423"}}}, "Microsoft.Xaml.Behaviors.Wpf/1.1.122": {"runtime": {"lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"assemblyVersion": "1.1.0.0", "fileVersion": "1.1.122.28819"}}}, "Prism.Container.Abstractions/9.0.106": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1"}, "runtime": {"lib/net8.0/Prism.Container.Abstractions.dll": {"assemblyVersion": "9.0.106.9543", "fileVersion": "9.0.106.9543"}}}, "Prism.Container.Unity/9.0.106": {"dependencies": {"Prism.Container.Abstractions": "9.0.106", "Unity.Container": "5.11.11"}, "runtime": {"lib/net8.0/Prism.Container.Unity.dll": {"assemblyVersion": "9.0.106.9543", "fileVersion": "9.0.106.9543"}}}, "Prism.Core/9.0.537": {"dependencies": {"Prism.Container.Abstractions": "9.0.106", "Prism.Events": "9.0.537"}, "runtime": {"lib/net6.0/Prism.dll": {"assemblyVersion": "9.0.537.60525", "fileVersion": "9.0.537.60525"}}}, "Prism.Events/9.0.537": {"runtime": {"lib/net6.0/Prism.Events.dll": {"assemblyVersion": "9.0.537.60525", "fileVersion": "9.0.537.60525"}}}, "Prism.Unity/9.0.537": {"dependencies": {"Prism.Container.Unity": "9.0.106", "Prism.Wpf": "9.0.537"}, "runtime": {"lib/net6.0-windows7.0/Prism.Unity.Wpf.dll": {"assemblyVersion": "9.0.537.60525", "fileVersion": "9.0.537.60525"}}}, "Prism.Wpf/9.0.537": {"dependencies": {"Microsoft.Xaml.Behaviors.Wpf": "1.1.122", "Prism.Core": "9.0.537"}, "runtime": {"lib/net6.0-windows7.0/Prism.Wpf.dll": {"assemblyVersion": "9.0.537.60525", "fileVersion": "9.0.537.60525"}}}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {}, "System.Threading.Tasks.Extensions/4.5.2": {}, "Unity.Abstractions/5.11.7": {"dependencies": {"System.Threading.Tasks.Extensions": "4.5.2"}, "runtime": {"lib/netcoreapp3.0/Unity.Abstractions.dll": {"assemblyVersion": "5.11.7.0", "fileVersion": "5.11.7.0"}}}, "Unity.Container/5.11.11": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.5.2", "Unity.Abstractions": "5.11.7"}, "runtime": {"lib/netcoreapp3.0/Unity.Container.dll": {"assemblyVersion": "5.11.11.0", "fileVersion": "5.11.11.0"}}}, "Framework/1.0.0": {"runtime": {"Framework.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "MyLibrary/1.2.0": {"runtime": {"MyLibrary.dll": {"assemblyVersion": "1.2.0.0", "fileVersion": "1.2.0.0"}}}}}, "libraries": {"MyModule/1.2.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-fGLiCRLMYd00JYpClraLjJTNKLmMJPnqxMaiRzEBIIvevlzxz33mXy39Lkd48hu1G+N21S7QpaO5ZzKsI6FRuA==", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.1", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.Xaml.Behaviors.Wpf/1.1.122": {"type": "package", "serviceable": true, "sha512": "sha512-SgcafT189u4qX++vSCV9FLQ4BsRXU9J2esnHA9IF8GOSgnPBulFw1CW4X/FYoOXvZwdDZxlSObJUGUg1U1wSyg==", "path": "microsoft.xaml.behaviors.wpf/1.1.122", "hashPath": "microsoft.xaml.behaviors.wpf.1.1.122.nupkg.sha512"}, "Prism.Container.Abstractions/9.0.106": {"type": "package", "serviceable": true, "sha512": "sha512-QNOERNOqsxvAa8pbWjqFB872DkvYK/cVRrcFO5vJYgWTIKBd8xfaI/jaZ0qeXLYVDz0nrvgJTZVVnip6+68dCw==", "path": "prism.container.abstractions/9.0.106", "hashPath": "prism.container.abstractions.9.0.106.nupkg.sha512"}, "Prism.Container.Unity/9.0.106": {"type": "package", "serviceable": true, "sha512": "sha512-QRakEz+1HG7PGETsEWQnHED4tmp7Ir/lVIVo0TySER1ACqNGNQgAfSgza+B/WMl/SadHhrz+HlTVQw3+PrAFWQ==", "path": "prism.container.unity/9.0.106", "hashPath": "prism.container.unity.9.0.106.nupkg.sha512"}, "Prism.Core/9.0.537": {"type": "package", "serviceable": true, "sha512": "sha512-D7mEqPKLVNrD0g2WHCpC/MOKwn8h7X1liCWyjqjL7NCuxgwuhVLTG85E/ZPBkISrXdwvOQZ+bSY31bvP79FQlg==", "path": "prism.core/9.0.537", "hashPath": "prism.core.9.0.537.nupkg.sha512"}, "Prism.Events/9.0.537": {"type": "package", "serviceable": true, "sha512": "sha512-Pzp5MGUuhAyKXZUbHVYNWLGF/eA3sScqDN6VrzbWlKj85R0IS0q+JXe99umynso2xhXAe+1jrQCCkgqmEFCBng==", "path": "prism.events/9.0.537", "hashPath": "prism.events.9.0.537.nupkg.sha512"}, "Prism.Unity/9.0.537": {"type": "package", "serviceable": true, "sha512": "sha512-F2RjW2QZg2TsQxuYsRB0ldoacQw2xuZmaMM1LENfR+qbxPxBXC887yZ+PKeP9eWPP2sP3oVUqo09N8EWJLZXng==", "path": "prism.unity/9.0.537", "hashPath": "prism.unity.9.0.537.nupkg.sha512"}, "Prism.Wpf/9.0.537": {"type": "package", "serviceable": true, "sha512": "sha512-srsXhi7FRUFawsNoRkY67duMEGjZo3ff0FpqpkjeWkkAuLazlH1UmNVrvwnpaLQCBboexH/z6oGrLvpeocxgdw==", "path": "prism.wpf/9.0.537", "hashPath": "prism.wpf.9.0.537.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/4.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-wprSFgext8cwqymChhrBLu62LMg/1u92bU+VOwyfBimSPVFXtsNqEWC92Pf9ofzJFlk4IHmJA75EDJn1b2goAQ==", "path": "system.runtime.compilerservices.unsafe/4.5.2", "hashPath": "system.runtime.compilerservices.unsafe.4.5.2.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.2": {"type": "package", "serviceable": true, "sha512": "sha512-BG/TNxDFv0svAzx8OiMXDlsHfGw623BZ8tCXw4YLhDFDvDhNUEV58jKYMGRnkbJNm7c3JNNJDiN7JBMzxRBR2w==", "path": "system.threading.tasks.extensions/4.5.2", "hashPath": "system.threading.tasks.extensions.4.5.2.nupkg.sha512"}, "Unity.Abstractions/5.11.7": {"type": "package", "serviceable": true, "sha512": "sha512-3ztwGEpe35UJlCUswXoi4uVDp8bJsgPsOmO71nZnNXh51II7t54AbezDbS6sR2z4QnMOpNGDaXbsEkyg6dIfOQ==", "path": "unity.abstractions/5.11.7", "hashPath": "unity.abstractions.5.11.7.nupkg.sha512"}, "Unity.Container/5.11.11": {"type": "package", "serviceable": true, "sha512": "sha512-47u4MBG8hxV2ZBUK7LlXcZQW8yWSqUSCRG+2/TBA2CSkxkQlMfVUJ0RJODJsZgsiSgy4N0M8HIr7J88drYR/OQ==", "path": "unity.container/5.11.11", "hashPath": "unity.container.5.11.11.nupkg.sha512"}, "Framework/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "MyLibrary/1.2.0": {"type": "project", "serviceable": false, "sha512": ""}}}