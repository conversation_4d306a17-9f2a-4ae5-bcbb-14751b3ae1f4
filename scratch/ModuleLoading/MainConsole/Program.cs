// See https://aka.ms/new-console-template for more information

using System.IO;
using MainConsole;

var a1 = Load(@".\v1.0");
var a2 = Load(@".\v1.2");

Run(a1, "nepton");
Run(a2, "zm");

// 释放程序集
a1.Unloading += context => { Console.WriteLine("Unloading"); };
a1.Unload();
a2.Unload();
a1.Dispose();

GC.Collect();
GC.WaitForPendingFinalizers();

Console.WriteLine("Press any key to exit.");
Console.ReadKey();

static PluginLoadContext Load(string pluginPath)
{
    var path = Path.GetFullPath(pluginPath);
    var alc  = new PluginLoadContext(path);

    return alc;
}

static void Run(PluginLoadContext alc, string name)
{
    var assembly = alc.LoadMyModule();

    Type type     = assembly.GetType("MyModule.Greeter")!;
    var  instance = Activator.CreateInstance(type)!;

    var method = type.GetMethod("Greet");
    var r      = method?.Invoke(instance, [name]);
    Console.WriteLine(r);
}
