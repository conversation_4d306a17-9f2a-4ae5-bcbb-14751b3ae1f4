using System.Reflection;
using System.Runtime.Loader;

namespace MainConsole;

public class PluginLoadContext : AssemblyLoadContext, IDisposable
{
    private readonly string _pluginPath;

    public PluginLoadContext(string pluginPath) : base(isCollectible: true) // 可回收
    {
        this._pluginPath = pluginPath;
    }

    protected override Assembly? Load(AssemblyName assemblyName)
    {
        string assemblyPath = System.IO.Path.Combine(_pluginPath, assemblyName.Name + ".dll");
        assemblyPath = Path.GetFullPath(assemblyPath);
        if (System.IO.File.Exists(assemblyPath))
        {
            return LoadFromAssemblyPath(assemblyPath);
        }

        return null;
    }

    public Assembly LoadMyModule()
    {
        var path = Path.Combine(_pluginPath, "MyModule.dll");
        path = Path.GetFullPath(path);

        return LoadFromAssemblyPath(path);
    }

    public void Dispose()
    {
        Unload();
        GC.SuppressFinalize(this);
        Console.WriteLine("PluginLoadContext.Dispose");
    }
}
