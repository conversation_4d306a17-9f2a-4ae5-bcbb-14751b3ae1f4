<UserControl x:Class="MyModule.MyControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:MyModule"
             mc:Ignorable="d"
             d:DataContext="{d:DesignInstance local:MyControlViewModel, IsDesignTimeCreatable=True}"
             d:DesignHeight="300" d:DesignWidth="300">
    <StackPanel>
        <Button Content="Click Me" HorizontalAlignment="Left" Margin="10,10,0,0" VerticalAlignment="Top" Width="75" Command="{Binding HelloWorldCommand }" />
        <Viewbox MaxWidth="600">
            <TextBlock>Hello MyModule v1.3</TextBlock>
        </Viewbox>
    </StackPanel>
</UserControl>