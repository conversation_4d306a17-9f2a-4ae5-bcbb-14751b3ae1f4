<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0-windows</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <UseWPF>true</UseWPF>
        <Nullable>enable</Nullable>

        <Version>1.3.0</Version>
        <AssemblyVersion>1.3.0.0</AssemblyVersion>
        <FileVersion>1.3.0.0</FileVersion>
        <Platforms>AnyCPU;x64</Platforms>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Prism.Unity" Version="9.0.537" />
        <PackageReference Include="Prism.Wpf" Version="9.0.537" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\Framework\Framework.csproj" />
        <ProjectReference Include="..\MyLibrary\MyLibrary.csproj" />
    </ItemGroup>

</Project>
