using System.Windows.Controls;

namespace MyModule;

public partial class MyControl : User<PERSON>ontrol, IDisposable
{
    public MyControl()
    {
        InitializeComponent();
        DataContext = new MyControlViewModel();
    }

    public void Dispose()
    {
        // 释放资源
        this.DataContext = null;
        if (this.Parent is Panel panel)
        {
            panel.Children.Remove(this);
        }
    }
}
