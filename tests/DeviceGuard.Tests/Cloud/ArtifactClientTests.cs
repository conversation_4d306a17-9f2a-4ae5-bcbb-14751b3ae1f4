using System;
using System.Threading;
using System.Threading.Tasks;
using DeviceGuard.Infrastructure.Cloud;
using DeviceGuard.Interface.Cloud;
using Moq;
using Xunit;

namespace DeviceGuard.Tests.Cloud;

/// <summary>
/// ArtifactClient 的单元测试
/// </summary>
public class ArtifactClientTests
{
    private readonly Mock<ILoginSession> _mockLoginSession;
    private readonly ArtifactClient      _artifactClient;

    public ArtifactClientTests()
    {
        _mockLoginSession = new Mock<ILoginSession>();
        _artifactClient   = new ArtifactClient(_mockLoginSession.Object);
    }

    [Fact]
    public void Constructor_WithValidLoginSession_ShouldCreateInstance()
    {
        // Arrange & Act
        var client = new ArtifactClient(_mockLoginSession.Object);

        // Assert
        Assert.NotNull(client);
    }

    [Fact]
    public void Constructor_WithNullLoginSession_ShouldThrowArgumentNullException()
    {
        // Arrange, Act & Assert
        Assert.Throws<ArgumentNullException>(() => new ArtifactClient(null!));
    }

    [Fact]
    public async Task GetLatestSoftwareVersionAsync_WithNullSoftwareCode_ShouldThrowArgumentException()
    {
        // Arrange
        string? softwareCode = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(async () =>
            await _artifactClient.GetLatestSoftwareVersionAsync(softwareCode!, CancellationToken.None));
    }

    [Fact]
    public async Task GetLatestSoftwareVersionAsync_WithEmptySoftwareCode_ShouldThrowArgumentException()
    {
        // Arrange
        var softwareCode = string.Empty;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(async () =>
            await _artifactClient.GetLatestSoftwareVersionAsync(softwareCode, CancellationToken.None));
    }

    [Fact]
    public async Task DownloadSoftwareAsync_WithNullSoftwareCode_ShouldThrowArgumentException()
    {
        // Arrange
        string? softwareCode = null;
        var version = "1.0.0";

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(async () =>
            await _artifactClient.DownloadSoftwareAsync(softwareCode!, version, CancellationToken.None));
    }

    [Fact]
    public async Task DownloadSoftwareAsync_WithEmptySoftwareCode_ShouldThrowArgumentException()
    {
        // Arrange
        var softwareCode = string.Empty;
        var version = "1.0.0";

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(async () =>
            await _artifactClient.DownloadSoftwareAsync(softwareCode, version, CancellationToken.None));
    }

    [Fact]
    public async Task DownloadSoftwareAsync_WithNullVersion_ShouldThrowArgumentException()
    {
        // Arrange
        var softwareCode = "test-software";
        string? version = null;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(async () =>
            await _artifactClient.DownloadSoftwareAsync(softwareCode, version!, CancellationToken.None));
    }

    [Fact]
    public async Task DownloadSoftwareAsync_WithEmptyVersion_ShouldThrowArgumentException()
    {
        // Arrange
        var softwareCode = "test-software";
        var version = string.Empty;

        // Act & Assert
        await Assert.ThrowsAsync<ArgumentException>(async () =>
            await _artifactClient.DownloadSoftwareAsync(softwareCode, version, CancellationToken.None));
    }
}
