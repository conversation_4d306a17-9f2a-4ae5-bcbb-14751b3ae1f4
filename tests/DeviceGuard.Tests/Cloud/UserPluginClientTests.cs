// using System;
// using System.IO;
// using System.Threading;
// using System.Threading.Tasks;
// using DeviceGuard.Infrastructure.Cloud;
// using DeviceGuard.Interface.Cloud;
// using DeviceGuard.Interface.FileSystem;
// using DeviceGuard.Interface.Licenses;
// using Microsoft.Extensions.Logging;
// using Moq;
// using Newtonsoft.Json;
// using Xunit;
//
// namespace DeviceGuard.Tests.Cloud;
//
// /// <summary>
// /// UserPluginClient 的单元测试
// /// </summary>
// public class UserPluginClientTests : IDisposable
// {
//     private readonly Mock<ILoginSession>             _mockLoginSession;
//     private readonly Mock<IAppFolderService>         _mockAppFolderService;
//     private readonly Mock<IPcSerialNumber>           _mockPcSerialNumber;
//     private readonly Mock<ILogger<UserPluginClient>> _mockLogger;
//     private readonly UserPluginClient                _userPluginClient;
//     private readonly string                          _testPluginDbPath;
//
//     public UserPluginClientTests()
//     {
//         _mockLoginSession     = new Mock<ILoginSession>();
//         _mockAppFolderService = new Mock<IAppFolderService>();
//         _mockPcSerialNumber   = new Mock<IPcSerialNumber>();
//         _mockLogger           = new Mock<ILogger<UserPluginClient>>();
//
//         // 创建临时测试目录
//         _testPluginDbPath = Path.Combine(Path.GetTempPath(), "DeviceGuardTests", Guid.NewGuid().ToString());
//         Directory.CreateDirectory(_testPluginDbPath);
//
//         _mockAppFolderService
//             .Setup(x => x.GetOrCreateFolder(It.IsAny<AppFolder>()))
//             .Returns(_testPluginDbPath);
//
//         _userPluginClient = new UserPluginClient(
//             _mockLoginSession.Object,
//             _mockAppFolderService.Object,
//             _mockPcSerialNumber.Object,
//             _mockLogger.Object);
//     }
//
//     [Fact]
//     public async Task GetMyOwnedPluginsAsync_WhenFileNotExists_ShouldReturnEmptyArray()
//     {
//         // Arrange
//         var machineSerialNumber = "TEST-SERIAL-123";
//
//         // Act
//         var result = await _userPluginClient.GetMyOwnedPluginsAsync(machineSerialNumber, CancellationToken.None);
//
//         // Assert
//         Assert.Empty(result);
//     }
//
//     [Fact]
//     public async Task GetMyOwnedPluginsAsync_WhenUserNotLoggedIn_ShouldReturnEmptyArray()
//     {
//         // Arrange
//         var machineSerialNumber = "TEST-SERIAL-123";
//         await CreateTestPluginOwnedFile();
//
//         _mockLoginSession.Setup(x => x.GetLoginUser()).Returns((LoginUserInfo?)null);
//
//         // Act
//         var result = await _userPluginClient.GetMyOwnedPluginsAsync(machineSerialNumber, CancellationToken.None);
//
//         // Assert
//         Assert.Empty(result);
//     }
//
//     [Fact]
//     public async Task GetMyOwnedPluginsAsync_WhenUserIdMismatch_ShouldReturnEmptyArray()
//     {
//         // Arrange
//         var machineSerialNumber = "TEST-SERIAL-123";
//         await CreateTestPluginOwnedFile();
//
//         var loginUser = new LoginUserInfo { Id = "different-user-id" };
//         _mockLoginSession.Setup(x => x.GetLoginUser()).Returns(loginUser);
//
//         // Act
//         var result = await _userPluginClient.GetMyOwnedPluginsAsync(machineSerialNumber, CancellationToken.None);
//
//         // Assert
//         Assert.Empty(result);
//     }
//
//     [Fact]
//     public async Task GetMyOwnedPluginsAsync_WhenSerialNumberMismatch_ShouldReturnEmptyArray()
//     {
//         // Arrange
//         var machineSerialNumber = "TEST-SERIAL-123";
//         await CreateTestPluginOwnedFile();
//
//         var loginUser = new LoginUserInfo { Id = "test-user-id" };
//         _mockLoginSession.Setup(x => x.GetLoginUser()).Returns(loginUser);
//         _mockPcSerialNumber.Setup(x => x.GetSerialNumber()).Returns("DIFFERENT-SERIAL");
//
//         // Act
//         var result = await _userPluginClient.GetMyOwnedPluginsAsync(machineSerialNumber, CancellationToken.None);
//
//         // Assert
//         Assert.Empty(result);
//     }
//
//     [Fact]
//     public async Task GetMyOwnedPluginsAsync_WhenValidData_ShouldReturnPluginCodes()
//     {
//         // Arrange
//         var machineSerialNumber = "TEST-SERIAL-123";
//         await CreateTestPluginOwnedFile();
//
//         var loginUser = new LoginUserInfo { Id = "test-user-id" };
//         _mockLoginSession.Setup(x => x.GetLoginUser()).Returns(loginUser);
//         _mockPcSerialNumber.Setup(x => x.GetSerialNumber()).Returns("TEST-SERIAL-123");
//
//         // Act
//         var result = await _userPluginClient.GetMyOwnedPluginsAsync(machineSerialNumber, CancellationToken.None);
//
//         // Assert
//         Assert.Single(result);
//         Assert.Equal("dgwin.power.infy.heg75050f3", result[0]);
//     }
//
//     [Fact]
//     public async Task GetMyOwnedPluginsAsync_WhenPluginExpired_ShouldNotReturnExpiredPlugin()
//     {
//         // Arrange
//         var machineSerialNumber = "TEST-SERIAL-123";
//         await CreateTestPluginOwnedFileWithExpiredPlugin();
//
//         var loginUser = new LoginUserInfo { Id = "test-user-id" };
//         _mockLoginSession.Setup(x => x.GetLoginUser()).Returns(loginUser);
//         _mockPcSerialNumber.Setup(x => x.GetSerialNumber()).Returns("TEST-SERIAL-123");
//
//         // Act
//         var result = await _userPluginClient.GetMyOwnedPluginsAsync(machineSerialNumber, CancellationToken.None);
//
//         // Assert
//         Assert.Empty(result);
//     }
//
//     private async Task CreateTestPluginOwnedFile()
//     {
//         var response = new UserOwnedPluginsResponse
//         {
//             Data = new UserOwnedPluginsData
//             {
//                 MachineSerialNumber = "TEST-SERIAL-123",
//                 UserId              = "test-user-id",
//                 UserName            = "testuser",
//                 Plugins = new[]
//                 {
//                     new UserOwnedPlugin
//                     {
//                         PluginCode = "dgwin.power.infy.heg75050f3",
//                         ValidUntil = DateTime.UtcNow.AddDays(30)
//                     }
//                 },
//                 QueryTimestamp = DateTime.UtcNow
//             }
//         };
//
//         var json     = JsonConvert.SerializeObject(response, Formatting.Indented);
//         var filePath = Path.Combine(_testPluginDbPath, "plugin-owned.json");
//         await File.WriteAllTextAsync(filePath, json);
//     }
//
//     private async Task CreateTestPluginOwnedFileWithExpiredPlugin()
//     {
//         var response = new UserOwnedPluginsResponse
//         {
//             Data = new UserOwnedPluginsData
//             {
//                 MachineSerialNumber = "TEST-SERIAL-123",
//                 UserId              = "test-user-id",
//                 UserName            = "testuser",
//                 Plugins = new[]
//                 {
//                     new UserOwnedPlugin
//                     {
//                         PluginCode = "dgwin.power.infy.heg75050f3",
//                         ValidUntil = DateTime.UtcNow.AddDays(-1) // 已过期
//                     }
//                 },
//                 QueryTimestamp = DateTime.UtcNow
//             }
//         };
//
//         var json     = JsonConvert.SerializeObject(response, Formatting.Indented);
//         var filePath = Path.Combine(_testPluginDbPath, "plugin-owned.json");
//         await File.WriteAllTextAsync(filePath, json);
//     }
//
//     public void Dispose()
//     {
//         // 清理测试目录
//         if (Directory.Exists(_testPluginDbPath))
//         {
//             Directory.Delete(_testPluginDbPath, true);
//         }
//     }
// }
