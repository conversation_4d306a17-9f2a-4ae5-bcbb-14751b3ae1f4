using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using DeviceGuard.Infrastructure.Updates;
using DeviceGuard.Interface.Cloud;
using DeviceGuard.Interface.FileSystem;
using Moq;
using Semver;
using Serilog;
using Xunit;

namespace DeviceGuard.Tests.Updates;

/// <summary>
/// 可测试的 UpdateManager，允许覆盖 GetCurrentVersion 方法
/// </summary>
public class TestableUpdateManager : UpdateManager
{
    private readonly Semver.SemVersion? _testCurrentVersion;

    public TestableUpdateManager(
        IArtifactClient artifactClient,
        IAppFolderService appFolderService,
        ILogger logger,
        Semver.SemVersion? testCurrentVersion = null)
        : base(artifactClient, appFolderService, logger)
    {
        _testCurrentVersion = testCurrentVersion;
    }

    protected override Semver.SemVersion? GetCurrentVersion()
    {
        return _testCurrentVersion ?? base.GetCurrentVersion();
    }
}

/// <summary>
/// UpdateManager 的单元测试
/// </summary>
public class UpdateManagerTests
{
    private readonly Mock<IArtifactClient> _mockArtifactClient;
    private readonly Mock<IAppFolderService> _mockAppFolderService;
    private readonly Mock<ILogger> _mockLogger;
    private readonly TestableUpdateManager _updateManager;
    private readonly string _testUpdatesFolder;

    public UpdateManagerTests()
    {
        _mockArtifactClient = new Mock<IArtifactClient>();
        _mockAppFolderService = new Mock<IAppFolderService>();
        _mockLogger = new Mock<ILogger>();
        
        // 创建临时测试文件夹
        _testUpdatesFolder = Path.Combine(Path.GetTempPath(), Guid.NewGuid().ToString());
        Directory.CreateDirectory(_testUpdatesFolder);
        
        _mockAppFolderService
            .Setup(x => x.GetOrCreateFolder(AppFolder.Updates))
            .Returns(_testUpdatesFolder);

        _updateManager = new TestableUpdateManager(
            _mockArtifactClient.Object,
            _mockAppFolderService.Object,
            _mockLogger.Object,
            SemVersion.Parse("1.0.0"));
    }

    [Fact]
    public void Constructor_WithValidParameters_ShouldCreateInstance()
    {
        // Arrange & Act
        var manager = new UpdateManager(
            _mockArtifactClient.Object,
            _mockAppFolderService.Object,
            _mockLogger.Object);

        // Assert
        Assert.NotNull(manager);
    }

    [Fact]
    public void Constructor_WithNullArtifactClient_ShouldThrowArgumentNullException()
    {
        // Arrange, Act & Assert
        Assert.Throws<ArgumentNullException>(() => new UpdateManager(
            null!,
            _mockAppFolderService.Object,
            _mockLogger.Object));
    }

    [Fact]
    public void Constructor_WithNullAppFolderService_ShouldThrowArgumentNullException()
    {
        // Arrange, Act & Assert
        Assert.Throws<ArgumentNullException>(() => new UpdateManager(
            _mockArtifactClient.Object,
            null!,
            _mockLogger.Object));
    }

    [Fact]
    public void Constructor_WithNullLogger_ShouldThrowArgumentNullException()
    {
        // Arrange, Act & Assert
        Assert.Throws<ArgumentNullException>(() => new UpdateManager(
            _mockArtifactClient.Object,
            _mockAppFolderService.Object,
            null!));
    }

    [Fact]
    public async Task DownloadUpdatesAsync_WithNewerVersion_ShouldDownloadUpdate()
    {
        // Arrange
        var latestVersion = "2.0.0"; // 使用一个明显更新的版本
        var updateData = new byte[] { 1, 2, 3, 4, 5 };

        _mockArtifactClient
            .Setup(x => x.GetLatestSoftwareVersionAsync("dgWin", It.IsAny<CancellationToken>()))
            .ReturnsAsync(latestVersion);

        _mockArtifactClient
            .Setup(x => x.DownloadSoftwareAsync("dgWin", latestVersion, It.IsAny<CancellationToken>()))
            .ReturnsAsync(updateData);

        // Act
        await _updateManager.DownloadUpdatesAsync(CancellationToken.None);

        // Assert
        var updateFilePath = Path.Combine(_testUpdatesFolder, "DeviceGuard-Update.exe");
        var versionFilePath = Path.Combine(_testUpdatesFolder, "version.txt");

        // 验证调用了下载方法
        _mockArtifactClient.Verify(
            x => x.DownloadSoftwareAsync("dgWin", latestVersion, It.IsAny<CancellationToken>()),
            Times.Once);

        Assert.True(File.Exists(updateFilePath), $"Update file should exist at: {updateFilePath}");
        Assert.True(File.Exists(versionFilePath), $"Version file should exist at: {versionFilePath}");

        var downloadedData = await File.ReadAllBytesAsync(updateFilePath);
        var savedVersion = await File.ReadAllTextAsync(versionFilePath);

        Assert.Equal(updateData, downloadedData);
        Assert.Equal(latestVersion, savedVersion);
    }

    [Fact]
    public async Task DownloadUpdatesAsync_WithSameVersion_ShouldNotDownload()
    {
        // Arrange
        var latestVersion = "0.1.0"; // 使用一个明显更旧的版本

        _mockArtifactClient
            .Setup(x => x.GetLatestSoftwareVersionAsync("dgWin", It.IsAny<CancellationToken>()))
            .ReturnsAsync(latestVersion);

        // Act
        await _updateManager.DownloadUpdatesAsync(CancellationToken.None);

        // Assert
        var updateFilePath = Path.Combine(_testUpdatesFolder, "DeviceGuard-Update.exe");
        Assert.False(File.Exists(updateFilePath));

        // 验证没有调用下载方法
        _mockArtifactClient.Verify(
            x => x.DownloadSoftwareAsync(It.IsAny<string>(), It.IsAny<string>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact]
    public async Task GetUpdatePendingVersionAsync_WithValidUpdateFile_ShouldReturnVersion()
    {
        // Arrange
        var updateVersion = "99.0.0"; // 使用一个明显更新的版本
        var updateFilePath = Path.Combine(_testUpdatesFolder, "DeviceGuard-Update.exe");
        var versionFilePath = Path.Combine(_testUpdatesFolder, "version.txt");

        // 创建测试文件
        await File.WriteAllBytesAsync(updateFilePath, new byte[] { 1, 2, 3 });
        await File.WriteAllTextAsync(versionFilePath, updateVersion);

        // Act
        var result = await _updateManager.GetUpdatePendingVersionAsync(CancellationToken.None);

        // Assert
        Assert.Equal(updateVersion, result);
    }

    [Fact]
    public async Task GetUpdatePendingVersionAsync_WithoutUpdateFile_ShouldReturnNull()
    {
        // Arrange
        // 不创建任何文件

        // Act
        var result = await _updateManager.GetUpdatePendingVersionAsync(CancellationToken.None);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetUpdatePendingVersionAsync_WithEmptyUpdateFile_ShouldReturnNull()
    {
        // Arrange
        var updateVersion = "2.0.0";
        var updateFilePath = Path.Combine(_testUpdatesFolder, "DeviceGuard-Update.exe");
        var versionFilePath = Path.Combine(_testUpdatesFolder, "version.txt");

        // 创建空的更新文件
        await File.WriteAllBytesAsync(updateFilePath, Array.Empty<byte>());
        await File.WriteAllTextAsync(versionFilePath, updateVersion);

        // Act
        var result = await _updateManager.GetUpdatePendingVersionAsync(CancellationToken.None);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetUpdatePendingVersionAsync_WithInvalidVersionFile_ShouldReturnNull()
    {
        // Arrange
        var updateFilePath = Path.Combine(_testUpdatesFolder, "DeviceGuard-Update.exe");
        var versionFilePath = Path.Combine(_testUpdatesFolder, "version.txt");

        // 创建有效的更新文件但无效的版本文件
        await File.WriteAllBytesAsync(updateFilePath, new byte[] { 1, 2, 3 });
        await File.WriteAllTextAsync(versionFilePath, "invalid-version");

        // Act
        var result = await _updateManager.GetUpdatePendingVersionAsync(CancellationToken.None);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetUpdatePendingVersionAsync_WithOlderVersion_ShouldReturnNull()
    {
        // Arrange
        var updateVersion = "0.9.0"; // 比当前版本 1.0.0 更旧
        var updateFilePath = Path.Combine(_testUpdatesFolder, "DeviceGuard-Update.exe");
        var versionFilePath = Path.Combine(_testUpdatesFolder, "version.txt");

        // 创建测试文件
        await File.WriteAllBytesAsync(updateFilePath, new byte[] { 1, 2, 3 });
        await File.WriteAllTextAsync(versionFilePath, updateVersion);

        // Act
        var result = await _updateManager.GetUpdatePendingVersionAsync(CancellationToken.None);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task GetUpdatePendingVersionAsync_WithSameVersion_ShouldReturnNull()
    {
        // Arrange
        var updateVersion = "1.0.0"; // 与当前版本相同
        var updateFilePath = Path.Combine(_testUpdatesFolder, "DeviceGuard-Update.exe");
        var versionFilePath = Path.Combine(_testUpdatesFolder, "version.txt");

        // 创建测试文件
        await File.WriteAllBytesAsync(updateFilePath, new byte[] { 1, 2, 3 });
        await File.WriteAllTextAsync(versionFilePath, updateVersion);

        // Act
        var result = await _updateManager.GetUpdatePendingVersionAsync(CancellationToken.None);

        // Assert
        Assert.Null(result);
    }

    [Fact]
    public async Task InstallUpdatesAsync_WithoutReadyUpdate_ShouldNotInstall()
    {
        // Arrange
        // 不创建任何更新文件

        // Act
        await _updateManager.InstallUpdatesAsync(CancellationToken.None);

        // Assert
        // 验证日志记录了警告
        _mockLogger.Verify(
            x => x.Warning(It.Is<string>(s => s.Contains("没有可安装的更新"))),
            Times.Once);
    }

    /// <summary>
    /// 清理测试资源
    /// </summary>
    [Fact]
    public void Dispose_ShouldCleanupTestResources()
    {
        // This test method is just to satisfy xUnit analyzer warning
        // The actual cleanup is done in the real Dispose method below
        Assert.True(true);
    }

    [Fact]
    public void VersionComparison_ShouldWorkCorrectly()
    {
        // Test version comparison logic
        var currentVersion = SemVersion.Parse("1.0.0");
        var newerVersion = SemVersion.Parse("2.0.0");
        var olderVersion = SemVersion.Parse("0.9.0");

        Assert.True(currentVersion.ComparePrecedenceTo(newerVersion) < 0, "Current should be less than newer");
        Assert.True(currentVersion.ComparePrecedenceTo(olderVersion) > 0, "Current should be greater than older");
        Assert.True(currentVersion.ComparePrecedenceTo(currentVersion) == 0, "Current should equal itself");
    }

    /// <summary>
    /// 清理测试资源
    /// </summary>
    private void CleanupTestResources()
    {
        try
        {
            if (Directory.Exists(_testUpdatesFolder))
            {
                Directory.Delete(_testUpdatesFolder, true);
            }
        }
        catch
        {
            // 忽略清理错误
        }
    }
}
